﻿using Libraries.CommonEnums;

namespace Libraries.PerspectiveColumns
{
    public class PerspectiveColumnModel
    {
        public string Name { get; set; }
        public string DisplayName { get; set; }
        public string Attribute { get; set; }
        public bool IsMeasure { get; set; }
        public string AggregationType
        {
            get
            {
                return PerspectiveMeasure.ToString();
            }
        }
        public PerspectiveMeasure PerspectiveMeasure { get; set; }
        public bool IsDimension { get; set; }
        public bool NotForReports { get; set; }
        public bool IsPivot { get; set; }
        public bool IsOtherMeasure { get; set; }
    }
}
