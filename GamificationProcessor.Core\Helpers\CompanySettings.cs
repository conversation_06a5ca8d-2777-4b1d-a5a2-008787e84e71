﻿using Libraries.CommonEnums;
using System;
using System.Collections.Generic;
using Library.StringHelpers.Extensions;

namespace GamificationProcessor.Core.Helpers
{


    public class CompanySettings
    {
        private readonly Dictionary<string, object> settings;


        private CompanySettings(Dictionary<string, object> settings)
        {
            this.settings = settings;
        }

        public static CompanySettings Initialize(Dictionary<string, object> settings)
        {
            return new CompanySettings(settings);
        }

        public int YearStartMonth
        {
            get
            {
                try
                {
                    if (settings.ContainsKey("yearStartMonth"))
                    {
                        return (int)(long)settings["yearStartMonth"];
                    }
                    else
                        return 4;
                }
                catch (Exception)
                {
                    return 4;
                }
            }
        }
        public int DayLimitforOrderDispatch
        {
            get
            {
                try
                {
                    if (settings.ContainsKey("DayLimitforOrderDispatch"))
                    {
                        return (int)(long)settings["DayLimitforOrderDispatch"];
                    }
                    else
                        return 0;
                }
                catch (Exception)
                {
                    return 0;
                }
            }
        }
        public TimeSpan TimeZoneOffset
        {
            get
            {
                try
                {
                    if (settings.ContainsKey("TimeZoneOffset"))
                    {
                        return TimeSpan.FromMinutes((int)(long)settings["TimeZoneOffset"]);
                    }
                    else
                        return TimeSpan.FromMinutes(330);
                }
                catch (Exception)
                {
                    return TimeSpan.FromMinutes(330);
                }
            }
        }

        public JourneyPlanType GetJourneyPlanType
        {
            get
            {
                try
                {

                    if (settings.ContainsKey("JourneyPlanType"))
                    {
                        var journeyPlanType = (string)settings["JourneyPlanType"];
                        return journeyPlanType.TestNullAssign(JourneyPlanType.Default);
                    }
                    else
                    {
                        return JourneyPlanType.Default;
                    }
                }
                catch (Exception)
                {
                    return JourneyPlanType.Default;
                }

            }
        }

    }


}
