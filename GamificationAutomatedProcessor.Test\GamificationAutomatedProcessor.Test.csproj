<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netcoreapp3.1</TargetFramework>

    <IsPackable>false</IsPackable>
  </PropertyGroup>

  <ItemGroup>
	  <PackageReference Include="Microsoft.Azure.KeyVault" Version="3.0.3" />
	  <PackageReference Include="Microsoft.Azure.Services.AppAuthentication" Version="1.0.3" />
	  <PackageReference Include="Microsoft.EntityFrameworkcore.SqlServer" Version="2.0.2" />
	  <PackageReference Include="Microsoft.Extensions.Configuration.AzureKeyVault" Version="2.2.0" />
	  <PackageReference Include="Microsoft.NET.Test.Sdk" Version="15.5.0" />
	  <PackageReference Include="MSTest.TestAdapter" Version="1.2.0" />
	  <PackageReference Include="MSTest.TestFramework" Version="1.2.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\GamificationAutomatedProcessor.Core\GamificationAutomatedProcessor.Core.csproj" />
    <ProjectReference Include="..\GamificationAutomatedProcessor.DbStorage\GamificationAutomatedProcessor.DbStorage.csproj" />
    <ProjectReference Include="..\GamificationAutomatedProcessor\GamificationAutomatedProcessor.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="appsettings.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
