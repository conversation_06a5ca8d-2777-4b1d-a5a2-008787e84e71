﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Libraries.Cryptography
{
    public class TokenHelper
    {
        private const UInt32 salt1 = 89854;
        private const UInt32 salt2 = 32784;

        private const int rotateDigits = 3;
        public static UInt32 GetToken(long connId)
        {
            var t = RotateLeft(salt1 ^ (uint)connId);
            return RotateLeft(salt2 ^ t);

        }

        public static long GetConnectionId(UInt32 token)
        {
            var t = RotateRight(token) ^ salt2;
            return (long)(RotateRight(t) ^ salt1);

        }


        private static UInt32 RotateLeft(UInt32 v)
        {
            return (v << rotateDigits) | (v >> (32 - rotateDigits));
        }

        private static UInt32 RotateRight(UInt32 v)
        {
            return (v >> rotateDigits) | (v << (32 - rotateDigits));
        }
    }
}
