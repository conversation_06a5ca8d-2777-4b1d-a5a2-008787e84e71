﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace Libraries.CommonEnums
{
    public enum ImageContainers
    {
        [Display(Name = "outletimages")]
        OutletImages,
        [Display(Name = "surveyimages")]
        SurveyImages,
        [Display(Name = "paymentimages")]
        PaymentImages,
        [Display(Name = "invoiceimages")]
        InvoiceImages,
        [Display(Name = "daystartimage")]
        daystartimage,
        [Display(Name = "ocrimages")]
        ocrimage
    }
}
