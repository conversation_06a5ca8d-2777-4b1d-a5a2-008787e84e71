﻿namespace Library.ConnectionStringParsor
{
    public class ApiConnectionString : ConnectionStringBase
    {
        public string BaseUrl { get; private set; }
        public string AuthToken { get; private set; }
        public static ApiConnectionString GetConnection(string connString)
        {
            return new ApiConnectionString(connString);
        }
        private ApiConnectionString(string connectionString) : base(connectionString)
        {
            var parsedString = base.Parse();
            if (parsedString.ContainsKey("baseurl"))
            {
                BaseUrl = parsedString["baseurl"];
            }
            else
            {
                throw new System.Exception("MalFormed Connection String: 'accountendpoint' is compulsory");
            }

            if (parsedString.ContainsKey("authtoken"))
            {
                AuthToken = parsedString["authtoken"];
            }
            else
            {
                throw new System.Exception("MalFormed Connection String: 'authtoken' is compulsory");
            }
        }
    }
}
