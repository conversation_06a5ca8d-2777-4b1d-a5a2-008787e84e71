﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace Library.CommonHelpers
{
    public class ManagerAppPointers
    {
        public ManagerAppPointers(PointerState pointerStateForUsers, PointerState pointerStateForManagers = PointerState.Unknown)
        {
            this.PointerStateForUsers = pointerStateForUsers;
            this.PointerStateForManagers = (pointerStateForManagers == PointerState.Unknown) ? pointerStateForUsers : pointerStateForManagers;
        }
        public DailyDataPointers Name { get; set; }
        public string DisplayName { get; set; }
        public bool IsForManager { get; set; }
        public bool IsForUser { get; set; }
        public PointerState PointerStateForUsers { get; set; }
        public PointerState PointerStateForManagers { get; set; }
        public ColumnType ColumnType { get; set; }
        public int ManagerOrder { get; set; }
        public int UserOrder { get; set; }

    }
    public enum PointerState
    {
        Unknown = 0,
        Fixed = 1,
        Default = 2,
        Other = 4,
        CheckedForManagers = 8,
        CheckedForUsers = 16
    }
    public enum ColumnType
    {
        Master,
        Measure,
        Dimension,
        Fact
    }
    public class ManagerAppDataPointer
    {
        public List<ManagerAppPointers> Pointers
        {
            get
            {
                return new List<ManagerAppPointers>
                {
                    new ManagerAppPointers(PointerState.Fixed) { DisplayName = "Total Users", Name = DailyDataPointers.TotalUsers, IsForUser = false, IsForManager = true,ColumnType= ColumnType.Master,ManagerOrder=1},
                    new ManagerAppPointers(PointerState.Fixed) { DisplayName = "Retailing", Name = DailyDataPointers.Retailing, IsForUser = false, IsForManager = true,ColumnType = ColumnType.Measure,ManagerOrder=2},
                    new ManagerAppPointers(PointerState.Fixed) { DisplayName = "Official Work", Name = DailyDataPointers.OfficialWork, IsForUser = false, IsForManager = true,ColumnType = ColumnType.Measure,ManagerOrder=3},
                    new ManagerAppPointers(PointerState.Fixed) { DisplayName = "Leave", Name = DailyDataPointers.Leave, IsForUser = false, IsForManager = true,ColumnType = ColumnType.Measure,ManagerOrder=4},
                    new ManagerAppPointers(PointerState.Fixed) { DisplayName = "Planned Leave", Name = DailyDataPointers.PlannedLeave, IsForUser = false, IsForManager = true,ColumnType = ColumnType.Measure,ManagerOrder=100},
                    new ManagerAppPointers(PointerState.Fixed) { DisplayName = "Absent", Name = DailyDataPointers.Absent, IsForUser = false, IsForManager = true,ColumnType = ColumnType.Measure,ManagerOrder=5,UserOrder=1},
                    new ManagerAppPointers(PointerState.Fixed) { DisplayName = "Reporting Manager", Name = DailyDataPointers.ReportingManager, IsForUser = true,  IsForManager = true,ColumnType = ColumnType.Master,ManagerOrder=101,UserOrder=100},
                    new ManagerAppPointers(PointerState.Fixed) { DisplayName = "Field User", Name = DailyDataPointers.FieldUserName, IsForUser = true, IsForManager = true,ColumnType = ColumnType.Master,ManagerOrder=102,UserOrder=101},
                    new ManagerAppPointers(PointerState.Fixed) { DisplayName = "Field User HQ", Name = DailyDataPointers.FieldUserHQ, IsForUser = true, IsForManager = true,ColumnType = ColumnType.Master,ManagerOrder=103,UserOrder=102},
                    new ManagerAppPointers(PointerState.Fixed) { DisplayName = "Rank", Name = DailyDataPointers.FieldUserRole, IsForUser = true, IsForManager = true,ColumnType = ColumnType.Master,ManagerOrder=104,UserOrder=102},
                    new ManagerAppPointers(PointerState.Fixed) { DisplayName = "Assigned Type", Name = DailyDataPointers.AssignedReasonCategory, IsForUser = true, IsForManager = false,ColumnType = ColumnType.Fact,UserOrder=103},
                    new ManagerAppPointers(PointerState.Fixed) { DisplayName = "Type", Name = DailyDataPointers.ReasonCategory, IsForUser = true, IsForManager = false,ColumnType = ColumnType.Fact,UserOrder=104},
                    new ManagerAppPointers(PointerState.Fixed) { DisplayName = "Assigned Reason", Name = DailyDataPointers.AssignedReason, IsForUser = true, IsForManager = false,ColumnType = ColumnType.Fact,UserOrder=105},
                    new ManagerAppPointers(PointerState.Fixed) { DisplayName = "Reason", Name = DailyDataPointers.Reason, IsForUser = true, IsForManager = false,ColumnType = ColumnType.Fact,UserOrder=106},
                    new ManagerAppPointers(PointerState.Fixed) { DisplayName = "Assigned JW User", Name = DailyDataPointers.AssignedJointWorkingEmployee, IsForUser = true, IsForManager = false,ColumnType = ColumnType.Fact,UserOrder=107},
                    new ManagerAppPointers(PointerState.Fixed) { DisplayName = "Selected JW User", Name = DailyDataPointers.JointWorkingEmployee, IsForUser = true, IsForManager = false,ColumnType = ColumnType.Fact,UserOrder=108},
                    new ManagerAppPointers(PointerState.Fixed) { DisplayName = "Assigned Beat" , Name =DailyDataPointers.AssignedBeat, IsForUser = true, IsForManager = false,ColumnType = ColumnType.Fact,UserOrder=109},
                    new ManagerAppPointers(PointerState.Fixed) { DisplayName = "Selected Beat" , Name = DailyDataPointers.SelectedBeat, IsForUser = true, IsForManager = false,ColumnType = ColumnType.Fact,UserOrder=110},
                    new ManagerAppPointers(PointerState.Fixed) { DisplayName = "Assigned Route", Name = DailyDataPointers.AssignedRoute, IsForUser = true, IsForManager = false,ColumnType = ColumnType.Fact,UserOrder=111},
                    new ManagerAppPointers(PointerState.Fixed) { DisplayName = "Selected Route", Name = DailyDataPointers.SelectedRoute, IsForUser = true, IsForManager = false,ColumnType = ColumnType.Fact,UserOrder=112},
                    new ManagerAppPointers(PointerState.Fixed) { DisplayName = "Distributor", Name = DailyDataPointers.Distributor, IsForUser = true, IsForManager = false,ColumnType = ColumnType.Fact,UserOrder=113},
                    new ManagerAppPointers(PointerState.Fixed) { DisplayName = "Day Start Location", Name = DailyDataPointers.DayStartLocation, IsForUser = true, IsForManager = false,ColumnType = ColumnType.Fact,UserOrder=114},
                    new ManagerAppPointers(PointerState.Default, PointerState.Other) { DisplayName = "Login", Name = DailyDataPointers.Login, IsForUser = true, IsForManager = false,ColumnType = ColumnType.Measure,UserOrder=1},
                    new ManagerAppPointers(PointerState.Default, PointerState.Other) { DisplayName = "First Call", Name = DailyDataPointers.FirstCallTime, IsForUser = true, IsForManager = false,ColumnType = ColumnType.Measure,UserOrder=2},
                    new ManagerAppPointers(PointerState.Default, PointerState.Other) { DisplayName = "First PC", Name = DailyDataPointers.FirstPCTime, IsForUser = true, IsForManager = false,ColumnType = ColumnType.Measure,UserOrder=3},
                    new ManagerAppPointers(PointerState.Default, PointerState.Other) { DisplayName = "SC", Name = DailyDataPointers.SC, IsForUser = true, IsForManager = true,ColumnType = ColumnType.Measure,ManagerOrder=6,UserOrder=4},
                    new ManagerAppPointers(PointerState.Default) { DisplayName = "TC", Name = DailyDataPointers.TC, IsForUser = true, IsForManager = true,ColumnType = ColumnType.Measure,ManagerOrder=7,UserOrder=6},
                    new ManagerAppPointers(PointerState.Default) { DisplayName = "CAP", Name = DailyDataPointers.CAP, IsForUser = true, IsForManager = true,ColumnType = ColumnType.Measure,ManagerOrder=8,UserOrder=5},
                    new ManagerAppPointers(PointerState.Default) { DisplayName = "PC", Name = DailyDataPointers.PC, IsForUser = true, IsForManager = true,ColumnType = ColumnType.Measure,ManagerOrder=9 ,UserOrder=7},
                    new ManagerAppPointers(PointerState.Default) { DisplayName = "Productivity(%)", Name = DailyDataPointers.Productivity, IsForUser = true, IsForManager = true,ColumnType = ColumnType.Measure,ManagerOrder=10 ,UserOrder=8},
                    new ManagerAppPointers(PointerState.Default) { DisplayName = "Sch.Productivity(%)", Name = DailyDataPointers.SchProductivity, IsForUser = true, IsForManager = true,ColumnType = ColumnType.Measure,ManagerOrder=11 ,UserOrder=9},
                    new ManagerAppPointers(PointerState.Other) { DisplayName = "OVT (%)", Name = DailyDataPointers.OVT, IsForUser = true, IsForManager = true,ColumnType = ColumnType.Measure,ManagerOrder=15,UserOrder=13},
                    new ManagerAppPointers(PointerState.Default, PointerState.Other) { DisplayName = "OVC (%)", Name = DailyDataPointers.OVC, IsForUser = true, IsForManager = true,ColumnType = ColumnType.Measure,ManagerOrder=14,UserOrder=12},
                    new ManagerAppPointers(PointerState.Other) { DisplayName = "TO", Name = DailyDataPointers.TelephonicOrders, IsForUser = true, IsForManager = true,ColumnType = ColumnType.Measure,ManagerOrder=19,UserOrder=18},
                    new ManagerAppPointers(PointerState.Other) { DisplayName = "JW Calls", Name = DailyDataPointers.JointWorkingCalls, IsForUser = true, IsForManager = true,ColumnType = ColumnType.Measure,ManagerOrder=17,UserOrder=16},
                    new ManagerAppPointers(PointerState.Default, PointerState.Other) { DisplayName = "Qty (Std Unit)", Name = DailyDataPointers.OrderInStdUnits, IsForUser = true, IsForManager = true,ColumnType = ColumnType.Measure,ManagerOrder=13,UserOrder=11},
                    new ManagerAppPointers(PointerState.Other) { DisplayName = "FOC", Name = DailyDataPointers.TotalSchemeQty, IsForUser = true, IsForManager = true,ColumnType = ColumnType.Measure,ManagerOrder=20,UserOrder=19},
                    new ManagerAppPointers(PointerState.Default) { DisplayName = "Net Value", Name = DailyDataPointers.NetValue, IsForUser = true, IsForManager = true,ColumnType = ColumnType.Measure,ManagerOrder=12,UserOrder=10},
                    new ManagerAppPointers(PointerState.Other) { DisplayName = "New Outlets", Name = DailyDataPointers.NewOutletsCreated, IsForUser = true, IsForManager = true,ColumnType = ColumnType.Measure,ManagerOrder=15,UserOrder=14},
                    new ManagerAppPointers(PointerState.Other) { DisplayName = "No Of Other Activities", Name = DailyDataPointers.NoOfOtherActivities, IsForUser = true, IsForManager = true,ColumnType = ColumnType.Measure,ManagerOrder=16,UserOrder=15},
                    new ManagerAppPointers(PointerState.Fixed) { DisplayName = "MTDValue", Name = DailyDataPointers.MTDvalue, IsForUser = true, IsForManager = true,ColumnType = ColumnType.Measure,ManagerOrder=105,UserOrder=1},
                    //new ManagerAppPointers() { DisplayName = "MTDUnit", Name = "MTD unit", IsForUser = true, IsForManager = true, PointerType = PointerType.Fixed},
                    new ManagerAppPointers(PointerState.Other) { DisplayName = "Physical TC", Name = DailyDataPointers.PhycialCalls, IsForUser = true, IsForManager = true,ColumnType = ColumnType.Measure,ManagerOrder=40,UserOrder=18},
                    new ManagerAppPointers(PointerState.Default) { DisplayName = "New Outlet Value", Name = DailyDataPointers.NewOutletSalesInRevenue, IsForUser = false, IsForManager = true,ColumnType = ColumnType.Measure,ManagerOrder=106},
                    new ManagerAppPointers(PointerState.Other) {DisplayName ="Selected Journey Outlet", Name = DailyDataPointers.SelectedJourneyOutlet, IsForUser = true, IsForManager = true, ColumnType = ColumnType.Measure, ManagerOrder = 17, UserOrder = 16 },
                    new ManagerAppPointers(PointerState.Default, PointerState.Other) { DisplayName = "Qty (Super Unit)", Name = DailyDataPointers.OrderInSuperUnits, IsForUser = true, IsForManager = true, ColumnType = ColumnType.Measure,ManagerOrder=41,UserOrder=43},
                };
            }
        }
    }
    public enum DailyDataPointers
    {
        [Display(Name = "Total Users", GroupName = "Fixed", Order = 1)]
        TotalUsers,
        [Display(Name = "Retailing", GroupName = "Fixed", Order = 2)]
        Retailing,
        [Display(Name = "Official Work", GroupName = "Fixed", Order = 3)]
        OfficialWork,
        [Display(Name = "Leave", GroupName = "Fixed", Order = 4)]
        Leave,
        [Display(Name = "Planned Leave", GroupName = "Fixed", Order = 5)]
        PlannedLeave,
        [Display(Name = "Absent", GroupName = "Fixed", Order = 6)]
        Absent,
        [Display(Name = "Reporting Manager", GroupName = "Fixed", Order = 7)]
        ReportingManager,
        [Display(Name = "Field User", GroupName = "Fixed", Order = 8)]
        FieldUserName,
        [Display(Name = "Field User HQ", GroupName = "Fixed", Order = 9)]
        FieldUserHQ,
        [Display(Name = "Rank", GroupName = "Fixed", Order = 10)]
        FieldUserRole,
        [Display(Name = "Assigned Type", GroupName = "Fixed", Order = 11)]
        AssignedReasonCategory,
        [Display(Name = "Type", GroupName = "Fixed", Order = 12)]
        ReasonCategory,
        [Display(Name = "Assigned Reason", GroupName = "Fixed", Order = 13)]
        AssignedReason,
        [Display(Name = "Reason", GroupName = "Fixed", Order = 14)]
        Reason,
        [Display(Name = "Assigned JW User", GroupName = "Fixed", Order = 15)]
        AssignedJointWorkingEmployee,
        [Display(Name = "Selected JW User", GroupName = "Fixed", Order = 16)]
        JointWorkingEmployee,
        [Display(Name = "Assigned Beat", GroupName = "Fixed", Order = 17)]
        AssignedBeat,
        [Display(Name = "Selected Beat", GroupName = "Fixed", Order = 18)]
        SelectedBeat,
        [Display(Name = "Assigned Route", GroupName = "Fixed", Order = 17)]
        AssignedRoute,
        [Display(Name = "Selected Route", GroupName = "Fixed", Order = 18)]
        SelectedRoute,
        [Display(Name = "Distributor", GroupName = "Fixed", Order = 19)]
        Distributor,
        [Display(Name = "Day Start Location", GroupName = "Fixed", Order = 20)]
        DayStartLocation,
        [Display(Name = "Log In", GroupName = "NotFixed", Order = 21)]
        Login,
        [Display(Name = "First Call", GroupName = "NotFixed", Order = 22)]
        FirstCallTime,
        [Display(Name = "First PC", GroupName = "NotFixed", Order = 23)]
        FirstPCTime,
        [Display(Name = "SC", GroupName = "NotFixed", Order = 24)]
        SC,
        [Display(Name = "TC", GroupName = "NotFixed", Order = 25)]
        TC,
        [Display(Name = "PC", GroupName = "NotFixed", Order = 26)]
        PC,
        [Display(Name = "Productivity(%)", GroupName = "NotFixed", Order = 26)]
        Productivity,
        [Display(Name = "SchProductivity(%)", GroupName = "NotFixed", Order = 26)]
        SchProductivity,
        [Display(Name = "CAP", GroupName = "NotFixed", Order = 27)]
        CAP,
        [Display(Name = "OVT (%)", GroupName = "NotFixed", Order = 28)]
        OVT,
        [Display(Name = "OVC (%)", GroupName = "NotFixed", Order = 29)]
        OVC,
        [Display(Name = "TO", GroupName = "NotFixed", Order = 30)]
        TelephonicOrders,
        [Display(Name = "JW Calls", GroupName = "NotFixed", Order = 31)]
        JointWorkingCalls,
        [Display(Name = "Qty (Std Unit)", GroupName = "NotFixed", Order = 32)]
        OrderInStdUnits,
        [Display(Name = "FOC", GroupName = "NotFixed", Order = 33)]
        TotalSchemeQty,
        [Display(Name = "Net Value", GroupName = "NotFixed", Order = 34)]
        NetValue,
        [Display(Name = "New Outlets", GroupName = "NotFixed", Order = 35)]
        NewOutletsCreated,
        [Display(Name = "No Of Other Activities", GroupName = "NotFixed", Order = 36)]
        NoOfOtherActivities,
        [Display(Name = "MTD", GroupName = "NotFixed", Order = 37)]
        MTDvalue,
        [Display(Name = "MTD", GroupName = "NotFixed", Order = 38)]
        MTDunit,
        [Display(Name = "Physical TC", GroupName = "NotFixed", Order = 39)]
        PhycialCalls,
        [Display(Name = "DayStartType", GroupName = "NotFixed", Order = 40)]
        DayStartType,
        [Display(Name = "DayStartReasonCategory", GroupName = "NotFixed", Order = 41)]
        DayStartReasonCategory,
        [Display(Name = "DayStartReasonDescription", GroupName = "NotFixed", Order = 42)]
        DayStartReasonDescription,
        [Display(Name = "Total Time", GroupName = "NotFixed", Order = 43)]
        TotalTime,
        [Display(Name = "Is Journey Violated", GroupName = "NotFixed", Order = 44)]
        IsJourneyViolated,
        [Display(Name = "New Outlet Value", GroupName = "NotFixed", Order = 45)]
        NewOutletSalesInRevenue,
        [Display(Name = "Selected Journey Outlet", GroupName = "NotFixed", Order = 46)]
        SelectedJourneyOutlet,
        [Display(Name = "Qty (Std Unit)", GroupName = "NotFixed", Order = 47)]
        OrderInSuperUnits,
        [Display(Name = "Is Position Not UnderManager ", GroupName = "NotFixed", Order = 47)]
        IsPositionNotUnderManager,
    }
}
