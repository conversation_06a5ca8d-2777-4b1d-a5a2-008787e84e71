﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Library.ReverseGeoCoder
{
    public interface IGeoCoding
    {
        //GeoCodedReturnData GetDataFromLatLng(decimal lat, decimal lng);
        GeoCodedReturnData GetDataFromLatLongFromGoogle(decimal lat, decimal lng);
        GeoCodedReturnData GetDataFromLatLngFromMapMyIndia(decimal lat, decimal lng);

        GeoCodedReturnData GetDataFromLatLngFromLocationIQ(decimal lat, decimal lng);
    }
}
