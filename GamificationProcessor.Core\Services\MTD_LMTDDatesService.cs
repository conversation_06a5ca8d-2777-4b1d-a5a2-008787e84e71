﻿using GamificationProcessor.Core.Helpers;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using FA_MTD_LMTD = GamificationProcessor.Core.Services.Models.FA_MTD_LMTD;

namespace GamificationProcessor.Core.Services
{
    public class MTD_LMTDDatesService
    {
        private readonly ReportDataAPIsHelper reportDataAPIsHelper;

        public MTD_LMTDDatesService(ReportDataAPIsHelper reportDataAPIsHelper)
        {
            this.reportDataAPIsHelper = reportDataAPIsHelper;
        }

        public async Task<FA_MTD_LMTD> GetDates(long compnayId, DateTime date, int yearStartMonth, bool includeToday=false)
        {
            var api = $"api/MTDLMTDDate/GetMTDLMTD?companyId={compnayId}&today={date.ToString("MM/dd/yyyy")}&includeToday={includeToday}&yearStartMonth={yearStartMonth}";
            var data = await reportDataAPIsHelper.Get(api);

            return JsonConvert.DeserializeObject<FA_MTD_LMTD>(data);
        }

        

    }
}
