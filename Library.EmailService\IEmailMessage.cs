﻿using System;
using System.Threading.Tasks;
using Library.Infrastructure.QueueService;
using Library.SlackService;
using Library.StorageWriter;

namespace Library.EmailService
{
    public interface IEmailMessage : IQueueMessage
    {
        string FromEmail { get; set; }
        string FromName { get; set; }
        bool IsHTML { get; set; }
        string Message { get; set; }
        string Subject { get; set; }
        string To { get; set; }
        string Bcc { get; set; }
        string Cc { get; set; }

    }
    public class EmailMessage : IEmailMessage
    {
        public string FromEmail { get; set; }
        public string FromName { get; set; }
        public bool IsHTML { get; set; }
        public string Message { get; set; }
        public string Subject { get; set; }
        public string To { get; set; }
        public string Bcc { get; set; }
        public string Cc { get; set; }
        public string ContentPath { get; set; }

        public ISlackMessage ToSlackMessage(string channel)
        {
            return new SlackMessage
            {
                Channel = channel,
                Text = $"Email:{Subject}",
                Username = "DebugEmailRequest",
                Attachments = new System.Collections.Generic.List<Attachment>
                {
                    new Attachment
                    {
                        Title="EmailDetails",
                        Fields=new System.Collections.Generic.List<Field>
                        {
                            new Field{Title="From",Value=$"[{FromName}]{FromEmail}",Short=true},
                            new Field{Title="To",Value=To,Short=true},
                            new Field{Title="Subject",Value=Subject,Short=false},
                            new Field{Title="BCC",Value=Bcc,Short=true},
                            new Field{Title="IsHtml",Value=IsHTML.ToString(),Short=true},
                            new Field{Title="Body",Value=Message,Short=false}
                        }
                    }
                }
            };
        }

        internal async Task VerifyContent(string masterStorageConnectionString)
        {
            if ((this.Message?.Length ?? 0) > 45000)
            {
                var blobWriter = new AnonymousBlobWriter(masterStorageConnectionString);
                var filepath = $"{Guid.NewGuid()}.html";
                this.Message = await blobWriter.WriteToBlob(filepath, this.Message, "text/html");
                this.ContentPath = this.Message;
            }
        }
    }

}