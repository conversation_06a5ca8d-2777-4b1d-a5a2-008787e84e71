﻿using GamificationAutomatedProcessor.DbStorage.DbModels;
using Microsoft.EntityFrameworkCore;
using System.Threading;
using System.Threading.Tasks;

namespace GamificationAutomatedProcessor.DbStorage.DbContexts
{
    public class WritableTransactionDbContext : DbContext
    {
        public DbSet<DbGameKPIStat> GameKPIStats { get; set; }
        public WritableTransactionDbContext(DbContextOptions<WritableTransactionDbContext> options) : base(options)
        {
        }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {

        }
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            
            base.OnModelCreating(modelBuilder);
        }
        public override Task<int> SaveChangesAsync(CancellationToken cancellationToken = default(CancellationToken))
        {
            return base.SaveChangesAsync(cancellationToken);

        }
    }

}
