﻿using FileGenerator.Attributes;
using System;
using System.Collections.Generic;
using System.Reflection;
using System.Text;

namespace FileGenerator
{
    public class TableHelpers
    {
        public static bool CheckIfColNull<T>(PropertyInfo colProp, IEnumerable<T> listOfData)
        {
            foreach (var item in listOfData)
            {
                if (colProp.GetValue(item) != null && !string.IsNullOrWhiteSpace(colProp.GetValue(item).ToString()))
                {
                    return false;
                }
            }
            return true;
        }

        public static bool ShouldHideColumn<T>(PropertyInfo colProp, IEnumerable<T> listOfData)
        {
            var Tattr = Attribute.GetCustomAttribute(colProp, typeof(TableFieldAttribute));
            var excelAttrOfT = Tattr as TableFieldAttribute;
            if (excelAttrOfT.ColumnRequirement == Requirement.HideIfNull)
            {
                return CheckIfColNull(colProp, listOfData);
            }
            else
            {
                return false;
            }
        }
    }
}
