﻿using Microsoft.EntityFrameworkCore;
using System.Linq;
using System;
using System.Threading.Tasks;
using System.Threading;
using GamificationProcessor.DbStorage.Interfaces;
using GamificationProcessor.Core.Interfaces;

namespace GamificationProcessor.DbStorage.DbContexts
{
    public class ReportDbContext : DbContext
    {
        public ReportDbContext(DbContextOptions<ReportDbContext> options) : base(options)
        {
            this.Database.SetCommandTimeout(TimeSpan.FromSeconds(120));
        }
        public DbSet<DbModels.GamificationKPIStat> GamificationKPIStats { get; set; }
        public DbSet<DbModels.Attendances> Attendances { get; set; }


        public override int SaveChanges()
        {
            throw new NotImplementedException();
        }

        public override int SaveChanges(bool acceptAllChangesOnSuccess)
        {
            throw new NotImplementedException();
        }

        public override async Task<int> SaveChangesAsync(bool acceptAllChangesOnSuccess, CancellationToken cancellationToken = default(CancellationToken))
        {
            var newDeviceEntities = ChangeTracker.Entries<ICreatedEntity>()
                .Where(e => e.State == EntityState.Added).Select(e => e.Entity).ToList();
            var now = DateTime.UtcNow;
            foreach (var item in newDeviceEntities)
            {
                item.CreatedAt = now;
            }
            return await base.SaveChangesAsync(acceptAllChangesOnSuccess, cancellationToken);
        }

        public void RejectChanges()
        {
            var entries = ChangeTracker.Entries().ToList();
            foreach (var entry in entries)
            {
                switch (entry.State)
                {
                    case EntityState.Modified:
                    case EntityState.Deleted:
                        entry.State = EntityState.Modified; //Revert changes made to deleted entity.
                        entry.State = EntityState.Unchanged;
                        break;
                    case EntityState.Added:
                        entry.State = EntityState.Detached;
                        break;
                }
            }
        }

        public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default(CancellationToken))
        {
            var entries = ChangeTracker.Entries().Where(e => e.State == EntityState.Modified || e.State == EntityState.Added).ToList();
            var now = DateTime.UtcNow;
            foreach (var item in entries)
            {
                if (item.Entity.GetType().IsAssignableFrom(typeof(IModifiableEntity)))
                {
                    ((IModifiableEntity)item.Entity).ModifiedOn = now;
                }
            }
            return await base.SaveChangesAsync(cancellationToken);
        }


        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
        }
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {

            base.OnModelCreating(modelBuilder);
        }
    }
}
