using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;

namespace Library.SqlHelper
{
    public class SqlDataReader : IDisposable
    {
        public class IntClass
        {
            public int IntVal { get; set; }
        }
        /// <summary>
        /// Command Timeout in seconds
        /// </summary>
        public int CommandTimeout { get; set; }

        private SqlConnection connection;

        /// <summary>
        /// </summary>
        /// <param name="connectionString">connectionString of the DataSource</param>
        /// <param name="commandTimeout">timeout in Seconds</param>
        public SqlDataReader(string connectionString, int commandTimeout = 30)
        {
            this.CommandTimeout = commandTimeout;
            this.connection = new SqlConnection(connectionString);
        }
        private async Task OpenConnection()
        {
            connection.Close();
            await connection.OpenAsync();
        }
        public void CloseConnection()
        {
            connection.Close();
        }
        public async Task<string> GetSingleResultOfQuery(string queryString, List<SqlParameter> parameters = null, int? commandTimeout = null)
        {
            try
            {
                string result = null;
                await OpenConnection();
                using (SqlCommand command = new SqlCommand(queryString, connection))
                {
                    CommandTimeout = commandTimeout ?? this.CommandTimeout;
                    if (parameters != null)
                    {
                        command.Parameters.AddRange(parameters.ToArray());
                    }
                    using (System.Data.SqlClient.SqlDataReader reader = await command.ExecuteReaderAsync())
                    {
                        if (reader.HasRows)
                        {
                            reader.Read();
                            result = reader[0].ToString();
                        }
                        return result;
                    }
                }            
            }
            catch(Exception)
            {
                throw;
            }
            finally
            {
                CloseConnection();
            }
           
        }
        /// <summary>
        /// Converts a query to enumerable of the Type Provided
        /// </summary>
        /// <typeparam name="T">Return Data Type in the Enumerable</typeparam>
        /// <param name="queryString">sql query to be executed</param>
        /// <param name="parameters"> sql Parameters if any</param>
        /// <param name="commandTimeout"></param>
        /// <returns>Enumerable of Type T</returns>
        public async Task<IEnumerable<T>> GetModelFromQueryAsync<T>(string queryString, List<SqlParameter> parameters = null, int? commandTimeout = null, bool isSp = false)
        {
            try
            {
                await OpenConnection();
                var data = await GetModelFromQueryAsync<T>(connection, queryString, parameters, commandTimeout, isSp);
                return data;
            }
            catch(Exception ex)
            {
                throw;
            }
            finally
            {
                CloseConnection();
            }
         
        }
        
        private async Task<IEnumerable<T>> GetModelFromQueryAsync<T>(SqlConnection connection, string queryString, List<SqlParameter> parameters = null, int? commandTimeout = null, bool isSp = false)
        {
            var data = new List<T>();
            using (SqlCommand command = new SqlCommand(queryString, connection))
            {
                if (isSp)
                {
                    command.CommandType = CommandType.StoredProcedure;
                }
                command.CommandTimeout = commandTimeout ?? this.CommandTimeout;
                if (parameters != null)
                {
                    command.Parameters.AddRange(parameters.ToArray());
                }
                using (System.Data.SqlClient.SqlDataReader reader = await command.ExecuteReaderAsync())
                {
                    if (reader.HasRows)
                    {
                        int numFields = reader.FieldCount;
                        var rangeValues = Enumerable.Range(0, numFields).ToList();

                        var columnTypes = rangeValues.Select(i => reader.GetFieldType(i)).ToArray();
                        var columnNames = rangeValues.Select(i => reader.GetName(i)).ToArray();
                        var type = typeof(T);
                        var props = rangeValues
                                      .Select(i => type.GetProperty(columnNames[i]))
                                      .ToArray();

                        var fieldExceptions = rangeValues.Where(i => props[i] == null).ToList();
                        if (fieldExceptions.Count() > 0)
                        {
                            var errors = fieldExceptions.Where(i => fieldExceptions.Contains(i)).Select(i => columnNames[i]).ToList();
                            throw new InvalidOperationException($"Some of the Field(s) from query [{string.Join(",", errors)}] are not matching in the class");
                        }

                        var nonNullprops = rangeValues
                              .Select(i =>
                              Nullable.GetUnderlyingType(type.GetProperty(columnNames[i]).PropertyType) ?? type.GetProperty(columnNames[i]).PropertyType)
                              .ToArray();
                        var castExceptions = rangeValues.Where(i => !props[i].PropertyType.IsAssignableFrom(columnTypes[i])).ToList();
                        if (castExceptions.Count() > 0)
                        {
                            var errors = castExceptions.Select(i => $"'from {props[i].PropertyType} to {columnTypes[i]} for field {columnNames[i]}'").ToList();
                            throw new InvalidCastException($"Cannot convert {string.Join(",", errors)}");
                        }
                        while (reader.Read())
                        {
                            var item = Activator.CreateInstance(type);
                            rangeValues.ForEach(i =>
                            {
                                var val = reader.GetValue(i);
                                if (val.GetType() == typeof(System.DBNull) || val == null)
                                {
                                    props[i].SetValue(item, null);
                                }
                                else
                                {
                                    props[i].SetValue(item, Convert.ChangeType(reader.GetValue(i), nonNullprops[i]));

                                }

                            });
                            data.Add((T)item);
                        }
                    }
                    return data;
                }
            }
              

        }
       
        public async Task<IEnumerable<T>> GetModelFromQueryAsync<T>(string queryString, List<long> ids, string idPlaceHolders = "$$Ids$$", List<SqlParameter> parameters = null, int? commandTimeout = null)
        {
            try
            {
                await OpenConnection();
                var data = await GetModelFromQueryAsync<T>(connection, await GetQueryStringWithIds(connection, queryString, ids, idPlaceHolders), parameters, commandTimeout);
                return data;
            }
            catch(Exception ex)
            {
                throw;
            }
            finally
            {
                 CloseConnection();
            }
        
        }
        public async Task<IEnumerable<T>> GetModelFromQueryWithTempTableAsync<T>(string queryString, List<long> ids, string idPlaceHolders = "$$Ids$$", List<SqlParameter> parameters = null, int? commandTimeout = null)
        {
            await OpenConnection();
            var data = await GetModelFromQueryAsync<T>(connection, await GetTableNameWithIds(connection, queryString, ids, idPlaceHolders), parameters, commandTimeout);
            CloseConnection();
            return data;
        }

        private async Task<string> GetQueryStringWithIds(SqlConnection connection, string queryString, List<long> ids, string idPlaceHolders = "$$Ids$$")
        {
            await new SqlCommand(@"DROP TABLE if exists  #TempTable ;
                                 CREATE TABLE #TempTable(Id bigint)", connection).ExecuteNonQueryAsync();

            for (int i = 0; i < ids.Count(); i += 1000)
            {
                await new SqlCommand($@"INSERT INTO #TempTable Values{string.Join(",", ids.Select(id => "(" + id + ")").Skip(i).Take(1000))}", connection).ExecuteNonQueryAsync();
            }
            return queryString.Replace(idPlaceHolders, "select Id from #TempTable");
        }


        private async Task<string> GetTableNameWithIds(SqlConnection connection, string queryString, List<long> ids, string idPlaceHolders = "$$Ids$$")
        {
            await new SqlCommand(@"DROP TABLE if exists  #TempTable ;
                                 CREATE TABLE #TempTable(Id bigint)", connection).ExecuteNonQueryAsync();

            for (int i = 0; i < ids.Count(); i += 1000)
            {
                await new SqlCommand($@"INSERT INTO #TempTable Values{string.Join(",", ids.Select(id => "(" + id + ")").Skip(i).Take(1000))}", connection).ExecuteNonQueryAsync();
            }
            return queryString.Replace(idPlaceHolders, "#TempTable");
        }

        public async Task<DataTable> GetDataTableForQuery(string queryString, List<SqlParameter> parameters = null, int? commandTimeout = null, bool isSp = false, string idPlaceHolders = "$$Ids$$", List<long> ids=null)
        {
            try
            {
                await OpenConnection();
                if (!isSp && ids != null)
                {  queryString = await GetQueryStringWithIds(connection, queryString, ids, idPlaceHolders); }
                
                using ( SqlCommand command = new SqlCommand(queryString, connection))
                {
                    command.CommandTimeout = commandTimeout ?? this.CommandTimeout;
                    if (isSp)
                    {
                        command.CommandType = CommandType.StoredProcedure;
                    }
                    if (parameters != null)
                    {
                        command.Parameters.AddRange(parameters.ToArray());
                    }
                    using (System.Data.SqlClient.SqlDataReader reader = await command.ExecuteReaderAsync())
                    {
                        DataTable dt = null;
                        dt = new DataTable();
                        dt.Load(reader);
                        return dt;
                    }
                    
                }             
            }
            catch(Exception)
            {
                throw;
            }
            finally
            {
                 CloseConnection();
            }
           
        }

        public async Task<int> RunQuery(string queryString, List<SqlParameter> parameters = null, int? commandTimeout = null)
        {
            SqlCommand command = new SqlCommand(queryString, connection)
            {
                CommandTimeout = commandTimeout ?? this.CommandTimeout
            };
            if (parameters != null)
            {
                command.Parameters.AddRange(parameters.ToArray());
            }
            await OpenConnection();

            var dataReturn = await command.ExecuteNonQueryAsync();
            CloseConnection();
            return dataReturn;
        }
       
        public async Task<SqlTransaction> RunTransactionQuery(string queryString, List<long> ids, string idPlaceHolders = "$$Ids$$", List<SqlParameter> parameters = null, int? commandTimeout = null)
        {
            await OpenConnection();
            var query = await GetQueryStringWithIds(connection, queryString, ids, idPlaceHolders);
            var transaction = connection.BeginTransaction();
            SqlCommand command = new SqlCommand(query, connection, transaction)
            {
                CommandTimeout = commandTimeout ?? this.CommandTimeout
            };
            if (parameters != null)
            {
                command.Parameters.AddRange(parameters.ToArray());
            }
            await command.ExecuteNonQueryAsync();
            return transaction;
        }

        public void Dispose()
        {
            connection.Close();
        }

        public static string CreateTABLE(string tableName, DataTable table)
        {
            string sqlsc;
            sqlsc = "CREATE TABLE " + tableName + "(";
            for (int i = 0; i < table.Columns.Count; i++)
            {
                sqlsc += "\n [" + table.Columns[i].ColumnName + "] ";
                string columnType = table.Columns[i].DataType.ToString();
                switch (columnType)
                {
                    case "System.Int32":
                        sqlsc += " int ";
                        break;
                    case "System.Int64":
                        sqlsc += " bigint ";
                        break;
                    case "System.Int16":
                        sqlsc += " smallint";
                        break;
                    case "System.Byte":
                        sqlsc += " tinyint";
                        break;
                    case "System.Decimal":
                        sqlsc += " decimal ";
                        break;
                    case "System.DateTime":
                        sqlsc += " datetime ";
                        break;
                    case "System.Double":
                        sqlsc += " float ";
                        break;
                    case "System.Boolean":
                        sqlsc += " bit ";
                        break;
                    case "System.String":
                    default:
                        sqlsc += string.Format(" nvarchar({0}) ", table.Columns[i].MaxLength == -1 ? "max" : table.Columns[i].MaxLength.ToString());
                        break;
                }
                //if (table.Columns[i].AutoIncrement)
                //    sqlsc += " IDENTITY(" + table.Columns[i].AutoIncrementSeed.ToString() + "," + table.Columns[i].AutoIncrementStep.ToString() + ") ";
                if (!table.Columns[i].AllowDBNull)
                    sqlsc += " NOT NULL ";
                sqlsc += ",";
            }
            return sqlsc.Substring(0, sqlsc.Length - 1) + "\n)";
        }

        public async Task<DataTable> RunQueryOverCustomTable(DataTable updatedLiveSalesData, string query, List<SqlParameter> parameters = null, int? commandTimeout = null)
        {
            try
            {
                await OpenConnection();
                var sqlCreateTableQuery = CreateTABLE("#TempTable", updatedLiveSalesData);
                await new SqlCommand($@"DROP TABLE if exists  #TempTable ;{sqlCreateTableQuery};"
                                 , connection).ExecuteNonQueryAsync();
                Dictionary<long, List<string>> insertData = new Dictionary<long, List<string>>();
                foreach (DataRow row in updatedLiveSalesData.Rows)
                {
                    List<string> inputData = new List<string>();
                    for (var i = 0; i < updatedLiveSalesData.Columns.Count; i++)
                    {
                        var val = row[i].ToString().ToLower() == "false" || row[i].ToString().ToLower() == "true" ? (row[i].ToString().ToLower() == "false" ? 0.ToString() : 1.ToString()) : row[i].ToString();
                        if (!string.IsNullOrWhiteSpace(val))
                        {
                            if (updatedLiveSalesData.Columns[i].DataType.ToString() == "System.DateTime" || updatedLiveSalesData.Columns[i].DataType.ToString() == "System.DateTimeOffset")
                            {
                                string format = "yyyy-MM-dd HH:mm:ss";
                                DateTime date = DateTime.Parse(val);
                                val = date.ToString(format);
                            }
                            val = "'" + val + "'";
                        }
                        else
                        {
                            val = "null";
                        }
                        inputData.Add(val);
                    }
                    insertData.Add((long)(row[0]), inputData);
                }

                var groupCount = 500;
                for (int i = 0; i < insertData.Count(); i += groupCount)
                {
                    var insertGroup = insertData.Skip(i).Take(groupCount);
                    var values = new List<string>();
                    foreach (var datum in insertGroup)
                    {
                        values.Add("(" + string.Join(",", datum.Value.Select(d => d)) + ")");
                    }
                    await new SqlCommand($@"INSERT INTO #TempTable Values {string.Join(",", values)}", connection).ExecuteNonQueryAsync();
                }
                using ( SqlCommand command = new SqlCommand(query, connection))
                {
                    CommandTimeout = commandTimeout ?? this.CommandTimeout;
                    if (parameters != null)
                    {
                        command.Parameters.AddRange(parameters.ToArray());
                    }
                    using (System.Data.SqlClient.SqlDataReader reader = await command.ExecuteReaderAsync())
                    {
                        var dt = new DataTable();
                        dt.Load(reader);
                        return dt;
                    }
                }
            }
            catch(Exception)
            {
                throw;
            }
            finally
            {
                 CloseConnection();
            }

        }
    }

}
