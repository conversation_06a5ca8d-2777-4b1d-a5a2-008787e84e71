﻿using Libraries.CommonEnums;
using Libraries.PerspectiveColumns.Interface;
using System.Collections.Generic;

namespace Libraries.PerspectiveColumns
{
    public class TrendReportPerspective : IPerspective
    {
        //private linkNames linkNames;

        public TrendReportPerspective(long companyId)
        {
           // linkNames = InMemorySettings.GetLinkNames(companyId);
        }
        List<PerspectiveColumnModel> IPerspective.Columns { get => Columns; set => throw new System.NotImplementedException(); }
        public List<PerspectiveColumnModel> Columns => new List<PerspectiveColumnModel>
        {
            // Name Property Needs to match the key used for Nomenclature For QuickViz
                    new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = "GSM"    , Name = "GSM"         , IsMeasure = false, IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.Unknown, IsOtherMeasure = false, IsPivot = false},
                    new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = "NationalSalesManager" , Name = "NSM"         , IsMeasure = false, IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.Unknown, IsOtherMeasure = false, IsPivot = false},
                    new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = "ZonalSalesManager"    , Name = "ZSM"         , IsMeasure = false, IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.Unknown, IsOtherMeasure = false, IsPivot = false},
                    new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = "RegionalSalesManager" , Name = "RSM"         , IsMeasure = false, IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.Unknown, IsOtherMeasure = false, IsPivot = false},
                    new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = "AreaSalesManager"     , Name = "ASM"         , IsMeasure = false, IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.Unknown, IsOtherMeasure = false, IsPivot = false},
                    new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName =  "Employee"            , Name = "ESM"    , IsMeasure = false, IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.Unknown, IsOtherMeasure = false                 },
                    new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = "FieldUser HQ"                 , Name = "FieldUserHQ" , IsMeasure = false, IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.Unknown                                              },
                    new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = "FieldUser ErpId"                 , Name = "FieldUserERPID" , IsMeasure = false, IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.Unknown                                              },
                    new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = "FieldUser Designation"                 , Name = "FieldUserDesignation" , IsMeasure = false, IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.Unknown                                              },
                    new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = "Rank"                 , Name = "FieldUserRank" , IsMeasure = false, IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.Unknown                                              },
                    new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = "User Status" , Name = "UserStatus"         , IsMeasure = false, IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.Unknown, IsOtherMeasure = false, IsPivot = false},
                    new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName ="Active Days"    , Name = "ActiveDays"         , IsMeasure = false, IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.Unknown, IsOtherMeasure = false, IsPivot = false},
                    new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = "FieldUser Region" , Name = "FieldUserRegion"         , IsMeasure = false, IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.Unknown, IsOtherMeasure = false, IsPivot = false},
                    new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName ="FieldUser Zone"    , Name = "FieldUserZone"         , IsMeasure = false, IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.Unknown, IsOtherMeasure = false, IsPivot = false},
                    // Name Property Of New Positions need to be in this Pattern (LXPosition) only or Filtering based on setting won't work
                    new PerspectiveColumnModel() { Attribute = "Position"        , DisplayName = "L8Position"             , Name = "L8Position"       , IsMeasure = false , IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsPivot = false , IsOtherMeasure = false },
                    new PerspectiveColumnModel() { Attribute = "Position"        , DisplayName = "L7Position"            , Name = "L7Position"       , IsMeasure = false , IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsPivot = false , IsOtherMeasure = false },
                    new PerspectiveColumnModel() { Attribute = "Position"        , DisplayName = "L6Position"             , Name = "L6Position"       , IsMeasure = false , IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsPivot = false , IsOtherMeasure = false },
                    new PerspectiveColumnModel() { Attribute = "Position"        , DisplayName = "L5Position"            , Name = "L5Position"       , IsMeasure = false , IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsPivot = false , IsOtherMeasure = false },
                    new PerspectiveColumnModel() { Attribute = "Position"        , DisplayName = "L4Position"             , Name = "L4Position"       , IsMeasure = false , IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsPivot = false , IsOtherMeasure = false },
                    new PerspectiveColumnModel() { Attribute = "Position"        , DisplayName = "L3Position"             , Name = "L3Position"       , IsMeasure = false , IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsPivot = false , IsOtherMeasure = false },
                    new PerspectiveColumnModel() { Attribute = "Position"        , DisplayName = "L2Position"             , Name = "L2Position"       , IsMeasure = false , IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsPivot = false , IsOtherMeasure = false },
                    new PerspectiveColumnModel() { Attribute = "Position"        , DisplayName = "L1Position"             , Name = "L1Position"       , IsMeasure = false , IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsPivot = false , IsOtherMeasure = false },
                    new PerspectiveColumnModel() { Attribute = "Sales Territory" , DisplayName = "Level7"                 , Name = "Level7"           , IsMeasure = false , IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsPivot = false , IsOtherMeasure = false },
                    new PerspectiveColumnModel() { Attribute = "Sales Territory" , DisplayName = "Level6"                 , Name = "Level6"           , IsMeasure = false , IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsPivot = false , IsOtherMeasure = false },
                    new PerspectiveColumnModel() { Attribute = "Sales Territory" , DisplayName = "Level5"                 , Name = "Level5"           , IsMeasure = false , IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsPivot = false , IsOtherMeasure = false },
                    new PerspectiveColumnModel() { Attribute = "Sales Territory" , DisplayName = "Zone"                   , Name = "Zone"             , IsMeasure = false , IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsPivot = false , IsOtherMeasure = false },
                    new PerspectiveColumnModel() { Attribute = "Sales Territory" , DisplayName = "Region"                 , Name = "Region"           , IsMeasure = false , IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsPivot = false , IsOtherMeasure = false },
                    new PerspectiveColumnModel() { Attribute = "Sales Territory" , DisplayName = "Territory"              , Name = "Territory"        , IsMeasure = false , IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.CountDistinct                                            },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LMTDTC"                               , Name = "LMTDTC"                      , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value     },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "MTDTC"                               , Name = "MTDTC"                      , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value     },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "L3MAvgTC"                               , Name = "L3MAvgTC"                      , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value     },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LMTC"                               , Name = "LMTC"                      , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value     },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "PerL3MTC"                               , Name = "PerL3MTC"                      , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value     },

            new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LMTDPC"                               , Name = "LMTDPC"                      , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value   },
            new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "MTDPC"                               , Name = "MTDPC"                      , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value   },
            new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "L3MAvgPC"                               , Name = "L3MAvgPC"                      , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value   },
            new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LMPC"                               , Name = "LMPC"                      , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value   },
            new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "PerL3MPC"                               , Name = "PerL3MPC"                      , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value   },

                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LMTD Working Days"                      , Name = "LMTDWorkingDays"                     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value},
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "MTD Working Days"                      , Name = "MTDWorkingDays"                     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value},
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "L3MAvg Working Days"                      , Name = "L3MAvgWorkingDays"                     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value},
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LM Working Days"                      , Name = "LMWorkingDays"                     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value},
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "PerL3M Working Days"                      , Name = "PerL3MWorkingDays"                     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value},

                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Avg. TC (per day)"                , Name = "AvgTCPerday"         , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value      },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Avg. PC (per day)"                , Name = "AvgPCPerday"         , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value   } ,
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "UTC"                      , Name = "UTC"                     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value    },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "UPC"                      , Name = "UPC"                     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value    },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "OVT"                              , Name = "OVT"                     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value    },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "OVC"                              , Name = "OVC"                     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value      },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Avg Retailing Time"               , Name = "AvgRetailingTime"        , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value     },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Scheme Value"                     , Name = "SchemeValue"            , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value      },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Number of telephonic orders"      , Name = "Numberoftelephonicorders", IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value     },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Order Qty (Unit)"                 , Name = "OrderInUnits"            , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value   },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Value"                            , Name = "Value"                   , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value      },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "NetValue"                         , Name = "NetValue"                , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value      },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Order Qty (Std Unit)"             , Name = "SalesInStdUnit"          , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value      },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Net Value (Dispatch)"             , Name = "DispatchInRevenue"       , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value    },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Dispatch Qty (Std Unit)"          , Name = "DispatchInStdUnits"      , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Dispatch Qty (Unit)"              , Name = "DispatchInUnits"         , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value     },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "AvgValuePerCall"          , Name = "DropSize"                , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value   },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Total Lines"                      , Name = "TotalLines"              , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value     },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LPC"                              , Name = "LPC"                     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value    },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Ach"                              , Name = "Achievement"             , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value    },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Ach(Dispatch)"                    , Name = "DispatchAchievement"     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value      },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Days Retailing"                   , Name = "DaysRetailing"           , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value     },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "SC"                               , Name = "SC"                      , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "New Outlets"                      , Name = "NewOutlets"              , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value    },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "New Outlets Value"                , Name = "NewOutletsValue"         , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value   },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Avg Value Per New Outlet"         , Name = "ValuePerNewOutlet"       , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value     },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "FO Qty (Unit)"                    , Name = "FOQty"                   , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value      },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "FO Qty (Std Unit)"                , Name = "FOStdQty"                , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value    },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "FO Value"                         , Name = "FOValue"                 , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value   },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "FocusedOutlet UTC"                      , Name = "FocusedOutletsUTC"                     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value},
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "FocusedOutlet UPC"                      , Name = "FocusedOutletsUPC"                     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value},
                    //Master measures to be shown only if position code setting is off for now (08/07/2021)
                    new PerspectiveColumnModel() { Attribute = "Master Measure"  , DisplayName = "Employee Overall Target"          , Name = "Targets"                 , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value },
                    new PerspectiveColumnModel() { Attribute = "Master Measure"  , DisplayName = "Total Outlets"                    , Name = "Outlets"                 , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value },
                    new PerspectiveColumnModel() { Attribute = "Master Measure"  , DisplayName = "Total Beats"                      , Name = "Beats"                   , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value },
                    new PerspectiveColumnModel() { Attribute = "Master Measure"  , DisplayName = "Total Routes"                     , Name = "Routes"                  , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value },
                    new PerspectiveColumnModel() { Attribute = "Master Measure"  , DisplayName = "Total Distributors"               , Name = "Distributors"            , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value },
                    new PerspectiveColumnModel() { Attribute = "Master Measure"  , DisplayName = "FocusedOutlet Count"            , Name = "TotalFocusedOutlets"     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value },

                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LMTD Avg. TC (per day)"                , Name = "LMTDAvgTCPerday"         , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value      },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LMTD Avg. PC (per day)"                , Name = "LMTDAvgPCPerday"         , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value   } ,
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LMTD UTC"                      , Name = "LMTDUTC"                     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value    },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LMTD UPC"                      , Name = "LMTDUPC"                     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value    },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LMTD OVT"                              , Name = "LMTDOVT"                     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value    },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LMTD OVC"                              , Name = "LMTDOVC"                     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value      },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LMTD Avg Retailing Time"               , Name = "LMTDAvgRetailingTime"        , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value     },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LMTD Scheme Value"                     , Name = "LMTDSchemeValue"            , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value      },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LMTD Number of telephonic orders"      , Name = "LMTDNumberoftelephonicorders", IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value     },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LMTD Order Qty (Unit)"                 , Name = "LMTDOrderInUnits"            , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value   },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LMTD Value"                            , Name = "LMTDValue"                   , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value      },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LMTD NetValue"                         , Name = "LMTDNetValue"                , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value      },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LMTD Order Qty (Std Unit)"             , Name = "LMTDSalesInStdUnit"          , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value      },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LMTD Net Value (Dispatch)"             , Name = "LMTDDispatchInRevenue"       , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value    },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LMTD Dispatch Qty (Std Unit)"          , Name = "LMTDDispatchInStdUnits"      , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LMTD Dispatch Qty (Unit)"              , Name = "LMTDDispatchInUnits"         , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value     },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LMTD AvgValuePerCall"          , Name = "LMTDDropSize"                , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value   },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LMTD Total Lines"                      , Name = "LMTDTotalLines"              , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value     },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LMTD LPC"                              , Name = "LMTDLPC"                     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value    },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LMTD Ach"                              , Name = "LMTDAchievement"             , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value    },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LMTD Ach(Dispatch)"                    , Name = "LMTDDispatchAchievement"     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value      },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LMTD Days Retailing"                   , Name = "LMTDDaysRetailing"           , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value     },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LMTD SC"                               , Name = "LMTDSC"                      , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LMTD New Outlets"                      , Name = "LMTDNewOutlets"              , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value    },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LMTD New Outlets Value"                , Name = "LMTDNewOutletsValue"         , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value   },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LMTD Avg Value Per New Outlet"         , Name = "LMTDValuePerNewOutlet"       , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value     },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LMTD FO Qty (Unit)"                    , Name = "LMTDFOQty"                   , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value      },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LMTD FO Qty (Std Unit)"                , Name = "LMTDFOStdQty"                , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value    },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LMTD FO Value"                         , Name = "LMTDFOValue"                 , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value   },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LMTD FocusedOutlet UTC"                      , Name = "LMTDFocusedOutletsUTC"                     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value},
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LMTD FocusedOutlet UPC"                      , Name = "LMTDFocusedOutletsUPC"                     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value},
                    //Master measures to be shown only if position code setting is off for now (08/07/2021)
                    new PerspectiveColumnModel() { Attribute = "Master Measure"  , DisplayName = "LMTD Employee Overall Target"          , Name = "LMTDTargets"                 , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value },
                    new PerspectiveColumnModel() { Attribute = "Master Measure"  , DisplayName = "LMTD Total Outlets"                    , Name = "LMTDOutlets"                 , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value },
                    new PerspectiveColumnModel() { Attribute = "Master Measure"  , DisplayName = "LMTD Total Beats"                      , Name = "LMTDBeats"                   , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value },
                    new PerspectiveColumnModel() { Attribute = "Master Measure"  , DisplayName = "LMTD Total Routes"                     , Name = "LMTDRoutes"                  , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value },
                    new PerspectiveColumnModel() { Attribute = "Master Measure"  , DisplayName = "LMTD Total Distributors"               , Name = "LMTDDistributors"            , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value },
                    new PerspectiveColumnModel() { Attribute = "Master Measure"  , DisplayName = "LMTD FocusedOutlet Count"            , Name = "LMTDTotalFocusedOutlets"     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value },

                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "MTD Avg. TC (per day)"                , Name = "MTDAvgTCPerday"         , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value      },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "MTD Avg. PC (per day)"                , Name = "MTDAvgPCPerday"         , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value   } ,
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "MTD UTC"                      , Name = "MTDUTC"                     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value    },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "MTD UPC"                      , Name = "MTDUPC"                     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value    },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "MTD OVT"                              , Name = "MTDOVT"                     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value    },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "MTD OVC"                              , Name = "MTDOVC"                     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value      },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "MTD Avg Retailing Time"               , Name = "MTDAvgRetailingTime"        , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value     },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "MTD Scheme Value"                     , Name = "MTDSchemeValue"            , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value      },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "MTD Number of telephonic orders"      , Name = "MTDNumberoftelephonicorders", IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value     },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "MTD Order Qty (Unit)"                 , Name = "MTDOrderInUnits"            , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value   },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "MTD Value"                            , Name = "MTDValue"                   , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value      },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "MTD NetValue"                         , Name = "MTDNetValue"                , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value      },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "MTD Order Qty (Std Unit)"             , Name = "MTDSalesInStdUnit"          , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value      },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "MTD Net Value (Dispatch)"             , Name = "MTDDispatchInRevenue"       , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value    },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "MTD Dispatch Qty (Std Unit)"          , Name = "MTDDispatchInStdUnits"      , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "MTD Dispatch Qty (Unit)"              , Name = "MTDDispatchInUnits"         , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value     },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "MTD AvgValuePerCall"          , Name = "MTDDropSize"                , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value   },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "MTD Total Lines"                      , Name = "MTDTotalLines"              , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value     },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "MTD LPC"                              , Name = "MTDLPC"                     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value    },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "MTD Ach"                              , Name = "MTDAchievement"             , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value    },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "MTD Ach(Dispatch)"                    , Name = "MTDDispatchAchievement"     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value      },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "MTD Days Retailing"                   , Name = "MTDDaysRetailing"           , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value     },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "MTD SC"                               , Name = "MTDSC"                      , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "MTD New Outlets"                      , Name = "MTDNewOutlets"              , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value    },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "MTD New Outlets Value"                , Name = "MTDNewOutletsValue"         , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value   },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "MTD Avg Value Per New Outlet"         , Name = "MTDValuePerNewOutlet"       , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value     },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "MTD FO Qty (Unit)"                    , Name = "MTDFOQty"                   , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value      },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "MTD FO Qty (Std Unit)"                , Name = "MTDFOStdQty"                , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value    },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "MTD FO Value"                         , Name = "MTDFOValue"                 , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value   },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "MTD FocusedOutlet UTC"                      , Name = "MTDFocusedOutletsUTC"                     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value},
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "MTD FocusedOutlet UPC"                      , Name = "MTDFocusedOutletsUPC"                     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value},
                    //Master measures to be shown only if position code setting is off for now (08/07/2021)
                    new PerspectiveColumnModel() { Attribute = "Master Measure"  , DisplayName = "MTD Employee Overall Target"          , Name = "MTDTargets"                 , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value },
                    new PerspectiveColumnModel() { Attribute = "Master Measure"  , DisplayName = "MTD Total Outlets"                    , Name = "MTDOutlets"                 , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value },
                    new PerspectiveColumnModel() { Attribute = "Master Measure"  , DisplayName = "MTD Total Beats"                      , Name = "MTDBeats"                   , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value },
                    new PerspectiveColumnModel() { Attribute = "Master Measure"  , DisplayName = "MTD Total Routes"                     , Name = "MTDRoutes"                  , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value },
                    new PerspectiveColumnModel() { Attribute = "Master Measure"  , DisplayName = "MTD Total Distributors"               , Name = "MTDDistributors"            , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value },
                    new PerspectiveColumnModel() { Attribute = "Master Measure"  , DisplayName = "MTD FocusedOutlet Count"            , Name = "MTDTotalFocusedOutlets"     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value },

                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "L3MAvg Avg. TC (per day)"                , Name = "L3MAvgAvgTCPerday"         , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value      },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "L3MAvg Avg. PC (per day)"                , Name = "L3MAvgAvgPCPerday"         , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value   } ,
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "L3MAvg UTC"                      , Name = "L3MAvgUTC"                     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value    },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "L3MAvg UPC"                      , Name = "L3MAvgUPC"                     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value    },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "L3MAvg OVT"                              , Name = "L3MAvgOVT"                     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value    },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "L3MAvg OVC"                              , Name = "L3MAvgOVC"                     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value      },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "L3MAvg Avg Retailing Time"               , Name = "L3MAvgAvgRetailingTime"        , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value     },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "L3MAvg Scheme Value"                     , Name = "L3MAvgSchemeValue"            , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value      },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "L3MAvg Number of telephonic orders"      , Name = "L3MAvgNumberoftelephonicorders", IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value     },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "L3MAvg Order Qty (Unit)"                 , Name = "L3MAvgOrderInUnits"            , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value   },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "L3MAvg Value"                            , Name = "L3MAvgValue"                   , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value      },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "L3MAvg NetValue"                         , Name = "L3MAvgNetValue"                , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value      },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "L3MAvg Order Qty (Std Unit)"             , Name = "L3MAvgSalesInStdUnit"          , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value      },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "L3MAvg Net Value (Dispatch)"             , Name = "L3MAvgDispatchInRevenue"       , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value    },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "L3MAvg Dispatch Qty (Std Unit)"          , Name = "L3MAvgDispatchInStdUnits"      , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "L3MAvg Dispatch Qty (Unit)"              , Name = "L3MAvgDispatchInUnits"         , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value     },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "L3MAvg AvgValuePerCall"          , Name = "L3MAvgDropSize"                , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value   },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "L3MAvg Total Lines"                      , Name = "L3MAvgTotalLines"              , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value     },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "L3MAvg LPC"                              , Name = "L3MAvgLPC"                     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value    },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "L3MAvg Ach"                              , Name = "L3MAvgAchievement"             , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value    },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "L3MAvg Ach(Dispatch)"                    , Name = "L3MAvgDispatchAchievement"     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value      },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "L3MAvg Days Retailing"                   , Name = "L3MAvgDaysRetailing"           , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value     },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "L3MAvg SC"                               , Name = "L3MAvgSC"                      , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "L3MAvg New Outlets"                      , Name = "L3MAvgNewOutlets"              , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value    },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "L3MAvg New Outlets Value"                , Name = "L3MAvgNewOutletsValue"         , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value   },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "L3MAvg Avg Value Per New Outlet"         , Name = "L3MAvgValuePerNewOutlet"       , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value     },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "L3MAvg FO Qty (Unit)"                    , Name = "L3MAvgFOQty"                   , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value      },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "L3MAvg FO Qty (Std Unit)"                , Name = "L3MAvgFOStdQty"                , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value    },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "L3MAvg FO Value"                         , Name = "L3MAvgFOValue"                 , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value   },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "L3MAvg FocusedOutlet UTC"                      , Name = "L3MAvgFocusedOutletsUTC"                     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value},
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "L3MAvg FocusedOutlet UPC"                      , Name = "L3MAvgFocusedOutletsUPC"                     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value},
                    //Master measures to be shown only if position code setting is off for now (08/07/2021)
                    new PerspectiveColumnModel() { Attribute = "Master Measure"  , DisplayName = "L3MAvg Employee Overall Target"          , Name = "L3MAvgTargets"                 , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value },
                    new PerspectiveColumnModel() { Attribute = "Master Measure"  , DisplayName = "L3MAvg Total Outlets"                    , Name = "L3MAvgOutlets"                 , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value },
                    new PerspectiveColumnModel() { Attribute = "Master Measure"  , DisplayName = "L3MAvg Total Beats"                      , Name = "L3MAvgBeats"                   , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value },
                    new PerspectiveColumnModel() { Attribute = "Master Measure"  , DisplayName = "L3MAvg Total Routes"                     , Name = "L3MAvgRoutes"                  , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value },
                    new PerspectiveColumnModel() { Attribute = "Master Measure"  , DisplayName = "L3MAvg Total Distributors"               , Name = "L3MAvgDistributors"            , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value },
                    new PerspectiveColumnModel() { Attribute = "Master Measure"  , DisplayName = "L3MAvg FocusedOutlet Count"            , Name = "L3MAvgTotalFocusedOutlets"     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value },

                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LM Avg. TC (per day)"                , Name = "LMAvgTCPerday"         , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value      },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LM Avg. PC (per day)"                , Name = "LMAvgPCPerday"         , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value   } ,
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LM UTC"                      , Name = "LMUTC"                     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value    },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LM UPC"                      , Name = "LMUPC"                     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value    },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LM OVT"                              , Name = "LMOVT"                     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value    },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LM OVC"                              , Name = "LMOVC"                     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value      },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LM Avg Retailing Time"               , Name = "LMAvgRetailingTime"        , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value     },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LM Scheme Value"                     , Name = "LMSchemeValue"            , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value      },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LM Number of telephonic orders"      , Name = "LMNumberoftelephonicorders", IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value     },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LM Order Qty (Unit)"                 , Name = "LMOrderInUnits"            , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value   },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LM Value"                            , Name = "LMValue"                   , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value      },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LM NetValue"                         , Name = "LMNetValue"                , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value      },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LM Order Qty (Std Unit)"             , Name = "LMSalesInStdUnit"          , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value      },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LM Net Value (Dispatch)"             , Name = "LMDispatchInRevenue"       , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value    },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LM Dispatch Qty (Std Unit)"          , Name = "LMDispatchInStdUnits"      , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LM Dispatch Qty (Unit)"              , Name = "LMDispatchInUnits"         , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value     },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LM AvgValuePerCall"          , Name = "LMDropSize"                , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value   },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LM Total Lines"                      , Name = "LMTotalLines"              , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value     },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LM LPC"                              , Name = "LMLPC"                     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value    },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LM Ach"                              , Name = "LMAchievement"             , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value    },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LM Ach(Dispatch)"                    , Name = "LMDispatchAchievement"     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value      },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LM Days Retailing"                   , Name = "LMDaysRetailing"           , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value     },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LM SC"                               , Name = "LMSC"                      , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LM New Outlets"                      , Name = "LMNewOutlets"              , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value    },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LM New Outlets Value"                , Name = "LMNewOutletsValue"         , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value   },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LM Avg Value Per New Outlet"         , Name = "LMValuePerNewOutlet"       , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value     },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LM FO Qty (Unit)"                    , Name = "LMFOQty"                   , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value      },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LM FO Qty (Std Unit)"                , Name = "LMFOStdQty"                , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value    },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LM FO Value"                         , Name = "LMFOValue"                 , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value   },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LM FocusedOutlet UTC"                      , Name = "LMFocusedOutletsUTC"                     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value},
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LM FocusedOutlet UPC"                      , Name = "LMFocusedOutletsUPC"                     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value},
                    //Master measures to be shown only if position code setting is off for now (08/07/2021)
                    new PerspectiveColumnModel() { Attribute = "Master Measure"  , DisplayName = "LM Employee Overall Target"          , Name = "LMTargets"                 , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value },
                    new PerspectiveColumnModel() { Attribute = "Master Measure"  , DisplayName = "LM Total Outlets"                    , Name = "LMOutlets"                 , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value },
                    new PerspectiveColumnModel() { Attribute = "Master Measure"  , DisplayName = "LM Total Beats"                      , Name = "LMBeats"                   , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value },
                    new PerspectiveColumnModel() { Attribute = "Master Measure"  , DisplayName = "LM Total Routes"                     , Name = "LMRoutes"                  , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value },
                    new PerspectiveColumnModel() { Attribute = "Master Measure"  , DisplayName = "LM Total Distributors"               , Name = "LMDistributors"            , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value },
                    new PerspectiveColumnModel() { Attribute = "Master Measure"  , DisplayName = "LM FocusedOutlet Count"            , Name = "LMTotalFocusedOutlets"     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value },

                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "PerL3M Avg. TC (per day)"                , Name = "PerL3AvgTCPerday"         , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value      },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "PerL3M Avg. PC (per day)"                , Name = "PerL3AvgPCPerday"         , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value   } ,
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "PerL3M UTC"                      , Name = "PerL3MUTC"                     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value    },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "PerL3M UPC"                      , Name = "PerL3MUPC"                     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value    },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "PerL3M OVT"                              , Name = "PerL3MOVT"                     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value    },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "PerL3M OVC"                              , Name = "PerL3MOVC"                     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value      },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "PerL3M Avg Retailing Time"               , Name = "PerL3MAvgRetailingTime"        , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value     },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "PerL3M Scheme Value"                     , Name = "PerL3MSchemeValue"            , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value      },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "PerL3M Number of telephonic orders"      , Name = "PerL3MNumberoftelephonicorders", IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value     },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "PerL3M Order Qty (Unit)"                 , Name = "PerL3MOrderInUnits"            , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value   },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "PerL3M Value"                            , Name = "PerL3MValue"                   , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value      },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "PerL3M NetValue"                         , Name = "PerL3MNetValue"                , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value      },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "PerL3M Order Qty (Std Unit)"             , Name = "PerL3MSalesInStdUnit"          , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value      },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "PerL3M Net Value (Dispatch)"             , Name = "PerL3MDispatchInRevenue"       , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value    },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "PerL3M Dispatch Qty (Std Unit)"          , Name = "PerL3MDispatchInStdUnits"      , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "PerL3M Dispatch Qty (Unit)"              , Name = "PerL3MDispatchInUnits"         , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value     },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "PerL3M AvgValuePerCall"          , Name = "PerL3MDropSize"                , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value   },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "PerL3M Total Lines"                      , Name = "PerL3MTotalLines"              , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value     },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "PerL3M LPC"                              , Name = "PerL3MLPC"                     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value    },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "PerL3M Ach"                              , Name = "PerL3MAchievement"             , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value    },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "PerL3M Ach(Dispatch)"                    , Name = "PerL3MDispatchAchievement"     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value      },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "PerL3M Days Retailing"                   , Name = "PerL3MDaysRetailing"           , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value     },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "PerL3M SC"                               , Name = "PerL3MSC"                      , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "PerL3M New Outlets"                      , Name = "PerL3MNewOutlets"              , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value    },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "PerL3M New Outlets Value"                , Name = "PerL3MNewOutletsValue"         , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value   },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "PerL3M Avg Value Per New Outlet"         , Name = "PerL3MValuePerNewOutlet"       , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value     },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "PerL3M FO Qty (Unit)"                    , Name = "PerL3MFOQty"                   , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value      },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "PerL3M FO Qty (Std Unit)"                , Name = "PerL3MFOStdQty"                , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value    },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "PerL3M FO Value"                         , Name = "PerL3MFOValue"                 , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value   },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "PerL3M FocusedOutlet UTC"                      , Name = "PerL3MFocusedOutletsUTC"                     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value},
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "PerL3M FocusedOutlet UPC"                      , Name = "PerL3MFocusedOutletsUPC"                     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value},
                    //Master measures to be shown only if position code setting is off for now (08/07/2021)
                    new PerspectiveColumnModel() { Attribute = "Master Measure"  , DisplayName = "PerL3M Employee Overall Target"          , Name = "PerL3MTargets"                 , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value },
                    new PerspectiveColumnModel() { Attribute = "Master Measure"  , DisplayName = "PerL3M Total Outlets"                    , Name = "PerL3MOutlets"                 , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value },
                    new PerspectiveColumnModel() { Attribute = "Master Measure"  , DisplayName = "PerL3M Total Beats"                      , Name = "PerL3MBeats"                   , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value },
                    new PerspectiveColumnModel() { Attribute = "Master Measure"  , DisplayName = "PerL3M Total Routes"                     , Name = "PerL3MRoutes"                  , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value },
                    new PerspectiveColumnModel() { Attribute = "Master Measure"  , DisplayName = "PerL3M Total Distributors"               , Name = "PerL3MDistributors"            , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value },
                    new PerspectiveColumnModel() { Attribute = "Master Measure"  , DisplayName = "PerL3M FocusedOutlet Count"            , Name = "PerL3MTotalFocusedOutlets"     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value },

        };
    }
}


