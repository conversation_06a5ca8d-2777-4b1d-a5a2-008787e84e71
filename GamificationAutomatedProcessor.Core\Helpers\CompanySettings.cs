﻿using Libraries.CommonEnums;
using System;
using System.Collections.Generic;

namespace GamificationAutomatedProcessor.Core.Helpers
{


    public class CompanySettings
    {
        private readonly Dictionary<string, object> settings;


        private CompanySettings(Dictionary<string, object> settings)
        {
            this.settings = settings;
        }

        public static CompanySettings Initialize(Dictionary<string, object> settings)
        {
            return new CompanySettings(settings);
        }

        public int YearStartMonth
        {
            get
            {
                try
                {
                    if (settings.ContainsKey("yearStartMonth"))
                    {
                        return (int)(long)settings["yearStartMonth"];
                    }
                    else
                        return 4;
                }
                catch (Exception)
                {
                    return 4;
                }
            }
        }

     

    }


}
