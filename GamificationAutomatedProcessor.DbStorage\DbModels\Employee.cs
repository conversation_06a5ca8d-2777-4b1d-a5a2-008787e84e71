﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text;

namespace GamificationAutomatedProcessor.DbStorage.DbModels
{
    [Table("ClientEmployees")]
    public class Employee
    {
        public long Id { get; set; }
        [Column("Deleted")]
        public bool IsDeactive { get; set; }
        [Column("Company")]
        public long CompanyId { get; set; }
    }
}
