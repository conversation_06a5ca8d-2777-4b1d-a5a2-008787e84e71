﻿using Libraries.CommonEnums;
using System;
using System.Collections.Generic;
using System.Text;

namespace GamificationProcessor.DbStorage.MasterDBModels
{
    public class CompanySetting
    {
        public long Id { get; set; }

        public string SettingKey { get; set; }

        public CompanySettingType SettingType { get; set; }

        public bool SendInApp { get; set; }

        public string DefaultValue { get; set; }

        public string MinVersionSupported { get; set; }

        public bool IsDeprecated { get; set; }


    }



    public class CompanySettingValue
    {
        public long Id { get; set; }

        public long SettingId { get; set; }

        public long CompanyId { get; set; }

        public string SettingValue { get; set; }

    }
}
