using Library.ResiliencyHelpers;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Threading.Tasks;

namespace Library.Tests
{
    [TestClass]
    public class ResiliencyHelpersTest
    {
        private ResilientAction resilientAction;
        private static int retryCount = 0;

        [TestInitialize]
        public void Initialise()
        {
            retryCount = 0;
        }


        [TestMethod]
        public async Task TaskResiliency()
        {
            //await resilientAction.RetryResilientlyAsync(DemoTaskAsync);
            var maxRetry = 3;
            resilientAction = new ResilientAction(maxRetry, TimeSpan.FromSeconds(2));
            try
            {
                await resilientAction.RetryResilientlyAsync(DemoTaskWithParams, 0);
                Assert.Fail();
            }
            catch (Exception ex)
            {
                Assert.AreEqual(ex.Message, $"ErrorParams {maxRetry}");
            }
        }
        private async Task DemoTaskAsyncTwoParams(int x, string y)
        {
            await Task.CompletedTask;
            throw (new Exception($"Error {retryCount++}"));
        }
        private async Task DemoTaskWithParams(int x)
        {
            await Task.CompletedTask;
            throw (new Exception($"ErrorParams {retryCount++}"));
        }



    }
}
