﻿using Libraries.CommonEnums;
using System.Collections.Generic;

namespace Libraries.PerspectiveColumns.Interface
{
    public interface IPerspective
    {
        List<PerspectiveColumnModel> Columns { get; set; }
    }

    public class PerspectiveBuilder
    {
        public static List<ViewPerspective> PerspectivesWithColorColorCoding = new List<ViewPerspective> { ViewPerspective.EmpGeoPerformanceData, ViewPerspective.ProductWiseSales, ViewPerspective.NoSalesReason, ViewPerspective.TrendReportNLT, ViewPerspective.TrendReportLT, ViewPerspective.MTStockAndSales };
        public static List<ViewPerspective> PerspectivesWithDrillDown= new List<ViewPerspective> { ViewPerspective.EmpGeoPerformanceData, ViewPerspective.ProductWiseSales, ViewPerspective.NoSalesReason, ViewPerspective.MTStockAndSales };
        public static List<ChartType> ChartsWithColorCoding= new List<ChartType> { ChartType.FlatTable, ChartType.FullPageTableLT, ChartType.FullPageTableNLT, ChartType.GroupedNoLimit, ChartType.OneNumber, ChartType.TwoNumber, ChartType.Bar };
        public static bool ColorCodingIsApplicable(ViewPerspective perspective, ChartType chartType)
        {
            if (PerspectivesWithColorColorCoding.Contains(perspective))
            {
                return perspective == ViewPerspective.EmpGeoPerformanceData || ChartsWithColorCoding.Contains(chartType);
            }
            else
                return false;
        }        
        public static bool DrillDownIsApplicable(ViewPerspective perspective, ChartType chartType)
        {
            if (PerspectivesWithDrillDown.Contains(perspective))
            {
                return perspective == ViewPerspective.EmpGeoPerformanceData || chartType == ChartType.FlatTable 
                        || (perspective == ViewPerspective.MTStockAndSales && chartType == ChartType.Bar);
            }
            else
                return false;
        }
        public static IPerspective GetPerspectiveFor(ViewPerspective viewPerspective, long companyId)
        {
            switch (viewPerspective)
            {
                case ViewPerspective.ProductWiseSales:
                    return new ProductSalePerspective(companyId);
                case ViewPerspective.DayStart:
                    return new DayStartPerspective(companyId);
                case ViewPerspective.NoSalesReason:
                    return new NoSalesReasonPerspective(companyId);
                case ViewPerspective.LiveSalesData:
                    return new LiveDataSalesPerspective(companyId);
                case ViewPerspective.MasterData:
                    return new MasterDataPerspective();

                case ViewPerspective.MTStockAndSales:
                    return new MTProductSalePerspective(companyId);
                case ViewPerspective.HistoricalSalesData:
                    return new HistoricalSalesDataPerspective(companyId);
                case ViewPerspective.TrendReportNLT:
                    return new TrendReportPerspective(companyId);
                case ViewPerspective.TrendReportLT:
                    return new TrendReportLTPerspective(companyId);
                case ViewPerspective.EmpGeoPerformanceData:
                    return new EmpGeoPerformanceModel(companyId);
                default:
                    return new ProductSalePerspective(companyId);
            }
        }
    }
}
