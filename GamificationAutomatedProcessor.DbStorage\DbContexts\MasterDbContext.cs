﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Threading.Tasks;
using System.Threading;
using GamificationAutomatedProcessor.DbStorage.DbModels;

namespace GamificationAutomatedProcessor.DbStorage.DbContexts
{
    public class MasterDbContext : DbContext
    {
        public MasterDbContext(DbContextOptions<MasterDbContext> options) : base(options)
        {

        }
              
        public DbSet<Employee> Employees { get; set; }
        public DbSet<Game> Games { get; set; }
        public DbSet<KPI> KPIs { get; set; }
        public DbSet<Team> Teams { get; set; }
        public DbSet<TargetForTeams> TargetForTeams { get; set; }
        public DbSet<CoinsforKpi> CoinsforKpi { get; set; }
        public DbSet<TeamUserMapping> TeamUserMappings { get; set; }
        public DbSet<KPISlab> KPISlabs { get; set; }
        public DbSet<PositionCodeEntityMapping> PositionCodeEntityMappings { get; set; }


        public override int SaveChanges()
        {
            throw new NotImplementedException();
        }

        public override int SaveChanges(bool acceptAllChangesOnSuccess)
        {
            throw new NotImplementedException();
        }

        public override Task<int> SaveChangesAsync(bool acceptAllChangesOnSuccess, CancellationToken cancellationToken = default(CancellationToken))
        {
            throw new NotImplementedException();
        }

        public override Task<int> SaveChangesAsync(CancellationToken cancellationToken = default(CancellationToken))
        {
            throw new NotImplementedException();
        }


        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {

        }
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
        }
    }
}
