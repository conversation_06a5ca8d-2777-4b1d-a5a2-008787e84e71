﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Libraries.CommonEnums
{
    public enum ManagerAlertAction
    {
        Pending,
        Approved,
        Disapproved,
        Archived
    }
    public enum AlertType
    {
        StarKRA,
        TourPlanCreation,
        OutletUpdation,
        LateDayStart,
        LateDayEnd,
        RoutePlanRequest,
        NewOutletCreationRequest,
        OTP,
        OTPNSApp,
        OpportunityOutletRequest,
        ActionCardResponseApprovalRequest,
        OutletVerification,
        TADAApprovals,
        DeadOutletRequest,
        NotificationSummary,
        RegulariseAttendance
    }

    public enum OTPRequestType
    {
        JourneyDiversion,
        OrderValidation
    }
}
