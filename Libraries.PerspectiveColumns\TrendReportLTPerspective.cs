﻿using Libraries.CommonEnums;
using Libraries.PerspectiveColumns;
using Libraries.PerspectiveColumns.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace Libraries.PerspectiveColumns
{
    public class TrendReportLTPerspective : IPerspective
    {
        public TrendReportLTPerspective(long companyId)
        {
        }
        public List<PerspectiveColumnModel> Columns
        {
            get
            {
                return new List<PerspectiveColumnModel>
                {
                    // Name Property Needs to match the key used for Nomenclature For QuickViz
                    new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = "GlobalSalesManager   ", Name = "GSM"         , IsMeasure = false, IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.Unknown, IsOtherMeasure = false, IsPivot = false},
                    new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = "NationalSalesManager ", Name = "NSM"         , IsMeasure = false, IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.Unknown, IsOtherMeasure = false, IsPivot = false},
                    new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = "ZonalSalesManager    ", Name = "ZSM"         , IsMeasure = false, IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.Unknown, IsOtherMeasure = false, IsPivot = false},
                    new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = "RegionalSalesManager ", Name = "RSM"         , IsMeasure = false, IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.Unknown, IsOtherMeasure = false, IsPivot = false},
                    new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = "AreaSalesManager     ", Name = "ASM"         , IsMeasure = false, IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.Unknown, IsOtherMeasure = false, IsPivot = false},
                    new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = "Employee             ", Name = "ESM"    , IsMeasure = false, IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.Unknown, IsOtherMeasure = false                 },
                    new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = "FieldUser HQ"                 , Name = "UserHQ" , IsMeasure = false, IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.Unknown                                              },
                    new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = "FieldUser ErpId"                 , Name = "UserERPID" , IsMeasure = false, IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.Unknown                                              },
                    new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = "FieldUser Designation"                 , Name = "UserDesignation" , IsMeasure = false, IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.Unknown                                              },
                    new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = "Rank"                 , Name = "FieldUserRank" , IsMeasure = false, IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.Unknown                                              },
                    new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = "User Status" , Name = "UserStatus"         , IsMeasure = false, IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.Unknown, IsOtherMeasure = false, IsPivot = false},
                    new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName ="Active Days"    , Name = "ActiveDays"         , IsMeasure = false, IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.Unknown, IsOtherMeasure = false, IsPivot = false},
                    new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = "FieldUser Region" , Name = "FieldUserRegion"         , IsMeasure = false, IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.Unknown, IsOtherMeasure = false, IsPivot = false},
                    new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName ="FieldUser Zone"    , Name = "FieldUserZone"         , IsMeasure = false, IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.Unknown, IsOtherMeasure = false, IsPivot = false},
                   
                    // Name Property Of New Positions need to be in this Pattern (LXPosition) only or Filtering based on setting won't work
                    new PerspectiveColumnModel() { Attribute = "Position"        , DisplayName = "L8Position"             , Name = "L8Position"       , IsMeasure = false , IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsPivot = false , IsOtherMeasure = false },
                    new PerspectiveColumnModel() { Attribute = "Position"        , DisplayName = "L7Position"             , Name = "L7Position"       , IsMeasure = false , IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsPivot = false , IsOtherMeasure = false },
                    new PerspectiveColumnModel() { Attribute = "Position"        , DisplayName = "L6Position"             , Name = "L6Position"       , IsMeasure = false , IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsPivot = false , IsOtherMeasure = false },
                    new PerspectiveColumnModel() { Attribute = "Position"        , DisplayName = "L5Position"             , Name = "L5Position"       , IsMeasure = false , IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsPivot = false , IsOtherMeasure = false },
                    new PerspectiveColumnModel() { Attribute = "Position"        , DisplayName = "L4Position"             , Name = "L4Position"       , IsMeasure = false , IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsPivot = false , IsOtherMeasure = false },
                    new PerspectiveColumnModel() { Attribute = "Position"        , DisplayName = "L3Position"             , Name = "L3Position"       , IsMeasure = false , IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsPivot = false , IsOtherMeasure = false },
                    new PerspectiveColumnModel() { Attribute = "Position"        , DisplayName = "L2Position"             , Name = "L2Position"       , IsMeasure = false , IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsPivot = false , IsOtherMeasure = false },
                    new PerspectiveColumnModel() { Attribute = "Position"        , DisplayName = "L1Position"             , Name = "L1Position"       , IsMeasure = false , IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsPivot = false , IsOtherMeasure = false },
                    new PerspectiveColumnModel() { Attribute = "Sales Territory" , DisplayName = "Level7    "             , Name = "Level7"           , IsMeasure = false , IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsPivot = false , IsOtherMeasure = false },
                    new PerspectiveColumnModel() { Attribute = "Sales Territory" , DisplayName = "Level6    "             , Name = "Level6"           , IsMeasure = false , IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsPivot = false , IsOtherMeasure = false },
                    new PerspectiveColumnModel() { Attribute = "Sales Territory" , DisplayName = "Level5    "             , Name = "Level5"           , IsMeasure = false , IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsPivot = false , IsOtherMeasure = false },
                    new PerspectiveColumnModel() { Attribute = "Sales Territory" , DisplayName = "Zone      "             , Name = "Zone"             , IsMeasure = false , IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsPivot = false , IsOtherMeasure = false },
                    new PerspectiveColumnModel() { Attribute = "Sales Territory" , DisplayName = "Region    "             , Name = "Region"           , IsMeasure = false , IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsPivot = false , IsOtherMeasure = false },
                    new PerspectiveColumnModel() { Attribute = "Sales Territory" , DisplayName = "Territory "             , Name = "Territory"        , IsMeasure = false , IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.CountDistinct                                            },
                    
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "TC"                               , Name = "WeeklyTC"                      , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "PC"                               , Name = "WeeklyPC"                      , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Avg. TC (per day)"                , Name = "WeeklyAvgTCPerday"             , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Avg. PC (per day)"                , Name = "WeeklyAvgPCPerday"             , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "UTC"                              , Name = "WeeklyUTC"                     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "UPC"                              , Name = "WeeklyUPC"                     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Order Qty (Unit)"                 , Name = "WeeklyOrderInUnits"            , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Order Qty (Sup Unit)"             , Name = "WeeklySalesInSupUnit"          , IsMeasure = true, IsDimension = false,   PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Value"                            , Name = "WeeklyValue"                   , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "NetValue"                         , Name = "WeeklyNetValue"                , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Order Qty (Std Unit)"             , Name = "WeeklySalesInStdUnit"          , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Net Value (Dispatch)"             , Name = "WeeklyDispatchInRevenue"       , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Dispatch Qty (Std Unit)"          , Name = "WeeklyDispatchInStdUnits"      , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Dispatch Qty (Unit)"              , Name = "WeeklyDispatchInUnits"         , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "AvgValuePerCall"                  , Name = "WeeklyDropSize"                , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Total Lines"                      , Name = "WeeklyTotalLines"              , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LPC"                              , Name = "WeeklyLPC"                     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Ach"                              , Name = "WeeklyAchievement"             , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Ach(Dispatch)"                    , Name = "WeeklyDispatchAchievement"     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Days Retailing"                   , Name = "WeeklyDaysRetailing"           , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "SC"                               , Name = "WeeklySC"                      , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "New Outlets"                      , Name = "WeeklyNewOutlets"              , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "New Outlets Value"                , Name = "WeeklyNewOutletsValue"         , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Avg Value Per New Outlet"         , Name = "WeeklyValuePerNewOutlet"       , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "FO Qty (Unit)"                    , Name = "WeeklyFOQty"                   , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "FO Qty (Std Unit)"                , Name = "WeeklyFOStdQty"                , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "FO Value"                         , Name = "WeeklyFOValue"                 , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Focus Product Order Qty (Unit)"   , Name = "WeeklyFocProdOrderQtyUnit"       , IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"  , DisplayName = "Focus Product Order Qty (Std Unit)", Name = "WeeklyFocProdOrderQtyStd"        , IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"  , DisplayName = "Focus Product Order Qty (Sup Unit)", Name = "WeeklyFocProdOrderQtySup"        , IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Avg Retailing Time"               , Name = "WeeklyAvgRetailingTime"        , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Total Users Count"                , Name = "WeeklyTotalUsersCount"         , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Active Users Count"               , Name = "WeeklyActiveUserCount"         , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Avg Day Start Time"               , Name = "WeeklyAvgDayStartTime"         , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Avg First Call Time"              , Name = "WeeklyAvgFirstCallTime"        , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "OVT"                              , Name = "WeeklyOVT"                     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "OVC"                              , Name = "WeeklyOVC"                     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "FocusedOutlet UTC"                , Name = "WeeklyFocusedOutletsUTC"       , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "FocusedOutlet UPC"                , Name = "WeeklyFocusedOutletsUPC"       , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Working Days"                     , Name = "WeeklyWorkingDays"             , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },

                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "TC"                               , Name = "MonthlyTC"                      , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "PC"                               , Name = "MonthlyPC"                      , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Avg. TC (per day)"                , Name = "MonthlyAvgTCPerday"             , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Avg. PC (per day)"                , Name = "MonthlyAvgPCPerday"             , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "UTC"                              , Name = "MonthlyUTC"                     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "UPC"                              , Name = "MonthlyUPC"                     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Order Qty (Unit)"                 , Name = "MonthlyOrderInUnits"            , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Order Qty (Sup Unit)"             , Name = "MonthlySalesInSupUnit"          , IsMeasure = true, IsDimension = false,   PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Value"                            , Name = "MonthlyValue"                   , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "NetValue"                         , Name = "MonthlyNetValue"                , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Order Qty (Std Unit)"             , Name = "MonthlySalesInStdUnit"          , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Net Value (Dispatch)"             , Name = "MonthlyDispatchInRevenue"       , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Dispatch Qty (Std Unit)"          , Name = "MonthlyDispatchInStdUnits"      , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Dispatch Qty (Unit)"              , Name = "MonthlyDispatchInUnits"         , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "AvgValuePerCall"                  , Name = "MonthlyDropSize"                , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Total Lines"                      , Name = "MonthlyTotalLines"              , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LPC"                              , Name = "MonthlyLPC"                     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Ach"                              , Name = "MonthlyAchievement"             , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Ach(Dispatch)"                    , Name = "MonthlyDispatchAchievement"     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Days Retailing"                   , Name = "MonthlyDaysRetailing"           , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "SC"                               , Name = "MonthlySC"                      , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "New Outlets"                      , Name = "MonthlyNewOutlets"              , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "New Outlets Value"                , Name = "MonthlyNewOutletsValue"         , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Avg Value Per New Outlet"         , Name = "MonthlyValuePerNewOutlet"       , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "FO Qty (Unit)"                    , Name = "MonthlyFOQty"                   , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "FO Qty (Std Unit)"                , Name = "MonthlyFOStdQty"                , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "FO Value"                         , Name = "MonthlyFOValue"                 , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Focus Product Order Qty (Unit)"   , Name = "MonthlyFocProdOrderQtyUnit"       , IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"  , DisplayName = "Focus Product Order Qty (Std Unit)", Name = "MonthlyFocProdOrderQtyStd"        , IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"  , DisplayName = "Focus Product Order Qty (Sup Unit)", Name = "MonthlyFocProdOrderQtySup"        , IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Avg Retailing Time"               , Name = "MonthlyAvgRetailingTime"        , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Total Users Count"                , Name = "MonthlyTotalUsersCount"         , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Active Users Count"               , Name = "MonthlyActiveUserCount"         , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Avg Day Start Time"               , Name = "MonthlyAvgDayStartTime"         , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Avg First Call Time"              , Name = "MonthlyAvgFirstCallTime"        , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "OVT"                              , Name = "MonthlyOVT"                     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "OVC"                              , Name = "MonthlyOVC"                     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "FocusedOutlet UTC"                , Name = "MonthlyFocusedOutletsUTC"       , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "FocusedOutlet UPC"                , Name = "MonthlyFocusedOutletsUPC"       , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Working Days"                     , Name = "MonthlyWorkingDays"             , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },

                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "TC"                               , Name = "QuarterlyTC"                      , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "PC"                               , Name = "QuarterlyPC"                      , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Avg. TC (per day)"                , Name = "QuarterlyAvgTCPerday"             , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Avg. PC (per day)"                , Name = "QuarterlyAvgPCPerday"             , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "UTC"                              , Name = "QuarterlyUTC"                     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "UPC"                              , Name = "QuarterlyUPC"                     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Order Qty (Unit)"                 , Name = "QuarterlyOrderInUnits"            , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Order Qty (Sup Unit)"             , Name = "QuarterlySalesInSupUnit"          , IsMeasure = true, IsDimension = false,   PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Value"                            , Name = "QuarterlyValue"                   , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "NetValue"                         , Name = "QuarterlyNetValue"                , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Order Qty (Std Unit)"             , Name = "QuarterlySalesInStdUnit"          , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Net Value (Dispatch)"             , Name = "QuarterlyDispatchInRevenue"       , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Dispatch Qty (Std Unit)"          , Name = "QuarterlyDispatchInStdUnits"      , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Dispatch Qty (Unit)"              , Name = "QuarterlyDispatchInUnits"         , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "AvgValuePerCall"                  , Name = "QuarterlyDropSize"                , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Total Lines"                      , Name = "QuarterlyTotalLines"              , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LPC"                              , Name = "QuarterlyLPC"                     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Ach"                              , Name = "QuarterlyAchievement"             , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Ach(Dispatch)"                    , Name = "QuarterlyDispatchAchievement"     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Days Retailing"                   , Name = "QuarterlyDaysRetailing"           , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "SC"                               , Name = "QuarterlySC"                      , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "New Outlets"                      , Name = "QuarterlyNewOutlets"              , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "New Outlets Value"                , Name = "QuarterlyNewOutletsValue"         , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Avg Value Per New Outlet"         , Name = "QuarterlyValuePerNewOutlet"       , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "FO Qty (Unit)"                    , Name = "QuarterlyFOQty"                   , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "FO Qty (Std Unit)"                , Name = "QuarterlyFOStdQty"                , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "FO Value"                         , Name = "QuarterlyFOValue"                 , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Focus Product Order Qty (Unit)"   , Name = "QuarterlyFocProdOrderQtyUnit"       , IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"  , DisplayName = "Focus Product Order Qty (Std Unit)", Name = "QuarterlyFocProdOrderQtyStd"        , IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"  , DisplayName = "Focus Product Order Qty (Sup Unit)", Name = "QuarterlyFocProdOrderQtySup"        , IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Avg Retailing Time"               , Name = "QuarterlyAvgRetailingTime"        , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Total Users Count"                , Name = "QuarterlyTotalUsersCount"         , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Active Users Count"               , Name = "QuarterlyActiveUserCount"         , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Avg Day Start Time"               , Name = "QuarterlyAvgDayStartTime"         , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Avg First Call Time"              , Name = "QuarterlyAvgFirstCallTime"        , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "OVT"                              , Name = "QuarterlyOVT"                     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "OVC"                              , Name = "QuarterlyOVC"                     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "FocusedOutlet UTC"                , Name = "QuarterlyFocusedOutletsUTC"       , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "FocusedOutlet UPC"                , Name = "QuarterlyFocusedOutletsUPC"       , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Working Days"                     , Name = "QuarterlyWorkingDays"             , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },

                    new PerspectiveColumnModel() { Attribute = "Master Measure"  , DisplayName = "Employee Overall Target"          , Name = "Targets"                 , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value },
                    new PerspectiveColumnModel() { Attribute = "Master Measure"  , DisplayName = "Total Outlets"                    , Name = "Outlets"                 , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value },
                    new PerspectiveColumnModel() { Attribute = "Master Measure"  , DisplayName = "Total Beats"                      , Name = "Beats"                   , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value },
                    new PerspectiveColumnModel() { Attribute = "Master Measure"  , DisplayName = "Total Routes"                     , Name = "Routes"                  , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value },
                    new PerspectiveColumnModel() { Attribute = "Master Measure"  , DisplayName = "Total Distributors"               , Name = "Distributors"            , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value },
                    new PerspectiveColumnModel() { Attribute = "Master Measure"  , DisplayName = "FocusedOutlet Count"              , Name = "TotalFocusedOutlets"     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value },
                };
            }
        }

        List<PerspectiveColumnModel> IPerspective.Columns { get => Columns; set => throw new NotImplementedException(); }
    }
}