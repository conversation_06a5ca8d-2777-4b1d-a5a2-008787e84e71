﻿using Libraries.CommonEnums;
using System.Collections.Generic;
using System.Linq;

namespace Libraries.PerspectiveColumns
{
    public static class ComarisonInCharts
    {
        public class ComparisonColumnModel
        {
            public ComparisonType ComparisonType { get; set; }
            public ComparisonTimePeriod ComparisonTimePeriod { get; set; }
            public DateRangePreset DateRangePreset { get; set; }
        }
        public static List<ComparisonColumnModel> GetComparisonColumns(DateRangePreset dateRangePreset)
        {
            return Columns.Where(s => s.DateRangePreset == dateRangePreset).ToList();
        }
        public static List<ComparisonColumnModel> Columns
        {
            get
            {
                return new List<ComparisonColumnModel>
                {
                    new ComparisonColumnModel() {ComparisonTimePeriod = ComparisonTimePeriod.Month, DateRangePreset = DateRangePreset.MTD, ComparisonType= ComparisonType.LastMonth},
                    new ComparisonColumnModel() {ComparisonTimePeriod = ComparisonTimePeriod.Week, DateRangePreset = DateRangePreset.Yesterday, ComparisonType= ComparisonType.LastWeek},
                    new ComparisonColumnModel() {ComparisonTimePeriod = ComparisonTimePeriod.Week, DateRangePreset = DateRangePreset.Yesterday, ComparisonType= ComparisonType.LastMonthCurrentWeek},
                    new ComparisonColumnModel() {ComparisonTimePeriod = ComparisonTimePeriod.Day, DateRangePreset = DateRangePreset.Yesterday, ComparisonType= ComparisonType.Yesterday},
                    new ComparisonColumnModel() {ComparisonTimePeriod = ComparisonTimePeriod.Day, DateRangePreset = DateRangePreset.Yesterday, ComparisonType= ComparisonType.LastMonthCurrentDay},
                    //new ComparisonColumnModel() {ComparisonTimePeriod = ComparisonTimePeriod.Day, DateRangePreset = DateRangePreset.Yesterday, ComparisonType= ComparisonType.ThisMonthAverage},
                };
            }
        }
    }
}
