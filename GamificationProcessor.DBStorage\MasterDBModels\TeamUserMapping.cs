﻿using Libraries.CommonEnums;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text;

namespace GamificationProcessor.DBStorage.MasterDBModels
{
    [Table("TeamUserMappings")]
    public class TeamUserMapping
    {
        public long Id { get; set; }

        [ForeignKey("Team")]
        public long TeamId { get; set; }

       // [ForeignKey("ClientEmployee")]
        public long TeamPlayerId { get; set; }

        public bool IsActive { get; set; }

        public long CompanyId { get; set; }
        public DateTime CreatedAt { get; set; }
        public string CreationContext { get; set; }
        public DateTime LastUpdatedAt { get; set; }

       // public ClientEmployee ClientEmployee { get; set; }
        public Teams Team { get; set; }
    }
}
