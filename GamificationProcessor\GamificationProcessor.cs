﻿using GamificationProcessor.Configuration;
using GamificationProcessor.Core.Models;
using GamificationProcessor.Core.Services;
using Library.AsyncLock;
using Library.ResiliencyHelpers;
using Library.SlackService;
using Microsoft.Azure.WebJobs;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using System;
using System.Threading.Tasks;

namespace GamificationProcessor
{
    public class GamificationProcessor
    {
        private readonly IServiceProvider serviceProvider;
        private readonly ResilientAction resilientAction;
        private readonly ErrorMessenger errorMessenger;

        public GamificationProcessor(IServiceProvider serviceProvider, ResilientAction resilientAction, ErrorMessenger errorMessenger)
        {
            this.serviceProvider = serviceProvider;
            this.resilientAction = resilientAction;
            this.errorMessenger = errorMessenger;
        }

        public async Task ProcessGamificationQueue([QueueTrigger(Dependencies.GamificationQueue)]GamificationQueueEvent request)
        {
            await Process(request);
        }

        private async Task Process(GamificationQueueEvent request)
        {

            try
            {
                using (var scope = serviceProvider.CreateScope())
                {
                    var gamificationProcessor = scope.ServiceProvider.GetRequiredService<IGamificationProcessor>();
                    await resilientAction.RetryResilientlyAsync(gamificationProcessor.Process, request);
                }

            }
            catch (MaxLockAttemptExceededException ex)
            {
                await errorMessenger.SendToSlack(ex, $"{JsonConvert.SerializeObject(request)} : {ex.GetBaseException().Message}");
                throw;
            }
            catch (Exception ex)
            {
                await errorMessenger.SendToSlack(ex, $"{JsonConvert.SerializeObject(request)} : {ex.GetBaseException().Message}");
                throw;
            }
        }
    }

}
