﻿namespace Library.ConnectionStringParsor
{
    public class CosmosConnectionString : ConnectionStringBase
    {
        public string AccountEndpoint { get; private set; }
        public string AccountKey { get; private set; }

        public static CosmosConnectionString GetConnection(string connString)
        {
            return new CosmosConnectionString(connString);
        }
        private CosmosConnectionString(string connectionString) : base(connectionString)
        {
            var parsedString = base.Parse();
            if (parsedString.ContainsKey("accountendpoint"))
            {
                AccountEndpoint = parsedString["accountendpoint"];
            }
            else
            {
                throw new System.Exception("MalFormed Connection String: 'accountendpoint' is compulsory");
            }

            if (parsedString.ContainsKey("accountkey"))
            {
                AccountKey = parsedString["accountkey"];
            }
            else
            {
                throw new System.Exception("MalFormed Connection String: 'accountkey' is compulsory");
            }
        }
    }
}
