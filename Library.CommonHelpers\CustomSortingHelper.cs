﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Library.CommonHelpers
{
    public class CustomStringComparer : IComparer<string>
    {
        private readonly Dictionary<string, int> rankDict;
        private int Index { get; set; }
        private bool InAscendingOrder { get; set; }
        public CustomStringComparer()
        {
            var rankDict = new Dictionary<string, int> {
                { "January", 0 },
                { "February", 1 },
                { "March", 2 },
                { "April", 3 },
                { "May", 4 },
                { "June", 5 },
                { "July", 6 },
                { "August", 7 },
                { "September", 8},
                { "October", 9 },
                { "November", 10},
                { "December", 11 },
            };
            this.rankDict = rankDict;
        }
        public CustomStringComparer(Dictionary<string, int> rankDict)
        {
            this.rankDict = rankDict;

        }
        //Date: Dec 2,2020; Asana:https://app.asana.com/0/139097763031412/1198706577074223/f; Reason: Added the custom sorting logic with a paramter indicating whether the sorting is to be done in ascending / descending order 
        public CustomStringComparer(bool InAscendingOrder = true)
        {
            this.InAscendingOrder = InAscendingOrder;
            var rankDict = new Dictionary<string, int> {
            { "January", 0 },
            { "February", 1 },
            { "March", 2 },
            { "April", 3 },
            { "May", 4 },
            { "June", 5 },
            { "July", 6 },
            { "August", 7 },
            { "September", 8},
            { "October", 9 },
            { "November", 10},
            { "December", 11 },
            };
            this.rankDict = rankDict;

        }

        public int Compare(string x, string y)
        {
            if (Index == 0 && x.Equals(y))
            {
                return 0;
            }

            var isXInt = int.TryParse(x, out int xIntValue);
            var isYInt = int.TryParse(y, out int yIntValue);
            if (isXInt && isYInt)
            {
                Index = 0;
                return InAscendingOrder ? ( xIntValue - yIntValue) : -(xIntValue - yIntValue);
            }

            var isXDouble = double.TryParse(x, out double xDoubleValue);
            var isYDouble = double.TryParse(y, out double yDoubleValue);
            if (isXDouble && isYDouble)
            {
                Index = 0;
                if(xDoubleValue == yDoubleValue)
                {
                    return 0;
                }
                return InAscendingOrder ? ((xDoubleValue - yDoubleValue) > 0 ? 1 : -1) : -((xDoubleValue - yDoubleValue) > 0 ? 1 : -1);
            }
            else if (rankDict.ContainsKey(x) && rankDict.ContainsKey(y))
            {
                Index = 0;
                return rankDict[x] - rankDict[y];
            }
            else
            {
                char a;
                char b;
                a = x.Count() > Index ? x[Index] : x.LastOrDefault();
                b = y.Count() > Index ? y[Index] : y.LastOrDefault();

                if (!a.Equals(b))
                {
                    Index = 0;
                    return (int)a - (int)b;
                }
                Index++;
                return Compare(x, y);
            }
        }
    }
}
