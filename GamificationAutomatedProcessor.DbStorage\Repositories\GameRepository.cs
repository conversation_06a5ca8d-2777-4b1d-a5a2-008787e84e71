﻿using GamificationAutomatedProcessor.Core.Models;
using GamificationAutomatedProcessor.Core.Repositories;
using GamificationAutomatedProcessor.DbStorage.DbContexts;
using Library.DateTimeHelpers;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GamificationAutomatedProcessor.DbStorage.Repositories
{
    public class GameRepository : IGameRepository
    {
        private readonly MasterDbContext dbContext;
        private readonly WritableTransactionDbContext transactionDbContext;
        private readonly IPositionCodeEntityMappingRepository pcrepo;

        public GameRepository(MasterDbContext dbContext, WritableTransactionDbContext transactionDbContext, IPositionCodeEntityMappingRepository pcrepo)
        {
            this.dbContext = dbContext;
            this.transactionDbContext = transactionDbContext;
            this.pcrepo = pcrepo;
        }

        public async Task<List<Employee>> GetActiveEmployeesInTeam(long teamId)
        {
            var teamMembers = await dbContext.TeamUserMappings.Where(t => t.TeamId == teamId && t.IsActive).Select(x => x.TeamPlayerId).ToListAsync();
            return await dbContext.Employees.Where(x => teamMembers.Contains(x.Id) && !x.IsDeactive).Select
                (e => new Employee
                {
                    Id = e.Id,
                    CompanyId = e.CompanyId
                }).ToListAsync();
        }

        public async Task<List<Team>> GetActiveTeamsForGame(long gameId)
        {
            var teamIds = await dbContext.TargetForTeams.Where(t => t.GameId == gameId && t.IsActive).Select(t => t.TeamId).Distinct().ToListAsync();
            return await dbContext.Teams.Where(t => teamIds.Contains(t.Id) && t.IsActive)
                .Select(t => new Team
                {
                    Id = t.Id,
                    Name = t.Name,
                    CompanyId = t.CompanyId
                }).ToListAsync();
        }

        public async Task<List<Game>> GetAllActiveGames(DateTime today)
        {
            return await dbContext.Games.Where(g => g.EndDate >= today && g.IsActive
            && g.StartDate <= today)
                .Select(x => new Game
                {
                    Id = x.Id,
                    CompanyId = x.CompanyId,
                    Name = x.Name,
                    StartDate = x.StartDate,
                    EndDate = x.EndDate
                }).ToListAsync();
        }

        public async Task<Dictionary<long, CoinsforKpi>> GetMonthlyKPIsCoinsForTeam(long gameId, List<long> kpiIds)
        {
            return await dbContext.CoinsforKpi.Where(c => c.GameId == gameId && kpiIds.Contains(c.KpiId)).Select
                (coins => new CoinsforKpi
                {
                    GameId = coins.GameId,
                    Coins = coins.Coins,
                    KpiId = coins.KpiId
                }).ToDictionaryAsync(x => x.KpiId, x => x);
        }

        public async Task<List<KPI>> GetMonthlyKPIsForGame(long gameId)
        {
            return await dbContext.TargetForTeams.Include(t => t.KPI).Where(x => x.GameId == gameId && x.KPI.Frequency == DbModels.Frequency.GamePeriod && !x.KPI.IsDeactivated && x.IsActive)
                .Select(x => new KPI
                {
                    Id = x.KPI.Id,
                    Measure = x.KPI.Measure,
                    IsQualifier = x.KPI.IsQualifier
                }).Distinct().ToListAsync();
        }

        public async Task<Dictionary<long, TargetForTeams>> GetMonthlyKPIsTargetForTeam(long teamId,long gameId, List<long> kpiIds)
        {
            return await dbContext.TargetForTeams.Include(x => x.KPISlabs).Where(t => t.IsActive && t.TeamId == teamId && kpiIds.Contains(t.KpiId) && t.GameId == gameId)
                .Select(x => new TargetForTeams
                {
                    GameId = x.GameId,
                    Target = x.Target,
                    TeamId = x.TeamId,
                    KpiId = x.KpiId,
                    IsContinuous = x.IsContinuous,
                    KPISlabs = x.KPISlabs.Select(y => new KPISlab
                    {
                        Id = y.Id,
                        SlabCoins = y.SlabCoins,
                        SlabPayout = y.SlabPayout,
                        SlabTarget = y.SlabTarget
                    }).ToList()
                }).ToDictionaryAsync(t => t.KpiId, t => t);
        }

        public async Task<MinGameKPIStat> SaveKPIUserStats(GameKPIStat gameKPIStats, long companyId, long employeeId, DateTime gameApiDate)
        {
            var exisitingStat = transactionDbContext.GameKPIStats.Where(s => s.CompanyId == companyId
            && s.EsmId == employeeId
            && s.KPIId == gameKPIStats.KPIId
            && s.GameId == gameKPIStats.GameId
            && s.TeamId == gameKPIStats.TeamId
            && s.DateKey == DateTimeExtentions.GetDateKey(gameApiDate)).FirstOrDefault();
            var stat = GetDbGameKPIStats(gameKPIStats, companyId, employeeId, gameApiDate);
            if (exisitingStat != null)
            {
                exisitingStat.CoinsEarned = stat.CoinsEarned > exisitingStat.CoinsEarned ? stat.CoinsEarned : exisitingStat.CoinsEarned;
                exisitingStat.TeamId = stat.TeamId;
                exisitingStat.KPITarget = stat.KPITarget;
                exisitingStat.KPIAchievedValue = stat.KPIAchievedValue;
                exisitingStat.IsQualifier = stat.IsQualifier;
                exisitingStat.IsKPIAchieved = stat.IsKPIAchieved;
                exisitingStat.IsUserQualified = stat.IsUserQualified;
                exisitingStat.IsSynched = false;
                exisitingStat.DateKey = DateTimeExtentions.GetDateKey(gameApiDate);
                exisitingStat.LastUpdatedAt = gameApiDate;
                exisitingStat.SlabId = stat.SlabId;
                exisitingStat.Payout = stat.Payout > exisitingStat.Payout ? stat.Payout : exisitingStat.Payout;
            }
            else
            {
                stat.PositionCodeId = await pcrepo.GetPositionCodeCorrespondingToUser(employeeId);
                await transactionDbContext.GameKPIStats.AddAsync(stat);
            }
            await transactionDbContext.SaveChangesAsync();
            if (exisitingStat != null)
            {
                stat.Id = exisitingStat.Id;
            }
            return new MinGameKPIStat() { Id = stat.Id, DateKey = stat.DateKey };
        }
        private DbModels.DbGameKPIStat GetDbGameKPIStats(GameKPIStat gameKPIStats, long companyId, long employeeId, DateTime today)
        {
            return new DbModels.DbGameKPIStat()
            {
                EsmId = employeeId,
                CompanyId = companyId,
                IsKPIAchieved = gameKPIStats.IsKPIAchieved,
                GameId = gameKPIStats.GameId,
                TeamId = gameKPIStats.TeamId,
                KPIId = gameKPIStats.KPIId,
                KPITarget = gameKPIStats.KPITarget,
                KPIAchievedValue = gameKPIStats.KPIAchievedValue,
                IsQualifier = gameKPIStats.IsQualifier,
                IsUserQualified = gameKPIStats.IsUserQualified,
                CoinsEarned = gameKPIStats.CoinsEarned,
                IsSynched = false,
                DateKey = DateTimeExtentions.GetDateKey(today),
                CreatedAt = today,
                LastUpdatedAt = today,
                SlabId = gameKPIStats.SlabId,
                Payout = gameKPIStats.Payout
            };
        }
    }
}

