﻿using Microsoft.Azure.WebJobs.Host;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using GamificationProcessor.Configuration;
using Di<PERSON>atchKPIGamification;

namespace GamificationDispatchKPIsProcessor
{
    class Program
    {
        public static async Task Main(string[] args)
        {
            DateTime date = DateTime.UtcNow.Date;
            var builder = new HostBuilder()
              .ConfigureAppConfiguration((config) =>
              {
                  var env = Environment.GetEnvironmentVariable("BuildEnvironment");
                  config.AddJsonFile($"appsettings.json", optional: true, reloadOnChange: true)
                  .AddJsonFile($"appsettings.{env}.json", optional: true, reloadOnChange: true)
                  .AddEnvironmentVariables();
              })
               .ConfigureServices((context, services) =>
               {
                   Dependencies.SetUp(services, context.Configuration);
               })
               .UseConsoleLifetime();
            var host = builder.Build();
            using (host)
            {
                await host.Services.GetRequiredService<DispatchKPIGamificationTrigger>().Process(date);
            }

        }
    }
}
