﻿using System;
using System.Collections.Generic;
using System.Text;

namespace GamificationProcessor.DBStorage.TransactionModels
{
    public class DbGameKPIStat
    {
        public long Id { get; set; }
        public long EsmId { get; set; }
        public long CompanyId { get; set; }
        public long GameId { get; set; }
        public long TeamId { get; set; }
        public DateTime CreatedAt { get; set; }
        public long KPIId { get; set; }
        public string KPITarget { get; set; }
        public string KPIAchievedValue { get; set; }
        public bool IsQualifier { get; set; }
        public bool IsKPIAchieved { get; set; }
        public long CoinsEarned { get; set; }
        public bool IsUserQualified { get; set; }
        public bool IsSynched { get; set; }
        public long DateKey { get; set; }
        public long? SlabId { get; set; }
        public long? PositionCodeId { get; set; }
        public long? Payout { get; set; }
    }
}
