﻿using Library.DateTimeHelpers;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FileGenerator
{
    public class FileStreamHelper
    {
        public FileStreamHelper()
        {

        }
        public string GenerateFile()
        {
            var reportId = Guid.NewGuid();
            string path = $@"{Path.GetTempPath()}{reportId.ToString()}";
            return path;
        }
        public string GenerateFile(string reportId)
        {
            string path = $@"{Path.GetTempPath()}{reportId}";
            return path;
        }
        public List<DateAndFile> GenerateDateAndFileListAndStream(string reportId, DateTime startDate, DateTime endDate)
        {
            List<DateAndFile> list = new List<DateAndFile>();
            for (var dt = startDate; dt < endDate; dt = dt.AddDays(1))
            {
                list.Add(new DateAndFile
                {
                    Date = dt,
                    Path = $@"{Path.GetTempPath()}{reportId.ToString()}_{dt.GetDateKey().ToString()}",
                    Stream = GetFileStream($@"{Path.GetTempPath()}{reportId.ToString()}_{dt.GetDateKey().ToString()}")
                });

            }
            return list;
        }
        public FilePathAndStream GenerateDateAndFileAndStream(string reportId)
        {
            return new FilePathAndStream
            {
                Path = $@"{Path.GetTempPath()}{reportId.ToString()}",
                Stream = GetFileStream($@"{Path.GetTempPath()}{reportId.ToString()}")
            };
        }
        public FilePathAndStream GenerateDateAndFileAndStream()
        {
            string reportId = Guid.NewGuid().ToString();
            return new FilePathAndStream
            {
                Path = $@"{Path.GetTempPath()}{reportId.ToString()}",
                Stream = GetFileStream($@"{Path.GetTempPath()}{reportId.ToString()}")
            };
        }
        public FileStream GetFileStream(string path)
        {
            // Delete the file if it exists.
            if (File.Exists(path))
            {
                File.Delete(path);
            }

            //Create the file.
            FileStream fs = File.Create(path);
            return fs;
        }

        public void DeleteFileIfExistAndCloseStream(DateAndFile fileDetails)
        {
            // Delete the file if it exists.
            if (File.Exists(fileDetails.Path))
            {
                File.Delete(fileDetails.Path);
            }
            fileDetails.Stream.Close();
        }
        public async Task MergeFiles(List<DateAndFile> fileList, FilePathAndStream localDest, Stream blobStream)
        {
            StreamWriter fileDest = new StreamWriter(localDest.Stream);

            bool isFirst = true;
            foreach (var file in fileList)
            {

                string[] lines = File.ReadAllLines(file.Path);
                DeleteFileIfExistAndCloseStream(file);

                if (isFirst)
                {
                    lines = lines.Skip(1).ToArray(); // Skip header row for all but first file
                }

                foreach (string line in lines)
                {
                    await fileDest.WriteLineAsync(line);
                }
                isFirst = false;
            }

            localDest.Stream.Flush();
            localDest.Stream.Position = 0;
            localDest.Stream.CopyTo(blobStream);
            localDest.Stream.Close();
            if (blobStream != null)
            {
                blobStream.Flush();
                if (blobStream.CanSeek)
                {
                    blobStream.Position = 0;
                }
                else
                {
                    blobStream.Close();
                }
            }
            // delete local temp file after wrting to blob
            if (File.Exists(localDest.Path))
            {
                File.Delete(localDest.Path);
            }
        }
        public void CopyFileStreamToBlobStreamAndCloseBothStream(FilePathAndStream filePathAndStream,Stream blobStream)
        {
            filePathAndStream.Stream.Flush();
            filePathAndStream.Stream.Position = 0;
            filePathAndStream.Stream.CopyTo(blobStream);
            filePathAndStream.Stream.Close();
            if (blobStream != null)
            {
                blobStream.Flush();
                if (blobStream.CanSeek)
                {
                    blobStream.Position = 0;
                }
                else
                {
                    blobStream.Close();
                }
            }
            // delete local temp file after wrting to blob
            if (File.Exists(filePathAndStream.Path))
            {
                File.Delete(filePathAndStream.Path);
            }
        }

        public class DateAndFile: FilePathAndStream
        {
            public DateTime Date { get; set; }
        }
        public class FilePathAndStream
        {
            public string Path { get; set; }
            public FileStream Stream { get; set; }
        }
    }
}
