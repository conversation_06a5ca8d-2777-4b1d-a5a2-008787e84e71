﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text;

namespace GamificationProcessor.DBStorage.MasterDBModels
{
    [Table("KPIs")]
    public class KPI
    {
        public long Id { get; set; }
        public string CreationContext { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime LastUpdatedAt { get; set; }
        public bool IsQualifier { get; set; }
        public string Description { get; set; }
        public string SQLQuery { get; set; }
        public bool IsDeactivated { get; set; }
        public string Name { get; set; }
        public string UIName { get; set; }
        public int? Sequence { get; set; }
        public int? TimeAlertNumber { get; set; }
        public TimeSpan? TimeAlertWatch { get; set; }
        public int? ProgressInterval { get; set; }
        public int? ProgressStart { get; set; }

    }
}
