﻿using Library.ResiliencyHelpers;
using Library.SlackService;
using System;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using GamificationProcessor.Configuration;
using GamificationProcessor.Core.Models;
using GamificationProcessor.Core.Services;
using Library.AsyncLock;
using Microsoft.Azure.WebJobs;
using Newtonsoft.Json;


namespace DispatchKPIGamification
{
    public class DispatchKPIGamificationTrigger
    {
        private readonly IServiceProvider serviceProvider;
        private readonly ResilientAction resilientAction;
        private readonly ErrorMessenger errorMessenger;

        public DispatchKPIGamificationTrigger(IServiceProvider serviceProvider, ResilientAction resilientAction, ErrorMessenger errorMessenger)
        {
            this.serviceProvider = serviceProvider;
            this.resilientAction = resilientAction;
            this.errorMessenger = errorMessenger;
        }

        public async Task Process(DateTime forDate)
        {
            using (var scope = serviceProvider.CreateScope())
            {
                try
                {
                    await errorMessenger.SendToSlack($"Running dispatch KPIs processor for Gamification for {forDate.ToShortDateString()}!");
                    var gamificationProcessor = scope.ServiceProvider.GetRequiredService<IGamificationProcessor>();
                    await resilientAction.RetryResilientlyAsync(gamificationProcessor.ProcessDispatchKPIsForGamification);
                    await errorMessenger.SendToSlack($"Running dispatch KPIs processor for Gamification for {forDate.ToShortDateString()} Done!");

                }
                catch (Exception ex)
                {
                    await errorMessenger.SendToSlack($"Some Error For Date {forDate.ToShortDateString()}!:{ex.Message}");
                    throw;
                }
            }

        }


    }
}
