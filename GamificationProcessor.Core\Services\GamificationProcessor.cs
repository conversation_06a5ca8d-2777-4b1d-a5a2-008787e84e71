﻿using GamificationProcessor.Core.Helpers;
using GamificationProcessor.Core.Models;
using GamificationProcessor.Core.Repositories;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Text;
using System.Threading.Tasks;
using Library.SlackService;
using Library.ResiliencyHelpers;
using Libraries.CommonEnums.Helpers;
using System.Linq;

namespace GamificationProcessor.Core.Services
{
    public interface IGamificationProcessor
    {
        Task Process(GamificationQueueEvent queueEvent);
        Task ProcessDispatchKPIsForGamification();
    }
    //TODO: Create a New Processor base for all kinds of triggered processor that log the everything in a file and send it to slack
    public class GamificationProcessor : IGamificationProcessor
    {
        private readonly IGamificationRepository repo;
        private readonly MTD_LMTDDatesService mtdlmtdService;
        private readonly ICompanySettingsRepository companySettingRepository;
        private readonly ErrorMessenger errorMessenger;
        private readonly ResilientAction resilientAction;



        public GamificationProcessor(IGamificationRepository repo, MTD_LMTDDatesService mtdlmtdService, ICompanySettingsRepository companySettingRepository, ResilientAction resilientAction, ErrorMessenger errorMessenger)
        {
            this.repo = repo;
            this.mtdlmtdService = mtdlmtdService;
            this.companySettingRepository = companySettingRepository;
            this.errorMessenger = errorMessenger;
            this.resilientAction = resilientAction;

        }
        public async Task Process(GamificationQueueEvent queueEvent)
        {
            var companySettings = CompanySettings.Initialize(companySettingRepository.GetSettings(queueEvent.Data.CompanyId));
            var mtdLmtdDates = await mtdlmtdService.GetDates(queueEvent.Data.CompanyId, DateTime.ParseExact(queueEvent.Data.QualifiedDateKey,
                                        "yyyyMMdd",
                                        CultureInfo.InvariantCulture,
                                        DateTimeStyles.None), companySettings.YearStartMonth, true);
            await repo.SavePerspectiveData(Convert.ToInt64(queueEvent.Id), queueEvent.Data.CompanyId, queueEvent.Data.EmployeeId, mtdLmtdDates);


        }
        public async Task ProcessDispatchKPIsForGamification()
        {
            var KpisIdsForDispatch = repo.GetDispatchKPIIds();
            var activeCompanies = repo.GetAllActiveCompanies();
            foreach (var company in activeCompanies)
            {
                await errorMessenger.SendToSlack($"Started Running for {company.Id}-{company.Name}!");
                var settings = CompanySettings.Initialize(companySettingRepository.GetSettings(company.Id));
                var dayLimitforOrderDispatch = settings.DayLimitforOrderDispatch;
                var timezone = settings.TimeZoneOffset;
                var today = DateTime.UtcNow.AddMinutes(timezone.TotalMinutes).Date;
                var journeyPlanType = settings.GetJourneyPlanType;

                var activeGames = await repo.GetDispatchGames(company.Id, dayLimitforOrderDispatch);
                var empList = await repo.GetGameUsers(company.Id);
                var gameUsers = empList.Select(e => e.UserId).Distinct().ToList();
                Console.WriteLine(company.Id);
                foreach (var game in activeGames)
                {
                    var teamIdsForGame = game.TargetsforTeams.Select(t => t.TeamId).Distinct().ToList();
                    var teamPlayers = empList.Where(a => teamIdsForGame.Contains(a.TeamId)).Select(b => b.UserId).ToList();
                    var mtdLmtdDates = await mtdlmtdService.GetDates(company.Id, DateTime.Today, settings.YearStartMonth, true);

                    foreach (var dispatchKpi in KpisIdsForDispatch)
                    {
                        //TODO: This block should be moved to a separate queue Processor
                        if (game.TargetsforTeams.Where(a =>a.KpiId == dispatchKpi.Key).Count() > 0 && dispatchKpi.Value == "%DispatchAgainstOrder (Volume)")
                        {
                            var orderSummaryData = await repo.GetUsersDispatchAgainstOrderCalculation(company.Id, teamPlayers, game);
                            if (orderSummaryData != null)
                            {
                                foreach (var data in orderSummaryData)
                                {
                                    var teamId = empList.Where(a => a.UserId == data.UserId).Select(b => b.TeamId).FirstOrDefault();
                                    await repo.SaveDispatchKPIData(company.Id, game, data, teamId, dispatchKpi.Key, mtdLmtdDates);
                                }
                            }
                        }
                        else if (game.TargetsforTeams.Where(a => a.KpiId == dispatchKpi.Key).Count() > 0 && dispatchKpi.Value == "% Order Validation")
                        {
                            var orderSummaryData = await repo.GetUsersOrderValidationCalculation(company.Id, teamPlayers, game);
                            if (orderSummaryData != null)
                            {
                                foreach (var data in orderSummaryData)
                                {
                                    var teamId = empList.Where(a => a.UserId == data.UserId).Select(b => b.TeamId).FirstOrDefault();
                                    await repo.SaveDispatchKPIData(company.Id, game, data, teamId, dispatchKpi.Key, mtdLmtdDates);
                                }
                            }
                        }
                    }                   
                }
                //TODO: Need to create a New Slack Logging Service
                await errorMessenger.SendToSlack($"Ended Running for{company.Id}-{company.Name}!");
            }

        }

    }
}
