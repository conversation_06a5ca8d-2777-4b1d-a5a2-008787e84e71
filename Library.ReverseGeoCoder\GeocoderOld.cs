﻿using Newtonsoft.Json;
using ResilientHttpClient;
using System;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Runtime.Serialization;

namespace Library.ReverseGeoCoder
{
    [Obsolete("User Dusara wala",true)]
    public static class GeocoderOld
    {
        private static string api_key = "346f5960ac1c28b9af4c";
        public static string GetAddressFromLatLng(double? lat, double? lng)
        {
            return GetAddressFromLatLng((decimal?)lat, (decimal?)lng);
        }
        public static string GetAddressFromLatLng(decimal? lat, decimal? lng)
        {
            if (lat == null || lng == null || lat == 0 || lng == 0)
            {
                return null;
            }

            var client = new FAResilientHttpClient();

            client.DefaultRequestHeaders.Accept.Add(
                  new MediaTypeWithQualityHeaderValue("application/json"));

            var url = $"http://locationiq.org/v1/reverse.php?format=json&key={api_key}&lat={lat}&lon={lng}&zoom=16";

            var response = client.GetAsync(url).Result;

            if (response.IsSuccessStatusCode)
            {
                var reversedGeoCodedAddress = JsonConvert.DeserializeObject<ReverseGeoCode>(response.Content.ReadAsStringAsync().Result).Address;
                return reversedGeoCodedAddress;
            }
            else
            {
                return "Address not found";
            }
        }
    }

    [DataContract]
    internal class ReverseGeoCode
    {
        [DataMember(Name = "display_name")]
        public string Address { set; get; }
    }
}
