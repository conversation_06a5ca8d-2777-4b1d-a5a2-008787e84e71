﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Globalization;
using System.Linq;
using System.Text.RegularExpressions;

namespace Library.StringHelpers.Extensions
{
    public static class StringHelper
    {
        public static T TestNullAssign<T>(this string value, T whenNull, T whenEmpty = default(T))
        {
            Type t = Nullable.GetUnderlyingType(typeof(T)) ?? typeof(T);
            if (t.IsEnum)
            {
                return (T)(value == null ? whenNull : (string.IsNullOrWhiteSpace(value) ? whenEmpty : ParseEnum_Unchecked(value, whenEmpty)));
            }
            return (T)(value == null ? whenNull : (string.IsNullOrWhiteSpace(value) ? whenEmpty : Convert.ChangeType(value, t)));
        }
        public static string GetDisplayName(this Enum seg)
        {
            try
            {
                var display = seg.GetType()
                .GetField(seg.ToString())
                 //.GetMember(seg.ToString())
                 //.Attr
                 .GetCustomAttributes(false)
                 .OfType<DisplayAttribute>()
                 .SingleOrDefault();
                return display == null ? seg.ToString() : display.Name;
            }
            catch(Exception ex)
            {
                return seg.ToString(); //
            }
        }
        private static T ParseEnum_Unchecked<T>(this string displayName, T defaultData = default(T))
        {
            if (typeof(T).IsEnum)
            {
                try
                {
                    var res = (T)Enum.Parse(typeof(T), displayName);
                    if (!Enum.IsDefined(typeof(T), res))
                    {
                        return defaultData;
                    }
                    return res;
                }
                catch
                {
                    try
                    {
                        try
                        {
                            return Enum.GetValues(typeof(T))
                          .Cast<T>()
                          .Where(v => GetDisplayName(v as Enum).isSameWhenNormalized(displayName))
                          .Single();
                        }
                        catch
                        {
                            return defaultData;
                        }
                    }
                    catch (Exception)
                    {

                        return defaultData;
                    }
                }
            }
            else
            {
                return defaultData;
            }
        }

        //public static T ParseEnum<T>(this string displayName, T defaultData = default(T)) where T : struct
        //{
        //    return TestNullAssign(displayName, defaultData);
        //}



        //private static T ParseEnumName<T>(string displayName, T defaultData = default(T)) where T : struct
        //{
        //    try
        //    {
        //        return Enum.GetValues(typeof(T))
        //      .Cast<T>()
        //      .Where(v => EnumExtension.GetDisplayName(v as Enum).isSameWhenNormalized(displayName))
        //      .Single();
        //    }
        //    catch
        //    {
        //        return defaultData;
        //    }
        //}
        public static bool PhoneDigitsCheck(string item)
        {
            if (!String.IsNullOrEmpty(item))
            {
                if (item.Length > 10 || item.Length < 9)
                {
                    return false;
                }
                else
                {
                    return item.All(char.IsDigit);
                }
            }
            return false;
        }
        public static string NormalizeCaps(this string name)
        {
            if (name == null) return null;
            return name.Trim().Replace(" ", "").ToUpper();
        }
        public static string ListToString<T>(IEnumerable<T> values)
        {
            return string.Join(", ", values.Select(X => $"'{X?.ToString()}'"));
        }

        public static string ListToStringValues<T>(IEnumerable<T> values)
        {
            return string.Join(", ", values.Select(X => $"{X?.ToString()}"));
        }

        public static bool isSameWhenNormalized(this string st1, string st2, bool AreNullEqual = true)
        {
            if (!AreNullEqual && st1 == null && st2 == null)
                return false;
            return (NormalizeCaps(st1) == NormalizeCaps(st2));
        }
        /// <summary>
        /// Checks if a string can be converted to Int
        /// </summary>
        /// <param name="v">String To be Tested</param>
        /// <returns>'-1' for invalid Data</returns>
        public static int Is_StringInt(string v)
        {
            int returnable = -1;
            if (v.All(Char.IsDigit))
            {
                try
                {
                    returnable = int.Parse(v);
                }
                catch
                {
                    returnable = -1;
                }
            }
            else
                returnable = -1;
            return returnable;
        }
        /// <summary>
        /// Checks If a string can be converted to dateTime
        /// </summary>
        /// <param name="v">STring To be Tested</param>
        /// <returns>True or False</returns>
        public static bool Is_StringDateTime(string v)
        {
            DateTime returnable = new DateTime(1900, 1, 1);
            try
            {
                int integerFormat = Is_StringInt(v);
                if (integerFormat != -1)
                {
                    returnable = returnable.AddDays(integerFormat);
                    if (returnable.Year < 2010 || returnable.Year > 2030)
                        return false;
                    else
                        return true;
                }
                else
                {
                    CultureInfo ar = new CultureInfo("en-IN");
                    returnable = DateTime.Parse(v, ar);
                    // = DateTime.Parse(v);
                    return true;
                }
            }
            catch (Exception)
            {
                return false;
            }
        }

        public static DateTime? ParseDateTime(this string v)
        {
            DateTime returnable = new DateTime(1900, 1, 1);
            CultureInfo ar = new CultureInfo("en-IN");

            try
            {
                int integerFormat = Is_StringInt(v);
                if (string.IsNullOrWhiteSpace(v))
                {
                    return null;
                }
                else if (integerFormat != -1)
                {
                    returnable = returnable.AddDays(integerFormat - 2);
                    return returnable;
                }
                else
                {
                    returnable = DateTime.Parse(v, ar);
                    // = DateTime.Parse(v);
                    return returnable;
                }
            }
            catch
            {
                return null;
            }
        }
        public static bool Is_StringInteger(string v, bool onlyPositive = false)
        {
            int returnable;
            if (v.All(Char.IsDigit))
            {
                try
                {
                    returnable = int.Parse(v);
                    if (onlyPositive && returnable <= 0)
                    {
                        return false;
                    }
                    return true;
                }
                catch
                {
                    return false;
                }
            }
            else
            {
                return false;
            }

        }
        public static bool Is_StringDecimal(string v, bool onlyPositive = false)
        {
            decimal returnable;
            try
            {
                returnable = decimal.Parse(v);
                if (onlyPositive && returnable <= 0)
                {
                    return false;
                }
                return true;
            }
            catch
            {
                return false;
            }
        }



        public static bool Is_StringBool(string v)
        {
            bool returnable;
            try
            {
                returnable = bool.Parse(v);
                return true;
            }
            catch
            {
                return false;
            }
        }



        public static bool PinCodeCheck(string v)
        {
            if (v.All(Char.IsDigit) && v.Length <= 6 && v.Length >= 5)
            {
                return true;
            }
            else
                return false;
        }

        public static string ExtractPinCode(string input, int size = 6)
        {
            Regex Pattern = new Regex("\\d{5," + size + "}", RegexOptions.IgnoreCase);
            var m = Pattern.Match(input);
            if (m.Success)
            {
                return m.Value;
            }
            else
            {
                return new string(input.Reverse().Take(6).Reverse().ToArray());
            }
        }
        public static bool Is_HashColor(string v)
        {

            if (v.FirstOrDefault() == '#' && v.Length == 7)
            {
                string testString = v.Substring(1);
                if (testString.All(c => isHexaDecimal(c)))
                    return true;
            }
            return false;
        }

        private static bool isHexaDecimal(char c)
        {
            char testchar = Char.ToUpper(c);
            string list = "01234567890ABCDEF";
            if (list.Contains(testchar))
                return true;
            else
                return false;
        }

        public static bool Is_InList(List<string> CheckList, string val, bool AllowNullandEmpty)
        {
            var checklistLocal = new List<string>(CheckList);
            if (AllowNullandEmpty)
            {
                checklistLocal.Add(null);
                checklistLocal.Add(string.Empty);
            }
            return checklistLocal.Any(x => x == val);
        }


        public static string NumberConversion(double num)
        {
            var number = Math.Round(num, 2);
            int numberOfDigits = number.ToString().Split('.')[0].Length;
            string newNumber = "";
            string returnNumber = "";
            switch (numberOfDigits)
            {
                case 1:
                    returnNumber = number.ToString();
                    break;
                case 2:
                    returnNumber = number.ToString();
                    break;
                case 3:
                    returnNumber = number.ToString();
                    break;
                case 4:
                    newNumber = number.ToString().Split('.')[0].Insert(1, ".");
                    returnNumber = Math.Round(Convert.ToDouble(newNumber), 2).ToString() + "k";
                    break;
                case 5:
                    newNumber = number.ToString().Split('.')[0].Insert(2, ".");
                    returnNumber = Math.Round(Convert.ToDouble(newNumber), 2).ToString() + "k";
                    break;
                case 6:
                    newNumber = number.ToString().Split('.')[0].Insert(1, ".");
                    returnNumber = Math.Round(Convert.ToDouble(newNumber), 2).ToString() + "l";
                    break;
                case 7:
                    newNumber = number.ToString().Split('.')[0].Insert(2, ".");
                    returnNumber = Math.Round(Convert.ToDouble(newNumber), 2).ToString() + "l";
                    break;
                case 8:
                    newNumber = number.ToString().Split('.')[0].Insert(1, ".");
                    returnNumber = Math.Round(Convert.ToDouble(newNumber), 2).ToString() + "cr";
                    break;
                case 9:
                    newNumber = number.ToString().Split('.')[0].Insert(2, ".");
                    returnNumber = Math.Round(Convert.ToDouble(newNumber), 2).ToString() + "cr";
                    break;
                case 10:
                    newNumber = number.ToString().Split('.')[0].Insert(3, ".");
                    returnNumber = Math.Round(Convert.ToDouble(newNumber), 2).ToString() + "cr";
                    break;
                case 11:
                    newNumber = number.ToString().Split('.')[0].Insert(1, ".");
                    returnNumber = Math.Round(Convert.ToDouble(newNumber), 2).ToString() + "k cr";
                    break;
                default:
                    returnNumber = number.ToString();
                    break;

            }
            return returnNumber;
        }
        public static string MinutesToDatTimeStringConvertor(this int? minutes)
        {
            if (minutes.HasValue)
                return ((int)minutes.Value / 60).ToString() + " : " + ((int)(Math.Abs(minutes.Value) % 60)).ToString();
            else
            {
                return "00 : 00";
            }
        }
        public static bool Is_StringEnglish(string inputText)
        {
            if (string.IsNullOrWhiteSpace(inputText))
            {
                return false;
            }
            Regex regex = new Regex(@"[A-Za-z0-9 .,&-=+(){}\[\]\\]");
            MatchCollection matches = regex.Matches(inputText);

            if (matches.Count.Equals(inputText.Length))
                return true;
            else
                return false;
        }
        public static string TrimToLength(this string value, int count, bool addDots = true)
        {
            if (value == null || value.Length <= count)
            {
                return value;
            }
            else
            {
                if (count > 10 && addDots)
                {
                    return value.Substring(0, count - 3) + "...";
                }
                else
                {
                    return value.Substring(0, count);
                }
            }
        }
        public static bool IncludesAny(this string outer, IEnumerable<string> inner)
        {
            return outer.Split(',').Any(x => inner.Contains(x.Trim()));
        }
    }
}
