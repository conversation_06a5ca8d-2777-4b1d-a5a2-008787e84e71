﻿using System;
using System.Runtime.Serialization;

namespace Library.AsyncLock
{
    [Serializable]
    public class MaxLockAttemptExceededException : Exception
    {
        public MaxLockAttemptExceededException()
        {
        }

        public MaxLockAttemptExceededException(string message) : base(message)
        {
        }

        public MaxLockAttemptExceededException(string message, Exception innerException) : base(message, innerException)
        {
        }

        protected MaxLockAttemptExceededException(SerializationInfo info, StreamingContext context) : base(info, context)
        {
        }
    }

    [Serializable]
    public class AcquireLockException : Exception
    {
        public AcquireLockException()
        {
        }

        public AcquireLockException(string message) : base(message)
        {
        }

        public AcquireLockException(string message, Exception innerException) : base(message, innerException)
        {
        }

        protected AcquireLockException(SerializationInfo info, StreamingContext context) : base(info, context)
        {
        }
    }

    [Serializable]
    public class ReleaseLockException : Exception
    {
        public ReleaseLockException()
        {
        }

        public ReleaseLockException(string message) : base(message)
        {
        }

        public ReleaseLockException(string message, Exception innerException) : base(message, innerException)
        {
        }

        protected ReleaseLockException(SerializationInfo info, StreamingContext context) : base(info, context)
        {
        }
    }
}