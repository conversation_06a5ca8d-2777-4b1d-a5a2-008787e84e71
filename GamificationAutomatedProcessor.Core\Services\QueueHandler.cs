﻿using GamificationAutomatedProcessor.Core.Repositories;
using Library.Infrastructure.QueueService;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace GamificationAutomatedProcessor.Core.Services
{
    public class CustomQueueHandler : IQueueHandler
    {
        private readonly QueueHandlerService handler;

        public CustomQueueHandler(QueueHandlerService handler)
        {
            this.handler = handler;
        }

        public async Task AddToQueue<T>(string queueName, string id, T data) where T : class
        {
            var queueEvent = new GridEvent<T>
            {
                Data = data,
                Id = id,
                EventTime = DateTime.UtcNow,
            };
            await handler.AddToQueue(queueName, queueEvent).ConfigureAwait(false);
        }
    }
}
