﻿using GamificationAutomatedProcessor.Core.Interfaces;
using GamificationAutomatedProcessor.Core.Models;
using GamificationAutomatedProcessor.Core.Repositories;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace GamificationAutomatedProcessor.Core.Services
{
    public class QueueManager : IQueueManager
    {
        private const string gamificationQueue = "gamification-queue";
        private readonly IQueueHandler queueHandler;

        public QueueManager(IQueueHandler queueHandler)
        {
            this.queueHandler = queueHandler;
        }
        public async Task AddToGamificationQueue(string ContextGuid, GamificationQueue data)
        {
            await queueHandler.AddToQueue(gamificationQueue, ContextGuid, data);
        }
    }
}
