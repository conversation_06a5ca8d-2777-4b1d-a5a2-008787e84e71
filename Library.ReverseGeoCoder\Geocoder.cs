﻿using Library.StringHelpers.Extensions;
using Newtonsoft.Json;
using ResilientHttpClient;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Runtime.Serialization;
using System.Threading.Tasks;

namespace Library.ReverseGeoCoder
{
    public static class Geocoder
    {
        private static string googleApiKey = "AIzaSyDoPC-MNbaC47eeBE4CU3OORqGd9eExJBQ";

        public async static Task<string> GetAddressFromLatLng(decimal lat, decimal lng)
        {
            return (await GetDataFromLatLng(lat, lng))?.Address ?? "Address not found";
        }

        public async static Task<GeoCodedReturnData> GetDataFromLatLng(decimal lat, decimal lng)
        {
      

            var client = new FAResilientHttpClient();

            client.DefaultRequestHeaders.Accept.Add(
                              new MediaTypeWithQualityHeaderValue("application/json"));

            var api = $"https://maps.googleapis.com/maps/api/geocode/json?key={googleApiKey}&latlng={lat},{lng}";

            var response = await client.GetAsync(api);

            if (response.IsSuccessStatusCode)
            {
                var data = JsonConvert.DeserializeObject<GoogleApiListObject>(await response.Content.ReadAsStringAsync());
                return new GeoCodedReturnData
                {
                    Address = data.GetAddress(),
                    City = data.GetAdmin2(),
                    Locality = data.GetLocality(),
                    Latitude = lat,
                    Longitude = lng,
                    State = data.GetState(),
                    PinCode = data.GetPinCode(),
                };
            }
            else
            {
                return null;
            }
        }
        
    }

    public class GeoCodedReturnData
    {
        public string Address { set; get; }
        public string State { set; get; }
        public string District { get; set; }
        public string City { set; get; }
        public string Locality { set; get; }
        public decimal Latitude { get; set; }
        public decimal Longitude { get; set; }
        public string PinCode { get; set; }
        public string Country { get; set; }
    }
    [DataContract]
    public class GoogleApiListObject
    {
        [DataMember(Name = "results")]
        public List<GoogleApiReturnData> Results { get; set; }

        public string GetCountry()
        {
            var data = this.Results.Select(r => r.Data).FirstOrDefault();
            if (data != null && data.Count() > 0)
            {
                return data.Where(d => d.Types.Contains("country")).Select(d => d.Name).FirstOrDefault();
            }
            return null;
        }
        public string GetState()
        {
            var data = this.Results.Select(r => r.Data).FirstOrDefault();
            if (data != null && data.Count() > 0)
            {
                return data.Where(d => d.Types.Contains("administrative_area_level_1")).Select(d => d.Name).FirstOrDefault();
            }
            return null;
        }
        public string GetAdmin2()
        {
            return this.Results.SelectMany(r => r.Data.Where(i => i.Types.Contains("administrative_area_level_2")).Select(i => i.Name)).FirstOrDefault();
        }

        public string GetLocality()
        {
            return this.Results.SelectMany(r => r.Data.Where(i => i.Types.Contains("locality")).Select(i => i.Name)).FirstOrDefault();
        }
        public string GetSubLocality()
        {
            return this.Results.SelectMany(r => r.Data.Where(i => i.Types.Contains("sublocality")).Select(i => i.Name)).FirstOrDefault();
        }

        public string GetAddress()
        {
            var address = string.Empty;
            if (!string.IsNullOrWhiteSpace(GetSubLocality()))
            {
                address = address + GetSubLocality() + ", ";
            }
            if (!string.IsNullOrWhiteSpace(GetLocality()))
            {
                address = address + GetLocality() + ", ";
            }
            if (!string.IsNullOrWhiteSpace(GetAdmin2()))
            {
                address = address + GetAdmin2() + ", ";
            }
            if (!string.IsNullOrWhiteSpace(GetState()))
            {
                address = address + GetState() + ", ";
            }
            if (!string.IsNullOrWhiteSpace(GetCountry()))
            {
                address = address + GetCountry();
            }
            return address;
        }
        public string GetFullAddress()
        {
            return this.Results.Select(r => r.Address).FirstOrDefault();
        }
        public string GetPinCode()
        {
            var pincode = this.Results.SelectMany(r => r.Data.Where(i => i.Types.Contains("postal_code") && i.Name != "[no name]").Select(i => i.Name)).FirstOrDefault();
            if (pincode != null && pincode.Length > 6)
            {
                return StringHelper.ExtractPinCode(pincode);
            }
            return pincode;
        }
    }
    [DataContract]
    public class GoogleApiReturnData
    {
        [DataMember(Name = "address_components")]
        public List<GoogleApiAddressComponents> Data { set; get; }

        [DataMember(Name = "formatted_address")]
        public string Address { set; get; }

        [DataMember(Name = "types")]
        public List<string> Types { set; get; }
    }
    [DataContract]
    public class GoogleApiAddressComponents
    {
        [DataMember(Name = "long_name")]
        public string Name { set; get; }

        [DataMember(Name = "types")]
        public List<string> Types { set; get; }
    }

}
