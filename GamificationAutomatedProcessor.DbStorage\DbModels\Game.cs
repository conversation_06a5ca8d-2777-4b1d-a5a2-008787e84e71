﻿using System;
using System.Collections.Generic;
using System.Text;

namespace GamificationAutomatedProcessor.DbStorage.DbModels
{
    public class Game
    {
        public long Id { get; set; }
        public string Name { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public GameRewardType RewardType { get; set; }
        public long CompanyId { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public string CreationContext { get; set; }
        public DateTime LastUpdatedAt { get; set; }
        public List<CoinsforKpi> CoinsforKpi { get; set; }
        public List<TargetForTeams> TargetsforTeams { get; set; }

    }
    public class TargetForTeams
    {
        public long Id { get; set; }
        public long GameId { get; set; }
        public long TeamId { get; set; }
        public KPI KPI { get; set; }
        public long KpiId { get; set; }
        public string Target { get; set; }
        public bool IsQualifier { get; set; }
        public bool IsActive { get; set; }
        public bool IsContinuous { get; set; }
        public Game Game { get; set; }
        public ICollection<KPISlab> KPISlabs { get; set; }
    }
    public class CoinsforKpi
    {
        public long Id { get; set; }
        public long GameId { get; set; }
        public long KpiId { get; set; }
        public long Coins { get; set; }
        public Game Game { get; set; }
    }
    public enum GameRewardType
    {
        CUP = 0,
        MEDAL = 1,
        BADGE = 2
    }
    public class Team 
    {
        public long Id { get; set; }

        public string Name { get; set; }

        public bool IsActive { get; set; }

        public long CompanyId { get; set; }

        public DateTime CreatedAt { get; set; }
        public string CreationContext { get; set; }
        public DateTime LastUpdatedAt { get; set; }
    }
    public class TeamUserMapping
    {
        public long Id { get; set; }
        public long TeamId { get; set; }
        public long TeamPlayerId { get; set; }
        public bool IsActive { get; set; }

        public long CompanyId { get; set; }
        public DateTime CreatedAt { get; set; }
        public string CreationContext { get; set; }
        public DateTime LastUpdatedAt { get; set; }
        public Team Team { get; set; }

    }
    public class KPISlab
    {
        public long Id { get; set; }
        public TargetForTeams TargetForTeam { get; set; }
        public long TargetForTeamId { get; set; }
        public string SlabTarget { get; set; }
        public long SlabCoins { get; set; }
        public long? SlabPayout { get; set; }
    }
}
