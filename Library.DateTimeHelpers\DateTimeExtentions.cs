﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;

namespace Library.DateTimeHelpers
{   
    public class FADateRange
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int MonthNumber { get; set; }
        public int YearNumber { get; set; }
        public string MonthName { get; set; }
        public long StartDateKey => StartDate.GetDateKey();
        public long EndDateKey => EndDate.GetDateKey();
        public CycleWeek Week { get; set; }
        public ICollection<CycleWeek> CycleWeeks { get; set; }
    }

    public class DateRangeMTDs : FADateRange
    {
        public static DateRangeMTDs GetDefaultRange(DateTime today, DateTime? startDate = null, int? monthNumber = null, int? yearNumber = null)
        {
            startDate = startDate ?? new DateTime(today.Year, today.Month, 1);
            return new DateRangeMTDs
            {
                StartDate = startDate.Value,
                EndDate = startDate.Value.AddMonths(1).AddDays(-1),
                Today = today.Date,
                MonthNumber = monthNumber ?? (startDate.Value.Day > 1 ? startDate.Value.AddMonths(1).Month : startDate.Value.Month),
                YearNumber = yearNumber ?? startDate.Value.Year,
                MonthName = CultureInfo.CurrentCulture.DateTimeFormat.GetMonthName(startDate.Value.Day > 1 ? startDate.Value.AddMonths(1).Month : today.Month),
                CycleWeeks = GetWeeksForCalendarMonth(startDate.Value, startDate.Value.AddMonths(1).AddDays(-1)),
                 Week = GetWeeksForCalendarMonth(startDate.Value, startDate.Value.AddMonths(1).AddDays(-1)).Where(d=>d.WeekStartDate <= today.Date && d.WeekEndDate >= today.Date).FirstOrDefault()
            };
        }
        public static List<CycleWeek> GetWeeksForCalendarMonth(DateTime startDate, DateTime endDate)
        {
            var weeks = new List<CycleWeek>();  
            var diff = (endDate - startDate).TotalDays;
            var monthNumber = (startDate.Day > 1 ? startDate.AddMonths(1).Month : startDate.Month);
            monthNumber = monthNumber < 4 ? monthNumber + 9 : monthNumber - 3;
            var quarterNumber = ((monthNumber - 1) / 3) + 1 <= 4 ? ((monthNumber - 1) / 3) + 1 : 4;
            var startYear = monthNumber <= 9 ? startDate.Year : startDate.Year - 1;
            var yearStartDate = new DateTime(startYear, 4, 1);
            var quarterStartDate = yearStartDate.AddMonths((quarterNumber - 1) * 3);
            var yearWeekNumber = (startDate - yearStartDate).TotalDays / 7 + 1;
            var quarterWeekNumber = (startDate - quarterStartDate).TotalDays / 7 + 1;
            var weekOfMonth = 1;
            while (diff > 0)
            {
                var week = new CycleWeek();
                var dayOfWeek =weekOfMonth==1? (int)startDate.DayOfWeek : 1;
                dayOfWeek = dayOfWeek == 0 ? dayOfWeek = 7 : dayOfWeek;
                week.WeekStartDate = startDate;
                week.WeekEndDate = diff >= 6 ? (dayOfWeek==1?  week.WeekStartDate.AddDays(6) : week.WeekStartDate.AddDays(6-(dayOfWeek-1)) ): week.WeekStartDate.AddDays(diff);
                week.WeekForMonth = weekOfMonth;
                week.WeekForQuarter = (int)quarterWeekNumber;
                week.WeekForYear = (int)yearWeekNumber;
                week.QuarterNumber = quarterNumber;
                weeks.Add(week);
                weekOfMonth++;
                quarterWeekNumber++;
                yearWeekNumber++;
                startDate = week.WeekEndDate.AddDays(1);
                diff = dayOfWeek == 1 ? diff-7 : diff-7+dayOfWeek-1;
            }
            return weeks;
        }
        public DateTime Today { get; set; }
        public long TodayKey => Today.GetDateKey();

        public DateRangeMTDs DefaultPrevMonth()
        {
            var prevMonthRange = new DateRangeMTDs
            {
                EndDate = this.StartDate.AddDays(-1),
                StartDate = this.StartDate.AddMonths(-1),
                Today = this.StartDate.AddDays(-1).Month == 2 && (this.Today.Date - this.StartDate.Date).Days >= DateTime.DaysInMonth(this.StartDate.AddDays(-1).Year, 2) ? this.StartDate.AddMonths(-1).AddDays(DateTime.DaysInMonth(this.StartDate.AddDays(-1).Year, 2)).AddDays(-1) :
                   (this.Today.Date - this.StartDate.Date).Days >= 30 ? this.StartDate.AddMonths(-1).AddDays((this.Today.Date - this.StartDate.Date).Days).AddDays(-1) : this.StartDate.AddMonths(-1).AddDays((this.Today.Date - this.StartDate.Date).Days),
                MonthNumber = this.MonthNumber - 1 == 0 ? 12 : this.MonthNumber - 1,
            };
            //Adjustments
            prevMonthRange.YearNumber = this.MonthNumber - 1 == 0 ? this.YearNumber - 1 : this.YearNumber;
            prevMonthRange.MonthName = CultureInfo.CurrentCulture.DateTimeFormat.GetMonthName(prevMonthRange.StartDate.Day > 1 ? this.StartDate.Month : prevMonthRange.StartDate.Month);
            prevMonthRange.CycleWeeks = GetWeeksForCalendarMonth(prevMonthRange.StartDate, prevMonthRange.EndDate);
            return prevMonthRange;
        }
    }

    public class FA_MTD_LMTD
    {
        public DateRangeMTDs MTD { get; set; }
        public DateRangeMTDs LMTD { get; set; }
        public DateRangeMTDs YTD { get; set; }
    }

    public static class DateTimeExtentions
    {
        public static FA_MTD_LMTD Get_MTD_LMTD(this DateTime date, IEnumerable<FADateRange> journeyCycles, int monthStartDay, int yearStartMonth)
        {
            return new FA_MTD_LMTD
            {
                LMTD = GetLMTDDateRange(date, journeyCycles, monthStartDay, yearStartMonth),
                MTD = GetMTDDateRange(date, journeyCycles, monthStartDay, yearStartMonth),
                YTD = GetYTDDateRange(date, journeyCycles, monthStartDay, yearStartMonth)
            };
        }
        public static DateTimeOffset ToDateTimeOffset(this DateTime dateTime, TimeSpan offset)
        {
            return new DateTimeOffset(dateTime, offset);
        }

        public static long GetDateKey(this DateTime dateTime)
        {
            return dateTime.Year * 10000 + dateTime.Month * 100 + dateTime.Day;
        }

        public static int GetMonthFromDateKey(long dateKey)
        {
            return Convert.ToInt32(dateKey.ToString().Substring(4, 2).TrimStart('0'));
        }

        public static int GetYearFromDateKey(long dateKey)
        {
            return Convert.ToInt32(dateKey.ToString().Substring(0, 4));
        }

        public static int GetQuarterFromMonth(long dateKey)
        {
            var month = GetMonthFromDateKey(dateKey);
            if (month <= 3)
                return 1;
            else if (month > 3 && month <= 6)
                return 2;
            else if (month > 6 && month <= 9)
                return 3;
            else
                return 4;

        }

        public static DateTime FromDateKey(int datekey)
        {
            int year = datekey / 10000;
            int month = (datekey % 10000) / 100;
            int day = datekey % 100;
            return new DateTime(year, month, day);
        }

        public static DateRangeMTDs GetYTDDateRange(this DateTime dayStartTime, IEnumerable<FADateRange> journeyCycles, int monthStartDay, int yearStartMonth)
        {
            if (journeyCycles != null && journeyCycles.Count() > 0)
            {
                var firstCycle = journeyCycles.OrderBy(c => c.StartDate).First();
                var lastCycle = journeyCycles.OrderBy(c => c.EndDate).Last();
                if (dayStartTime.Date >= firstCycle.StartDate && dayStartTime.Date <= lastCycle.EndDate)
                {
                    var ytd= new DateRangeMTDs
                    {
                        StartDate = firstCycle.StartDate,
                        EndDate = lastCycle.EndDate,
                        Today = dayStartTime.Date,
                        YearNumber = firstCycle.YearNumber,
                        MonthNumber = 1,
                        CycleWeeks = DateRangeMTDs.GetWeeksForCalendarMonth(firstCycle.StartDate, firstCycle.EndDate)
                    };
                }
            }
            return GetYTDDateRange(dayStartTime, monthStartDay, yearStartMonth);
        }

        public static DateRangeMTDs GetMTDDateRange(this DateTime dayStartTime, IEnumerable<FADateRange> journeyCycles, int monthStartDay, int yearStartMonth)
        {
            if (journeyCycles != null && journeyCycles.Count() > 0)
            {
                foreach (var item in journeyCycles.OrderBy(c => c.StartDate))
                {
                    if (dayStartTime.Date >= item.StartDate && dayStartTime.Date <= item.EndDate)
                    {
                        return new DateRangeMTDs
                        {
                            StartDate = item.StartDate,
                            EndDate = item.EndDate,
                            Today = dayStartTime.Date,
                            MonthNumber = item.MonthNumber,
                            YearNumber = item.YearNumber,
                            MonthName = item.MonthName,
                            CycleWeeks = item.CycleWeeks
                        };
                    }
                }
            }
            return GetMTDDateRange(dayStartTime, monthStartDay, yearStartMonth);
        }

        public static DateRangeMTDs GetLMTDDateRange(this DateTime dayStartTime, IEnumerable<FADateRange> journeyCycles, int monthStartDay, int yearStartMonth)
        {
            FADateRange prevMonth = null;
            if (journeyCycles != null && journeyCycles.Count() > 0)
            {
                foreach (var item in journeyCycles.OrderBy(i => i.StartDate))
                {
                    if (dayStartTime.Date >= item.StartDate && dayStartTime.Date <= item.EndDate)
                    {
                        if (prevMonth != null)
                        {
                            return new DateRangeMTDs
                            {
                                StartDate = prevMonth.StartDate,
                                EndDate = prevMonth.EndDate,
                                Today = prevMonth.StartDate.AddDays((dayStartTime.Date - item.StartDate).Days),
                                MonthNumber = prevMonth.MonthNumber,
                                YearNumber = prevMonth.YearNumber,
                                MonthName = prevMonth.MonthName,
                                CycleWeeks = prevMonth.CycleWeeks,
                                Week = prevMonth.CycleWeeks.Where(d=>d.WeekStartDate<= prevMonth.StartDate.AddDays((dayStartTime.Date - item.StartDate).Days) && d.WeekEndDate>= prevMonth.StartDate.AddDays((dayStartTime.Date - item.StartDate).Days)).FirstOrDefault()
                            };
                        }
                        else
                        {
                            return (new DateRangeMTDs
                            {
                                StartDate = item.StartDate,
                                EndDate = item.EndDate,
                                Today = dayStartTime.Date,
                                MonthNumber = item.EndDate.Month,
                                YearNumber = item.StartDate.Year,
                                MonthName = item.MonthName,
                                CycleWeeks = item.CycleWeeks,
                                Week = item.CycleWeeks.Where( d=>d.WeekStartDate <= dayStartTime.Date  && d.WeekEndDate>=  dayStartTime.Date ).FirstOrDefault()
                            }).DefaultPrevMonth();
                        }
                    }
                    prevMonth = item;
                }
            }
            return GetLMTDDateRange(dayStartTime, monthStartDay, yearStartMonth);
        }
        public static DateRangeMTDs GetYTDDateRange(this DateTime dateTime, int monthStartDay, int yearStartMonth)
        {
            var month = dateTime.Month - yearStartMonth + 1;
            var year = dateTime.Year;
            if (monthStartDay > 1 && dateTime.Day < monthStartDay)
            {
                month--;
                if (month <= 0)
                {
                    month = month + 12;
                    year = year - 1;
                }
            }
            return new DateRangeMTDs
            {
                StartDate = new DateTime(year, yearStartMonth, monthStartDay),
                EndDate = new DateTime(year, yearStartMonth, monthStartDay).AddYears(1).AddDays(-1),
                MonthNumber = 1,
                YearNumber = year,
                Today = dateTime.Date
            };
        }
        public static DateRangeMTDs GetMTDDateRange(this DateTime dateTime, int monthStartDay, int yearStartMonth)
        {
            var monthNumber = dateTime.Month - yearStartMonth + 1;
            var yearNumber = dateTime.Year;
            if (monthNumber <= 0)
            {
                yearNumber--;
                monthNumber = monthNumber + 12;
            }
            if (monthStartDay > 1 && dateTime.Day >= monthStartDay)
            {
                monthNumber++;
                if (monthNumber > 12)
                {
                    monthNumber = monthNumber - 12;
                    yearNumber = yearNumber + 1;
                }
            }

            var month = dateTime.Month;
            var year = dateTime.Year;
            if (monthStartDay > 1 && dateTime.Day < monthStartDay)
            {
                month--;
                if (month <= 0)
                {
                    month = month + 12;
                    year = year - 1;
                }
            }

            return DateRangeMTDs.GetDefaultRange(dateTime, new DateTime(year, month, monthStartDay), monthNumber, yearNumber);
        }

        public static DateRangeMTDs GetLMTDDateRange(this DateTime dateTime, int monthStartDay, int yearStartMonth)
        {
            return GetMTDDateRange(dateTime, monthStartDay, yearStartMonth).DefaultPrevMonth();
        }

        public static string GetDateKey_String(this DateTime dateTime)
        {
            return dateTime.ToString("yyyyMMdd");
        }

        public static long ToUnixTime(this DateTime date)
        {
            var epoch = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
            return Convert.ToInt64((date - epoch).TotalSeconds);
        }

        public static DateTime FromUnixTime(this long unixTime)
        {
            var epoch = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
            return epoch.AddSeconds(unixTime);
        }
        public static int GetMonthKey(this DateTime dayStartTime, int monthStartDay, int yearStartMonth)
        {
            var month = dayStartTime.Month - yearStartMonth + 1;
            month = dayStartTime.Day < monthStartDay ? month - 1 : month;
            return month < 1 ? month + 12 : month;
        }
        public static int GetYearKey(this DateTime dayStartTime, int monthStartDay, int yearStartMonth)
        {
            return dayStartTime.Month < yearStartMonth || (dayStartTime.Month == yearStartMonth && dayStartTime.Day < monthStartDay)
                ? dayStartTime.Year - 1 : dayStartTime.Year;
        }
        public static int GetDayKey(this DateTime dayStartTime, int monthStartDay)
        {
            var dayStartDay = dayStartTime.Day;
            if (monthStartDay > dayStartTime.Day)
            {
                var preCalMonth = dayStartTime.Month == 1 ? new DateTime(dayStartTime.Year - 1, 12, 1) : new DateTime(dayStartTime.Year, dayStartTime.Month - 1, 1);
                var preMonthDays = DateTime.DaysInMonth(preCalMonth.Year, preCalMonth.Month);
                dayStartDay = preMonthDays - monthStartDay + dayStartTime.Day + 1;
            }
            else
            {
                dayStartDay = dayStartTime.Day - monthStartDay + 1;
            }
            return dayStartDay;
        }

        public static DateTime GetMTDFromDate(this DateTime currentDate, int monthStartDay)
        {
            var date = currentDate;
            if (currentDate.Day < monthStartDay)
            {
                date = currentDate.AddMonths(-1);
            }
            return new DateTime(date.Year, date.Month, monthStartDay <= DateTime.DaysInMonth(date.Year, date.Month)
                ? monthStartDay : DateTime.DaysInMonth(date.Year, date.Month));
        }

        public static string GetTimeString(this TimeSpan time)
        {
            return new DateTime().Add(time).ToString("HH:mm:ss");
        }
        public static string MinutesToDatTimeStringConvertor(this int minutes)
        {
            if (minutes < 0)
            {
                return ((int)minutes / 60).ToString("-00") + ":" + ((int)(Math.Abs(minutes) % 60)).ToString("00");
            }
            else
            {
                return ((int)minutes / 60).ToString("00") + ":" + ((int)(Math.Abs(minutes) % 60)).ToString("00");
            }

        }
        public static DateTime GetDateFromDateKey(this long dateKey)
        {
            return DateTime.ParseExact(dateKey.ToString(), "yyyyMMdd", CultureInfo.InvariantCulture, DateTimeStyles.None);
        }
        public class DateRangMin
        {
            public DateTime StartDate { get; set; }
            public DateTime EndDate { get; set; }
            public int Month { get; set; }
            public int Year { get; set; }
        }

    }
    public class CycleWeek
    {
        public int WeekForMonth { get; set; }
        public int QuarterNumber { get; set; }
        public int WeekForQuarter { get; set; }
        public int WeekForYear { get; set; }
        public DateTime WeekStartDate { get; set; }
        public DateTime WeekEndDate { get; set; }
    }
}
