﻿using GamificationProcessor.DBStorage.TransactionModels;
using Microsoft.EntityFrameworkCore;

namespace GamificationProcessor.DbStorage.DbContexts
{
    public class WritableTransactionDbContext : DbContext
    {
        public DbSet<DbGameKPIStat> GameKPIStats { get; set; }
        public WritableTransactionDbContext(DbContextOptions<WritableTransactionDbContext> options) : base(options)
        {
        }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {

        }
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            
            base.OnModelCreating(modelBuilder);
        }
    }

}
