﻿using System;
using System.Text;

namespace Libraries.Cryptography
{
    public class Base64Helper
    {
        public static Base64AuthModel GetAuthorisedModel(string token)
        {
            var encoding = Encoding.GetEncoding("iso-8859-1");
            var credentials = encoding.GetString(Convert.FromBase64String(token));
            credentials = encoding.GetString(Convert.FromBase64String(token));

            int separator = credentials.IndexOf(':');
            return new Base64AuthModel()
            {
                UserName = credentials.Substring(0, separator),
                Password = credentials.Substring(separator + 1)
            };
        }
        public static string GetBasicToken(Base64AuthModel data)
        {
            var plainTextBytes = Encoding.GetEncoding("iso-8859-1").GetBytes($"{data.UserName}:{data.Password}");
            return Convert.ToBase64String(plainTextBytes);
        }

        public static string Decrypt(string password)
        {
            byte[] data = Convert.FromBase64String(password);
            var base64Decoded = ASCIIEncoding.ASCII.GetString(data);
            return base64Decoded;
        }
    }

    public class Base64AuthModel
    {
        public string UserName { get; set; }
        public string Password { get; set; }
    }
}
