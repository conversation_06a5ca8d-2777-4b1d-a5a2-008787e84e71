﻿using Library.SlackService;
using System;
using System.Collections.Generic;
using System.Text;
using System.Linq;
using System.Runtime.Serialization;

namespace Library.ReverseGeoCoder
{
    public class GeoCodingMapMyIndia 
    {
        private static string mapMyIndia_ApiKey = "d624fc5yo2rdrx1wv17du4gv5ifzgde5";
        private readonly ErrorMessenger _errorMessanger;

        public GeoCodingMapMyIndia(ErrorMessenger errorMessanger)
        {
            _errorMessanger = errorMessanger;
        }
        public GeoCodedReturnData GetDataFromLatLng(decimal lat, decimal lng)
        {
            try
            {
                var api = $"http://apis.mapmyindia.com/advancedmaps/v1/{mapMyIndia_ApiKey}/rev_geocode?lat={lat}&lng={lng}";
                var response = new APIActions<MapMyIndiaReturnModel>(_errorMessanger).Get(api, 1).Result;
                if (response != null && response.IsSuccess)
                {
                    var data = response.Results.FirstOrDefault();
                    return new GeoCodedReturnData
                    {
                        Address = data.Address,
                        City = data.FormattedCity,
                        District = data.District,
                        Latitude = (decimal)data.Latitude,
                        Longitude = (decimal)data.Longitude,
                        State = data.State,
                        PinCode = data.PinCode,
                        Locality = data.Locality,
                        Country = data.Country
                    };
                }
            }
            finally
            {

            }
            return new GeoCodedReturnData();
        }
    }



    [DataContract]
    public class MapMyIndiaReturnModel
    {
        [DataMember(Name = "results")]
        public List<MapMyIndiaAddressComponents> Results { get; set; }

        [DataMember(Name = "responseCode")]
        public int SuccessCode { get; set; }

        public bool IsSuccess => SuccessCode == 200 && Results != null && Results.Count() > 0;

        [DataContract]
        public class MapMyIndiaAddressComponents
        {
            [DataMember(Name = "formatted_address")]
            public string Address { set; get; }

            [DataMember(Name = "state")]
            public string State { set; get; }
            [DataMember(Name = "district")]
            public string District { set; get; }

            [DataMember(Name = "subDistrict")]
            public string Locality { set; get; }

            [DataMember(Name = "locality")]
            public string Locality2 { set; get; }

            [DataMember(Name = "pincode")]
            public string PinCode { set; get; }

            [DataMember(Name = "city")]
            public string City { set; get; }

            public string FormattedCity => !string.IsNullOrWhiteSpace(City) ? City : (!string.IsNullOrWhiteSpace(Locality) ? Locality : Locality2);

            [DataMember(Name = "lat")]
            public double Latitude { get; set; }

            [DataMember(Name = "lng")]
            public double Longitude { get; set; }

            [DataMember(Name = "area")]
            public string Country { get; set; }
        }
    }
}
