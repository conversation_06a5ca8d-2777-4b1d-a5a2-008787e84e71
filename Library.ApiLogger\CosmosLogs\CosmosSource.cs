﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Library.ApiLogger.CosmosLogs
{
    public class CosmosSource
    {
        public string DatabaseId { get; private set; }
        public string CollectionId { get; private set; }
        public string EndpointUrl { get; private set; }
        public string AuthorizationKey { get; private set; }

        public CosmosSource(string cosmosConnString, string databaseId, string collectionId)
        {
            var items = cosmosConnString.Split(';');
            AuthorizationKey = items[1].Substring(items[1].IndexOf("AccountKey=") + "AccountKey=".Length);
            EndpointUrl = items[0].Substring(items[0].IndexOf("AccountEndpoint=") + "AccountEndpoint=".Length);

            this.DatabaseId = databaseId;
            this.CollectionId = collectionId;
        }
    }
}
