﻿using Libraries.CommonEnums;
using System;
using System.Collections.Generic;
using System.Text;

namespace Library.SMSHelpers
{
    public class SMSConfiguration
    {

        public long Id { get; set; }
        public long CompanyId { get; set; }
        public OrderKind OrderKind { get; set; }
        public string CompanyName { get; set; }
        public string Url { get; set; }
        public bool SendOnlyProductive { get; set; }
        public string Message { get; set; }
        public string UnProductiveMessage { get; set; }
        public string OTPMessage { get; set; }
        public bool IsPOSTOnly { get; set; }

        public DateTime CreatedAt { get; set; }
        public string CreationContext { get; set; }
        public DateTime LastUpdatedAt { get; set; }
        public bool IsDeactive { get; set; }

    }
}
