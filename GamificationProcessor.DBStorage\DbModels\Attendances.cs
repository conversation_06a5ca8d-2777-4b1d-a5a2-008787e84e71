﻿using Libraries.CommonEnums;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text;

namespace GamificationProcessor.DbStorage.DbModels
{
    [Table("Attendances")]
    public class Attendances
    {
        [Key]
        public Guid AttendanceGuid { get; set; }
        public long? AttendanceId { get; set; }
        public long? BeatId { get; set; }
        public long? CompanyId { get; set; }
        public long ESMId { get; set; }
        public long? GSMId { get; set; }
        public long? NSMId { get; set; }
        public long? ZSMId { get; set; }
        public long? RSMId { get; set; }
        public long? ASMId { get; set; }
        public double? OrderInStdUnits { get; set; }
        public double? OrderInUnits { get; set; }
        public double? DispatchInStdUnits { get; set; }
        public int? DispatchInUnits { get; set; }
        public long? RegionId { get; set; }
        public long? ZoneId { get; set; }
     public long? PositionLevel1 { get; set; }
     public long? PositionLevel2 { get; set; }
     public long? PositionLevel3 { get; set; }
     public long? PositionLevel4 { get; set; }
     public long? PositionLevel5 { get; set; }
     public long? PositionLevel6 { get; set; }
     public long? PositionLevel7 { get; set; }
     public long? PositionLevel8 { get; set; }
    public long? GeographyLevel5 { get; set; }
    public long? GeographyLevel6 { get; set; }
    public long? GeographyLevel7 { get; set; }
    public long? CallStartDateKey { get; set; }
    }

}
