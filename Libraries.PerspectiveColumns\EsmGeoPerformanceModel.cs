﻿using Libraries.CommonEnums;
using Libraries.PerspectiveColumns.Interface;
using System.Collections.Generic;

namespace Libraries.PerspectiveColumns
{
    public class EmpGeoPerformanceModel : IPerspective
    {
        //private linkNames linkNames;

        public EmpGeoPerformanceModel(long companyId)
        {
            // linkNames = InMemorySettings.GetLinkNames(companyId);
        }
        List<PerspectiveColumnModel> IPerspective.Columns { get => Columns; set => throw new System.NotImplementedException(); }
        public List<PerspectiveColumnModel> Columns => new List<PerspectiveColumnModel>
        {
            // Name Property Needs to match the key used for Nomenclature For QuickViz
                    new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = "GSM"    , Name = "GSM"         , IsMeasure = false, IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.Unknown, IsOtherMeasure = false, IsPivot = false},
                    new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = "NationalSalesManager" , Name = "NSM"         , IsMeasure = false, IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.Unknown, IsOtherMeasure = false, IsPivot = false},
                    new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = "ZonalSalesManager"    , Name = "ZSM"         , IsMeasure = false, IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.Unknown, IsOtherMeasure = false, IsPivot = false},
                    new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = "RegionalSalesManager" , Name = "RSM"         , IsMeasure = false, IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.Unknown, IsOtherMeasure = false, IsPivot = false},
                    new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = "AreaSalesManager"     , Name = "ASM"         , IsMeasure = false, IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.Unknown, IsOtherMeasure = false, IsPivot = false},
                    new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName =  "Employee"            , Name = "ESM"    , IsMeasure = false, IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.Unknown, IsOtherMeasure = false                 },
                   new PerspectiveColumnModel() { Attribute = "Sales Territory" , DisplayName = "Level7"                 , Name = "Level7"           , IsMeasure = false , IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsPivot = false , IsOtherMeasure = false },
                    new PerspectiveColumnModel() { Attribute = "Sales Territory" , DisplayName = "Level6"                 , Name = "Level6"           , IsMeasure = false , IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsPivot = false , IsOtherMeasure = false },
                    new PerspectiveColumnModel() { Attribute = "Sales Territory" , DisplayName = "Level5"                 , Name = "Level5"           , IsMeasure = false , IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsPivot = false , IsOtherMeasure = false },
                    new PerspectiveColumnModel() { Attribute = "Sales Territory" , DisplayName = "Zone"                   , Name = "Zone"             , IsMeasure = false , IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsPivot = false , IsOtherMeasure = false },
                    new PerspectiveColumnModel() { Attribute = "Sales Territory" , DisplayName = "Region"                 , Name = "Region"           , IsMeasure = false , IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsPivot = false , IsOtherMeasure = false },
                    new PerspectiveColumnModel() { Attribute = "Sales Territory" , DisplayName = "Territory"              , Name = "Territory"        , IsMeasure = false , IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.CountDistinct                                            },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "TC"                               , Name = "TC"                      , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value     },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "New Outlets"                      , Name = "NewOutlets"              , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value    },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Days Retailing"                   , Name = "DaysRetailing"           , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value     },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "PC"                               , Name = "PC"                      , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value   },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "UTC"                      , Name = "UTC"                     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value    },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "UPC"                      , Name = "UPC"                     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value    },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Avg. TC (per day)"                , Name = "AvgTCPerday"         , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value      },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Avg. PC (per day)"                , Name = "AvgPCPerday"         , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value   } ,
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "LPC"                      , Name = "LPC"                     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value    },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Avg Retailing Time"               , Name = "AvgRetailingTime"        , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value     },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Order Qty (Unit)"                 , Name = "OrderInUnits"            , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value   },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Value"                            , Name = "Value"                   , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value      },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "NetValue"                         , Name = "NetValue"                , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value      },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Order Qty (Std Unit)"             , Name = "OrderQtyInStdUnit"          , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value      },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Avg Sales (Per Day)"          , Name = "AvgValuePerDay"                , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value   },
                     new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Scheduled Days"          , Name = "ScheduledDays"                , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value   },
                    new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Loss of Mandays"          , Name = "LossOfMandays"                , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value   },
                    new PerspectiveColumnModel() { Attribute = "Master Measure"  , DisplayName = "Employee Overall Target"          , Name = "Targets"                 , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value },
                    new PerspectiveColumnModel() { Attribute = "Master Measure"  , DisplayName = "Total Outlets"                    , Name = "Outlets"                 , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value },
                    new PerspectiveColumnModel() { Attribute = "Master Measure"  , DisplayName = "Total Beats"                      , Name = "Beats"                   , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value },

        };
    }
}


