﻿using Library.SlackService;
using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace Library.SMSHelpers
{
    public class ClientSMSSender
    {

        private readonly SMSConfiguration _smsSetting;

        public ClientSMSSender(SMSConfiguration smsSetting)
        {
            _smsSetting = smsSetting;
        }
        public bool SendSms_OnOrders(string locationName, string locationOwnerNo,long attendanceId, double saleValue, string productUnit , string env = "dev")
        {
            if (_smsSetting == null)
            {
                return false;
            }
            locationName = locationName + ", ";
            if (!_smsSetting.SendOnlyProductive || (_smsSetting.SendOnlyProductive && saleValue > 0))
            {
                string message = saleValue > 0 ?
                    string.Format(_smsSetting.Message, locationName, saleValue, DateTime.UtcNow.ToShortDateString(), attendanceId, productUnit ?? "", _smsSetting.CompanyName)
                : string.Format(_smsSetting.UnProductiveMessage, _smsSetting.CompanyName);
                if (env == "dev")
                {
                    var slackHandler = new SMSConfigSlackMessanger();
                    var sms = new SMSMessage
                    {
                        Message = message,
                        To = locationOwnerNo,

                    };
                    var iSlackMessage = sms.ToSlackMessage("#debugrouted");
                    Task.Run(() => slackHandler.SendToSlack(iSlackMessage));
                    return true;
                }
                else
                {
                    string encodedMessage = Uri.EscapeDataString(message);
                    string requestUrl = string.Format(_smsSetting.Url, locationOwnerNo, encodedMessage);

                    var result = MakeRequest(requestUrl);
                    return result;
                }
            }
            else
            {
                return false;
            }
        }
        public bool SendSms_AccToCompanyWithOTP(string locationOwnerName, string locationOwnerNo, string locationName, string empName, string otp,string env ="dev")
        {
            if (_smsSetting == null)
            {
                return false;
            }
            empName = !string.IsNullOrWhiteSpace(empName) ? empName : "Company Employee";
            if (env == "dev")
            {
                var slackHandler = new SMSConfigSlackMessanger();
                string message = string.Format(_smsSetting.OTPMessage, locationOwnerName, _smsSetting.CompanyName, otp, empName);
                var sms = new SMSMessage
                {
                    Message = message,
                    To = locationOwnerNo,

                };
                var iSlackMessage = sms.ToSlackMessage("#debugrouted");
                Task.Run(() => slackHandler.SendToSlack(iSlackMessage));
                return true;
            }
            else
            {
                string message = string.Format(_smsSetting.OTPMessage, locationOwnerName, _smsSetting.CompanyName, otp, empName);
                string encodedMessage = Uri.EscapeDataString(message);
                string requestUrl = string.Format(_smsSetting.Url, locationOwnerNo, encodedMessage);

                var result = MakeRequest(requestUrl);
                return result;
            }
        }
        public bool OrderConfirmationOTP(string locationOwnerName, string locationOwnerNo, string locationName, string empName, string otp,double TotalOrderQuantity, string env = "dev")
        {
            if (_smsSetting == null)
            {
                return false;
            }
            empName = !string.IsNullOrWhiteSpace(empName) ? empName : "Company Employee";
            if (env == "dev")
            {
                var slackHandler = new SMSConfigSlackMessanger();
                string message = string.Format(_smsSetting.Message, locationOwnerName, _smsSetting.CompanyName, otp,TotalOrderQuantity, empName, DateTime.UtcNow.ToShortDateString());
                var sms = new SMSMessage
                {
                    Message = message,
                    To = locationOwnerNo,

                };
                var iSlackMessage = sms.ToSlackMessage("#debugrouted");
                Task.Run(() => slackHandler.SendToSlack(iSlackMessage));
                return true;
            }
            else
            {
                string message = string.Format(_smsSetting.Message, locationOwnerName, _smsSetting.CompanyName, otp, TotalOrderQuantity, empName, DateTime.UtcNow.ToShortDateString());
                string encodedMessage = Uri.EscapeDataString(message);
                string requestUrl = string.Format(_smsSetting.Url, locationOwnerNo, encodedMessage);

                var result = MakeRequest(requestUrl);
                return result;
            }
        }

        private bool MakeRequest(string requestUrl)
        {
            try
            {
                using (var client = new HttpClient())
                {
                    if (_smsSetting.IsPOSTOnly)
                    {
                        var url = new Uri(requestUrl);
                        if (!string.IsNullOrEmpty(url.UserInfo))
                        {
                            client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Basic",
                                          Convert.ToBase64String(System.Text.Encoding.ASCII.GetBytes(url.UserInfo)));
                        }
                        var response = client.PostAsync(url, null).Result;
                        if (response.StatusCode != HttpStatusCode.OK)
                        {
                            return false;
                        }
                        return true;
                    }
                    else
                    {
                        var url = new Uri(requestUrl);
                        if (!string.IsNullOrEmpty(url.UserInfo))
                        {
                            client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Basic",
                                          Convert.ToBase64String(System.Text.Encoding.ASCII.GetBytes(url.UserInfo)));
                        }
                        var response = client.GetAsync(requestUrl).Result;
                        if (response.StatusCode != HttpStatusCode.OK)
                        {
                            return false;
                        }

                        return true;
                    }
                }
            }
            catch (Exception e)
            {
                return false;
            }
        }
        public bool SendNewOutletConfimationMessage(string locationOwnerNum, string env = "dev")
        {
            if (_smsSetting == null || string.IsNullOrEmpty(_smsSetting.Message))
            {
                return false;
            }
            if (env == "dev")
            {
                var slackHandler = new SMSConfigSlackMessanger();
                var sms = new SMSMessage
                {
                    Message = _smsSetting.Message,
                    To = locationOwnerNum,

                };
                var iSlackMessage = sms.ToSlackMessage("#debugrouted");
                Task.Run(() => slackHandler.SendToSlack(iSlackMessage));
                return true;
            }
            else
            {
                string requestUrl = string.Format(_smsSetting.Url, locationOwnerNum, _smsSetting.Message);
                var result = MakeRequest(requestUrl);
                return result;
            }
        }
    }
}
