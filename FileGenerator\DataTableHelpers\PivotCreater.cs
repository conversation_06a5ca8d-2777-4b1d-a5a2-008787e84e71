﻿using FileGenerator.HelperModels;
using System.Collections.Generic;
using System.Data;
using System.Linq;

namespace FileGenerator.DataTableHelpers
{
    public class PivotCreater
    {
        public DataTable Pivot(DataTable dt, DataColumn pivotColumn, DataColumn pivotValue)
        {
            // find primary key columns 
            //(i.e. everything but pivot column and pivot value)
            DataTable temp = dt.Copy();
            temp.Columns.Remove(pivotColumn.ColumnName);
            temp.Columns.Remove(pivotValue.ColumnName);
            string[] pkColumnNames = temp.Columns.Cast<DataColumn>()
                .Select(c => c.ColumnName)
                .ToArray();

            // prep results table
            DataTable result = temp.DefaultView.ToTable(true, pkColumnNames).Copy();
            result.PrimaryKey = result.Columns.Cast<DataColumn>().ToArray();
            dt.Rows.Cast<DataRow>()
                .Select(r => r[pivotColumn.ColumnName].ToString())
                .Distinct().ToList()
                .ForEach(c => result.Columns.Add(c, pivotValue.DataType));

            // load it
            foreach (DataRow row in dt.Rows)
            {
                // find row to update
                DataRow aggRow = result.Rows.Find(
                    pkColumnNames
                        .Select(c => row[c])
                        .ToArray());
                // the aggregate used here is LATEST 
                // adjust the next line if you want (SUM, MAX, etc...)
                aggRow[row[pivotColumn.ColumnName].ToString()] = (row[pivotValue.ColumnName]);

            }
            result.PrimaryKey = null;
            var pivot = CheckDataTableColumn(result);
            result.TableName = "Pivot" + pivotColumn;
            return result;
        }


        public DataTable Pivot(DataTable dt, string primaryKey, PivotColumn pivotColumnSet, string tableName,string defaultGroupColumnName = null, List<Dictionary<string, int>> sortingDictionaryList = null, string delimiter = "_")
        {
            // find primary key columns 
            //(i.e. everything but pivot column and pivot value)
            DataTable temp = dt.Copy();

            var pivotColumn = dt.Columns[pivotColumnSet.ParentColumn];

            foreach (var item in temp.Columns.Cast<DataColumn>()
                .Select(c => c.ColumnName)
                .ToArray())
            {
                if (item != primaryKey)
                    temp.Columns.Remove(item);
            }
            string[] pkColumnNames = new string[] { primaryKey };

            // prep results table 
            DataTable result = temp.AsEnumerable().Distinct(DataRowComparer.Default).CopyToDataTable(); //temp.DefaultView.ToTable(true, pkColumnNames).Copy();
            result.PrimaryKey = result.Columns.Cast<DataColumn>().ToArray();
            foreach (var pivotMember in pivotColumnSet.ValueColumns)
            {
                var pivotValue = dt.Columns[pivotMember];
                dt.Rows.Cast<DataRow>()
                .Select(r => r[pivotColumn.ColumnName].ToString() + delimiter + pivotValue)
                .Distinct().ToList()
                .ForEach(c => result.Columns.Add(c, pivotValue.DataType));

                // load it
                foreach (DataRow row in dt.Rows)
                {
                    // find row to update
                    DataRow aggRow = result.Rows.Find(
                        pkColumnNames
                            .Select(c => row[c])
                            .ToArray());
                    // the aggregate used here is LATEST 
                    // adjust the next line if you want (SUM, MAX, etc...)
                    aggRow[row[pivotColumn.ColumnName].ToString() + delimiter + pivotValue] = (row[pivotValue.ColumnName]);
                }
            }
            result.PrimaryKey = null;         
            result.TableName = tableName;
            SortPivotColumns sortPivotColumns = new SortPivotColumns();
            //Date: 16th Aug 2021;
            //Link: https://app.asana.com/0/305436650865282/1200721183030850/f;
            //Reason: Change the concat variable to '+' from '_'. 
            foreach (var c in pivotColumnSet.ValueColumns)
            {
                var colmnName = $"{defaultGroupColumnName}{delimiter}{c}";
                if (result.Columns.Contains(colmnName))
                {
                    result.Columns.Remove(colmnName);
                }
            }
            if (sortingDictionaryList != null)
            {
                result = sortPivotColumns.GetSortedPivotTable(result, sortingDictionaryList);
            }
            //var pivot = CheckDataTableColumn(result);
            return result;
        }
        public DataTable DoublePivot(DataTable dt, string primaryKey, PivotColumn pivotColumnSet, string tableName, string defaultGroupColumnName = null, List<Dictionary<string, int>> sortingDictionaryList = null)
        {
            // find primary key columns 
            //(i.e. everything but pivot column and pivot value)
            DataTable temp = dt.Copy();

            var pivotColumn = dt.Columns[pivotColumnSet.ParentColumn];
            var SecondaryPivotColumn = dt.Columns[pivotColumnSet.SecondaryPivotColumn];

            foreach (var item in temp.Columns.Cast<DataColumn>()
                .Select(c => c.ColumnName)
                .ToArray())
            {
                if (item != primaryKey)
                    temp.Columns.Remove(item);
            }
            string[] pkColumnNames = new string[] { primaryKey };

            // prep results table 
            DataTable result = temp.AsEnumerable().Distinct(DataRowComparer.Default).CopyToDataTable(); //temp.DefaultView.ToTable(true, pkColumnNames).Copy();
            result.PrimaryKey = result.Columns.Cast<DataColumn>().ToArray();
            foreach (var pivotMember in pivotColumnSet.ValueColumns)
            {
                var pivotValue = dt.Columns[pivotMember];
                dt.Rows.Cast<DataRow>()
                .Select(r => r[SecondaryPivotColumn.ColumnName].ToString() + "+" + r[pivotColumn.ColumnName].ToString() + "+" + pivotValue)
                .Distinct().ToList()
                .ForEach(c => result.Columns.Add(c, pivotValue.DataType));

                // load it
                foreach (DataRow row in dt.Rows)
                {
                    // find row to update
                    DataRow aggRow = result.Rows.Find(
                        pkColumnNames
                            .Select(c => row[c])
                            .ToArray());
                    // the aggregate used here is LATEST 
                    // adjust the next line if you want (SUM, MAX, etc...)
                    aggRow[row[SecondaryPivotColumn.ColumnName].ToString() + "+" + row[pivotColumn.ColumnName].ToString() + "+" + pivotValue] = (row[pivotValue.ColumnName]);
                }
            }
            result.PrimaryKey = null;
            result.TableName = tableName;
            SortPivotColumns sortPivotColumns = new SortPivotColumns();
            foreach (var c in pivotColumnSet.ValueColumns)
            {
                var colmnName = $"{defaultGroupColumnName}_{c}";
                if (result.Columns.Contains($"{defaultGroupColumnName}_{c}"))
                {
                    result.Columns.Remove(colmnName);
                }
            }
            if (sortingDictionaryList != null)
            {
                result = sortPivotColumns.GetSortedPivotTable(result, sortingDictionaryList);
            }
            //var pivot = CheckDataTableColumn(result);
            return result;
        }
        private DataTable CheckDataTableColumn(DataTable dt)
        {
            bool flag = false;
            int counter = 0;
            for (int i = counter; i < dt.Columns.Count; i++)
            {
                for (int x = 0; x < dt.Rows.Count; x++)
                {
                    if (string.IsNullOrEmpty(dt.Rows[x][i].ToString()))

                    {
                        flag = true; //means there is an empty value
                    }
                    else
                    {
                        //means if it found non null or empty in rows of a particular column
                        flag = false;
                        counter = i + 1;
                        break;
                    }
                }
                if (flag == true)
                {
                    dt.Columns.Remove(dt.Columns[i]);
                    i--;
                }
            }

            return dt;

        }
    }
}
