using GamificationAutomatedProcessor.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Threading.Tasks;

namespace GamificationAutomatedProcessor.Test
{
    [TestClass]
    public class ProcessorTest
    {
        private ServiceProvider serviceProvider;
        [TestInitialize]
        public void Initialise()
        {
            //Environment.SetEnvironmentVariable("BuildEnvironment", "ManageTesting");
            Environment.SetEnvironmentVariable("KEYVAULT_ENDPOINT", "https://v3ManageWritable.vault.azure.net/");
            //Environment.SetEnvironmentVariable("KEYVAULT_ENDPOINT", "https://v3DebugWritable.vault.azure.net/");
            var config = Configuration.GetConfiguration();

            IServiceCollection serviceCollection = new ServiceCollection();
            Dependencies.SetUp(serviceCollection, config);
            serviceProvider = serviceCollection.BuildServiceProvider();
        }
        [TestMethod]
        public async Task StartProceesor()
        {
            var processor = serviceProvider.GetRequiredService<GamificationAutomatedProcessor>();
            await processor.Process();
        }
    }
}
