﻿using Libraries.CommonEnums;
using System;
using System.Collections.Generic;
using System.Text;

namespace Library.CommonHelpers
{
    public class RoleToRoleIdStringConversion
    {
        public static string GetOldIdStringFromRole(PortalUserRole role)
        {
            switch (role)
            {
                case PortalUserRole.ClientEmployee:
                    return "ESMId";
                case PortalUserRole.AreaSalesManager:
                    return "ASMId";
                case PortalUserRole.RegionalSalesManager:
                    return "RSMId";
                case PortalUserRole.ZonalSalesManager:
                    return "ZSMId";
                case PortalUserRole.NationalSalesManager:
                    return "NSMId";
                case PortalUserRole.GlobalSalesManager:
                    return "GSMId";
                default:
                    return "ESMId";
            }
        }
        public static string GetNewIdStringFromRole(PortalUserRole role)
        {
            switch (role)
            {
                case PortalUserRole.ClientEmployee:
                    return "ESMId";
                case PortalUserRole.AreaSalesManager:
                    return "ASMUserId";
                case PortalUserRole.RegionalSalesManager:
                    return "RSMUserId";
                case PortalUserRole.ZonalSalesManager:
                    return "ZSMUserId";
                case PortalUserRole.NationalSalesManager:
                    return "NSMUserId";
                case PortalUserRole.GlobalSalesManager:
                    return "GSMUserId";
                default:
                    return "ESMId";
            }
        }
    }
}
