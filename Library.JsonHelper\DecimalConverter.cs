﻿using Newtonsoft.Json;
using System;
using System.Runtime.InteropServices;

namespace Library.JsonHelper
{
    /// <summary>
    /// Custom Decimal Json Converter
    /// </summary>
    public class CustomDecimalJsonConverter : JsonConverter
    {
        public CustomDecimalJsonConverter()
        {
        }

        public override bool CanRead
        {
            get
            {
                return false;
            }
        }

        public override object ReadJson(JsonReader reader, Type objectType, object existingValue, JsonSerializer serializer)
        {
            throw new NotImplementedException("Error reading Json value.");
        }

        public override bool CanConvert(Type objType)
        {
            return (objType == typeof(decimal) || objType == typeof(float) || objType == typeof(double));
        }

        public override void WriteJson(JsonWriter jWriter, object value, JsonSerializer jSerializer)
        {
            if (CustomDecimalJsonConverter.IsWholeValue(value))
            {
                jWriter.WriteRawValue(JsonConvert.ToString(Convert.ToInt64(value)));
            }
            else if (value is decimal || value is float || value is double)
            {
                //Date: 04-10-2021; Asana: https://app.asana.com/0/305436650865282/1200941749067687/f
                //change: Remove rounding to 2 decimal places. The converter is only referenced in Flexible report code and tests
                jWriter.WriteRawValue(JsonConvert.ToString(Convert.ToDecimal(value)));
            }
            else
            {

            }
        }

        private static bool IsWholeValue(object value)
        {
            if (value is decimal)
            {
                decimal decimalValue = (decimal)value;
                int scale = new DecimalScale(decimalValue).Scale; //Scale is the number of digits after the decimal point
                return scale == 0;
            }
            else if (value is float || value is double)
            {
                double doubleValue = (double)value;
                return doubleValue == Math.Truncate(doubleValue);
            }

            return false;
        }
    }
    [StructLayout(LayoutKind.Explicit)]
    struct DecimalScale
    {
        public DecimalScale(decimal value)
        {
            this = default;
            this.d = value;
        }

        [FieldOffset(0)]
        decimal d;

        [FieldOffset(0)]
        int flags;

        public int Scale => (flags >> 16) & 0x000000FF;
    }

}
