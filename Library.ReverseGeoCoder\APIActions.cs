﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Net.Http;
using Library.SlackService;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace Library.ReverseGeoCoder
{
    public class APIActions<T> where T : class
    {
        private readonly HttpClient _client;
        private readonly ErrorMessenger _errorMessanger;
        private readonly string channel;

        public APIActions(ErrorMessenger errorMessanger, string authToken = null, bool isRawAuth = false, int? timeout = null,string channel = "")
        {
            _client = new HttpClient();
            if (timeout.HasValue)
            {
                _client.Timeout = TimeSpan.FromSeconds(timeout.Value);
            }
            if (authToken != null)
            {
                _client.DefaultRequestHeaders.Authorization = isRawAuth ? new System.Net.Http.Headers.AuthenticationHeaderValue(authToken.Split(' ')[0], authToken.Split(' ')[1]) : new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", authToken);
            }

            this._errorMessanger = errorMessanger;
            this.channel = channel;
        }


        //public async Task<List<T>> GetList(string api)
        //{
        //    HttpResponseMessage response = await _client.GetAsync(api).ConfigureAwait(false);
        //    string responseBody = await response.Content.ReadAsStringAsync();
        //    if (response.IsSuccessStatusCode)
        //    {
        //        var data = JsonConvert.DeserializeObject<List<T>>(responseBody);
        //        return data;
        //    }
        //    else
        //    {
        //        _errorMessanger.SendToSlack($"Dependency Failure at {api}", responseBody);
        //        throw new Exception($"Dependency Failed to respond Properly at {api}");
        //    }
        //}

        //public async Task<List<T>> PostForResultList(string api, object body)
        //{
        //    HttpResponseMessage response = await _client.PostAsJsonAsync(api, body).ConfigureAwait(false);
        //    string responseBody = await response.Content.ReadAsStringAsync();
        //    if (response.IsSuccessStatusCode)
        //    {
        //        var data = JsonConvert.DeserializeObject<List<T>>(responseBody);
        //        return data;
        //    }
        //    else
        //    {
        //        _errorMessanger.SendToSlack($"Dependency Failure at {api}", responseBody);
        //        throw new Exception($"Dependency Failed to respond Properly at {api}");
        //    }
        //}

        //public async Task<T> Post(string api, object body)
        //{
        //    HttpResponseMessage response = await _client.PostAsJsonAsync(api, body).ConfigureAwait(false);
        //    string responseBody = await response.Content.ReadAsStringAsync();
        //    if (response.IsSuccessStatusCode)
        //    {
        //        var data = JsonConvert.DeserializeObject<T>(responseBody);
        //        return data;
        //    }
        //    else
        //    {
        //        _errorMessanger.SendToSlack($"Dependency Failure at {api}", responseBody);
        //        throw new Exception($"Dependency Failed to respond Properly at {api}");
        //    }
        //}

        public async Task<T> Get(string api, int retryCount = 0)
        {
            HttpResponseMessage response;
            do
            {
                response = await _client.GetAsync(api).ConfigureAwait(false);
                if (response.IsSuccessStatusCode)
                {
                    string responseBody = await response.Content.ReadAsStringAsync();
                    var data = JsonConvert.DeserializeObject<T>(responseBody);
                    return data;
                }
                retryCount--;
                await Task.Delay(3000);
            } while (retryCount >= 0);

            string errorBody = await response.Content.ReadAsStringAsync();
            await _errorMessanger.SendToSlackGeoCoding($"Dependency Failure at {api}", errorBody, "appapi_geocoding_issues");
            throw new Exception($"Dependency Failed to respond Properly at {api}");
        }
    }
}
