﻿using System;
using System.Collections.Generic;
using System.Text;

namespace GamificationAutomatedProcessor.DbStorage.DbModels
{
    public class PositionCodeEntityMapping
    {
        public long Id { get; set; }
        public long CompanyId { get; set; }
        public long PositionCodeId { get; set; }
        public long EntityId { get; set; }
        public bool IsDetached { get; set; }
        public DateTime CreatedAt { get; set; }
        public string CreationContext { get; set; }
    }
}
