﻿using Libraries.CommonEnums;
using Libraries.PerspectiveColumns.Interface;
using System.Collections.Generic;

namespace Libraries.PerspectiveColumns
{
    public class NoSalesReasonPerspective : IPerspective
    {
        //private readonly linkNames linkNames;

        public NoSalesReasonPerspective(long companyId)
        {
            //linkNames = InMemorySettings.GetLinkNames(companyId);
        }
        List<PerspectiveColumnModel> IPerspective.Columns { get => Columns; set => throw new System.NotImplementedException(); }
        public List<PerspectiveColumnModel> Columns => new List<PerspectiveColumnModel> {
                    new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = "GSM" , Name = "GSM" , IsMeasure = true , IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.CountDistinct, IsOtherMeasure = true, IsPivot = false},
                    new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = /*linkNames.*/ "NationalSalesManager" , Name = "NSM", IsMeasure = true,  IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct, IsOtherMeasure = true, IsPivot = false},
                    new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = /*linkNames.*/ "ZonalSalesManager" , Name = "ZSM", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct, IsOtherMeasure = true, IsPivot = false},
                    new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = /*linkNames.*/ "RegionalSalesManager" , Name = "RSM", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct, IsOtherMeasure = true, IsPivot = false},
                    new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = /*linkNames.*/ "AreaSalesManager" , Name = "ASM", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct, IsOtherMeasure = true, IsPivot = false},
                    new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = "Reporting Manager", Name = "ReportingManager", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct},
                    new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = "FieldUser Name" , Name = "FieldUserName", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct, IsOtherMeasure = true},
                    new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = "Rank" , Name = "FieldUserRank", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct, IsOtherMeasure = true, IsPivot = true},
                    new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = "FieldUser HQ" , Name = "FieldUserHQ", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct},
                    new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = "Field User ERP ID" , Name = "FieldUserERPID", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct},
                    new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = "FieldUser MobileNumber" , Name = "FieldUserMobileNumber", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct},

                    new PerspectiveColumnModel() { Attribute = "Visit" , DisplayName = "Unproductive Visits", Name = "AttendanceId", IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Count},

                    new PerspectiveColumnModel() { Attribute = "Sales" , DisplayName = "SuperStockist" , Name = "SuperStockist", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Unknown, IsPivot = false},
                    new PerspectiveColumnModel() { Attribute = "Sales" , DisplayName = "SuperStockist Erp Id" , Name = "SuperStockistErpId", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Unknown, IsPivot = false},
                    new PerspectiveColumnModel() { Attribute = "Sales" , DisplayName = "Distributor" , Name = "Distributor", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct, IsOtherMeasure = true, IsPivot = true},
                    new PerspectiveColumnModel() { Attribute = "Sales" , DisplayName = "Distributor Erp Id" , Name = "DistributorErpId", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct, IsOtherMeasure = true, IsPivot = true},
                    new PerspectiveColumnModel() { Attribute = "Sales" , DisplayName = /*linkNames.*/ "Beat" , Name = "Beat", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct},
                    new PerspectiveColumnModel() { Attribute = "Sales" , DisplayName = /*linkNames.*/ "Territory" , Name = "Territory", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct},
                    new PerspectiveColumnModel() { Attribute = "Sales" , DisplayName = /*linkNames.*/ "Zone" , Name = "Zone", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct, IsOtherMeasure = true, IsPivot = true},
                    new PerspectiveColumnModel() { Attribute = "Sales" , DisplayName = /*linkNames.*/ "Region" , Name = "Region", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct, IsOtherMeasure = true, IsPivot = true},
                    new PerspectiveColumnModel() { Attribute = "Sales" , DisplayName = "Order Qty (Unit)" , Name = "OrderInUnits", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},
                    new PerspectiveColumnModel() { Attribute = "Sales" , DisplayName = "FOC" , Name = "SchemeQty", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},
                    new PerspectiveColumnModel() { Attribute = "Sales" , DisplayName = "Scheme Discount" , Name = "SchemeDiscount", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},
                    new PerspectiveColumnModel() { Attribute = "Sales" , DisplayName = "Discount" , Name = "ProductWiseDiscount", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},
                    new PerspectiveColumnModel() { Attribute = "Sales" , DisplayName = "Value" , Name = "Value", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},
                    new PerspectiveColumnModel() { Attribute = "Sales" , DisplayName = "Drop Size" , Name = "AvgValue", IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Sum},
                    new PerspectiveColumnModel() { Attribute = "Sales" , DisplayName = "NetValue" , Name = "NetValue", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},
                    new PerspectiveColumnModel() { Attribute = "Sales" , DisplayName = "Order Qty (Std Unit)" , Name = "OrderQtyInStdUnit", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},
                    new PerspectiveColumnModel() { Attribute = "Sales" , DisplayName = "Retailer Stock Value" , Name = "RetailerStockInRevenue", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},
                    new PerspectiveColumnModel() { Attribute = "Sales" , DisplayName = "Retailer Stock Qty (Unit)" , Name = "RetailerStockQty", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},
                    new PerspectiveColumnModel() { Attribute = "Sales" , DisplayName = "Retailer Stock Qty (Std Unit)" , Name = "RetailerStockInStdUnits", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},
                    new PerspectiveColumnModel() { Attribute = "Sales" , DisplayName = "Net Value (Dispatch)" , Name = "DispatchInRevenue", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},
                    new PerspectiveColumnModel() { Attribute = "Sales" , DisplayName = "Dispatch Qty (Unit)" , Name = "DispatchInUnits", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},
                    new PerspectiveColumnModel() { Attribute = "Sales" , DisplayName = "Dispatch Qty (Std Unit)" , Name = "DispatchInStdUnits", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},
                    new PerspectiveColumnModel() { Attribute = "Sales" , DisplayName = "Dispatch Status" , Name = "DispatchStatus", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Unknown},
                    new PerspectiveColumnModel() { Attribute = "Sales" , DisplayName = "No Sales Reason" , Name = "NoSalesReason", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Count},
                    new PerspectiveColumnModel() { Attribute = "Sales" , DisplayName = "No Sales Reason Category" , Name = "NoSalesReasonCategory", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Count},
                    new PerspectiveColumnModel() { Attribute = "Sales" , DisplayName = "Productive Visits" , Name = "ProductiveVisits", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Value},
                    new PerspectiveColumnModel() { Attribute = "Sales" , DisplayName = "No Sale Visits" , Name = "NoSaleVisits", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Count},
                    new PerspectiveColumnModel() { Attribute = "Sales" , DisplayName = "Visits" , Name = "Visits", IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Count},
                    new PerspectiveColumnModel() { Attribute = "Sales" , DisplayName = "UTC" , Name = "UTC", IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Value},
                    new PerspectiveColumnModel() { Attribute = "Sales" , DisplayName = "UPC" , Name = "UPC", IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Value},
                    new PerspectiveColumnModel() { Attribute = "Sales" , DisplayName = "OVC TC" , Name = "OVCTC", IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Value},
                    new PerspectiveColumnModel() { Attribute = "Sales" , DisplayName = "OVC PC" , Name = "OVCPC", IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Value},
                    new PerspectiveColumnModel() { Attribute = "Sales" , DisplayName = "OVT TC" , Name = "OVTTC", IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Value},
                    new PerspectiveColumnModel() { Attribute = "Sales" , DisplayName = "OVT PC" , Name = "OVTPC", IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Value},



                    new PerspectiveColumnModel() { Attribute = "Time" , DisplayName = "Time" , Name = "Time", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct},
                    new PerspectiveColumnModel() { Attribute = "Time" , DisplayName = "Date" , Name = "Date", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct},
                    new PerspectiveColumnModel() { Attribute = "Time" , DisplayName = "Month" , Name = "Month", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct},
                    new PerspectiveColumnModel() { Attribute = "Time" , DisplayName = "Week" , Name = "Week", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct},
                    new PerspectiveColumnModel() { Attribute = "Time" , DisplayName = "Telephonic Validated Calls" , Name = "TelephonicValidated", IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Sum},
                    new PerspectiveColumnModel() { Attribute = "Time" , DisplayName = "Telephonic Validated Time" , Name = "TotalValidatedTime", IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Sum},

                    new PerspectiveColumnModel() { Attribute = "Shop" , DisplayName = /*linkNames.*/ "Outlets", Name = "Shop", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct},
                    new PerspectiveColumnModel() { Attribute = "Shop" , DisplayName = /*linkNames.*/ "Outlets" + "ErpId" , Name = "ShopErpId", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct},
                    new PerspectiveColumnModel() { Attribute = "Shop" , DisplayName = /*linkNames.*/ "Outlets" + "OwnersName" , Name = "ShopOwnersName", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct},
                    new PerspectiveColumnModel() { Attribute = "Shop" , DisplayName = /*linkNames.*/ "Outlets" + "OwnersNo" , Name = "ShopOwnersNumber", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct},
                    new PerspectiveColumnModel() { Attribute = "Shop" , DisplayName = /*linkNames.*/ "Outlets" + "Address" , Name = "ShopAddress", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct},
                    new PerspectiveColumnModel() { Attribute = "Shop" , DisplayName = /*linkNames.*/ "Outlets" + "Market" , Name = "ShopMarket", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct},
                    new PerspectiveColumnModel() { Attribute = "Shop" , DisplayName = /*linkNames.*/ "Outlets" + "SubCity" , Name = "ShopSubCity", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct},
                    new PerspectiveColumnModel() { Attribute = "Shop" , DisplayName = /*linkNames.*/ "Outlets" + "City" , Name = "ShopCity", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct},
                    new PerspectiveColumnModel() { Attribute = "Shop" , DisplayName = /*linkNames.*/ "Outlets" + "State" , Name = "ShopState", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct},
                    new PerspectiveColumnModel() { Attribute = "Shop" , DisplayName = /*linkNames.*/ "Outlets" + "Type" , Name = "ShopType", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct},
                    new PerspectiveColumnModel() { Attribute = "Shop" , DisplayName = /*linkNames.*/ "Outlets" + "Segmentation" , Name = "ShopSegmentation", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct},
                    new PerspectiveColumnModel() { Attribute = "Shop" , DisplayName = "Focussed" + /*linkNames.*/ "Outlets" , Name = "FocussedShop", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct},
                    new PerspectiveColumnModel() { Attribute = "Sales Territory" , DisplayName = "Level5" , Name = "Level5" , IsMeasure = true , IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsPivot = false , IsOtherMeasure = true },
                    new PerspectiveColumnModel() { Attribute = "Sales Territory" , DisplayName = "Level6" , Name = "Level6" , IsMeasure = true , IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsPivot = false , IsOtherMeasure = true },
                    new PerspectiveColumnModel() { Attribute = "Sales Territory" , DisplayName = "Level7" , Name = "Level7" , IsMeasure = true , IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsPivot = false , IsOtherMeasure = true },
                    new PerspectiveColumnModel() { Attribute = "Position" , DisplayName = "L8Position" , Name = "L8Position" , IsMeasure = true  , IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsPivot = false , IsOtherMeasure = true },
                    new PerspectiveColumnModel() { Attribute = "Position" , DisplayName = "L7Position" , Name = "L7Position" , IsMeasure = true  , IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsPivot = false , IsOtherMeasure = true },
                    new PerspectiveColumnModel() { Attribute = "Position" , DisplayName = "L6Position" , Name = "L6Position" , IsMeasure = true  , IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsPivot = false , IsOtherMeasure = true },
                    new PerspectiveColumnModel() { Attribute = "Position" , DisplayName = "L5Position" , Name = "L5Position" , IsMeasure = true  , IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsPivot = false , IsOtherMeasure = true },
                    new PerspectiveColumnModel() { Attribute = "Position" , DisplayName = "L4Position" , Name = "L4Position" , IsMeasure = true  , IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsPivot = false , IsOtherMeasure = true },
                    new PerspectiveColumnModel() { Attribute = "Position" , DisplayName = "L3Position" , Name = "L3Position" , IsMeasure = true  , IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsPivot = false , IsOtherMeasure = true },
                    new PerspectiveColumnModel() { Attribute = "Position" , DisplayName = "L2Position" , Name = "L2Position" , IsMeasure = true  , IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsPivot = false , IsOtherMeasure = true },
                    new PerspectiveColumnModel() { Attribute = "Position" , DisplayName = "L1Position" , Name = "L1Position" , IsMeasure = true  , IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsPivot = false , IsOtherMeasure = true },
                };

    }
}
