﻿using Microsoft.WindowsAzure.Storage;
using Microsoft.WindowsAzure.Storage.Auth;
using Microsoft.WindowsAzure.Storage.Queue;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace Library.Infrastructure.QueueService
{
    public class QueueHandler<T>
    {
        private readonly string queueString;
        private readonly string connectionString;

        public QueueHandler(string queue,string connectionString)
        {
            this.queueString = queue;
            this.connectionString = connectionString;
        }

        public async Task AddtoQueue( T data)
        {
            var account = CloudStorageAccount.Parse(connectionString);
            var queueClient = account.CreateCloudQueueClient();
            var queue = queueClient.GetQueueReference(queueString);

            var msg = new CloudQueueMessage(JsonConvert.SerializeObject(data));
            await queue.AddMessageAsync(msg).ConfigureAwait(false);
        }

        public async Task AddtoGridQueue(T data)
        {
            var account = CloudStorageAccount.Parse(connectionString);
            var queueClient = account.CreateCloudQueueClient();
            var queue = queueClient.GetQueueReference(queueString);

            var queueEvent = new GridEvent<T>
            {
                Data = data,
                Id = Guid.NewGuid().ToString(),
                EventTime = DateTime.UtcNow,
                EventType = "Generic",
                Subject = queueString
            };


            var msg = new CloudQueueMessage(JsonConvert.SerializeObject(queueEvent));
            await queue.AddMessageAsync(msg).ConfigureAwait(false);
        }
    }

    
}
