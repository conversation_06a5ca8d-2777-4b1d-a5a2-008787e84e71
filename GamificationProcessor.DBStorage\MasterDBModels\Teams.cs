﻿using Libraries.CommonEnums;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text;

namespace GamificationProcessor.DBStorage.MasterDBModels
{
    [Table("Teams")]
    public class Teams
    {
        public long Id { get; set; }

        public string Name { get; set; }

        public bool IsActive { get; set; }

        public long CompanyId { get; set; }

        public DateTime CreatedAt { get; set; }
        public string CreationContext { get; set; }
        public DateTime LastUpdatedAt { get; set; }
    }
}
