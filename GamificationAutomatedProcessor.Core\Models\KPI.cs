﻿using System;
using System.Collections.Generic;
using System.Text;
using Libraries.CommonEnums;


namespace GamificationAutomatedProcessor.Core.Models
{
    public class KPI
    {
        public long Id { get; set; }
        public string CreationContext { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime LastUpdatedAt { get; set; }
        public Frequency Frequency { get; set; }
        public UserType UserType { get; set; }
        public Objective Objective { get; set; }
        public Calculation Calculation { get; set; }
        public KpiMeasure Measure { get; set; }
        public bool IsQualifier { get; set; }
        public string Description { get; set; }
        public KPIType KPIType { get; set; }
        public string SQLQuery { get; set; }
        public bool IsDeactivated { get; set; }
        public string Name { get; set; }
        public string UIName { get; set; }
        public int Sequence { get; set; }
        public Relation Relation { get; set; }
        public string MasterSQLQuery { get; set; }
    }
    public enum Frequency
    {
        Daily,
        GamePeriod
    }
    public enum UserType
    {
        AllFieldUsers,
        ManagerAsAFIeldUser,
        FieldUserAllowedToBookOrder
    }
    public enum Objective
    {
        Coverage,
        Sales,
        Discipline
    }
    public enum Calculation
    {
        App,
        Backend
    }
    public enum KPIType
    {
        Positive,
        Negative
    }
    public enum Relation
    {
        UseDirectReportDb,
        UseDirectMasterDb,
        ReportDbDividedByMasterDb,
        MasterDbDividedByReportDb

    }
}
