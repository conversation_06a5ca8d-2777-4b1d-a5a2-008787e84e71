﻿using Libraries.CommonEnums;
using Libraries.PerspectiveColumns.Interface;
using System.Collections.Generic;

namespace Libraries.PerspectiveColumns
{
    public class LiveDataSalesPerspective : IPerspective
    {
        //private readonly linkNames linkNames;

        public LiveDataSalesPerspective(long companyId)
        {
            //linkNames = InMemorySettings.GetLinkNames(companyId);
        }
        List<PerspectiveColumnModel> IPerspective.Columns { get => Columns; set => throw new System.NotImplementedException(); }

        public List<PerspectiveColumnModel> Columns => new List<PerspectiveColumnModel>
        {
            new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = /*linkNames.*/ "GlobalSalesManager" , Name = "GSM" , IsMeasure = true , IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.CountDistinct},
            new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = /*linkNames.*/ "NationalSalesManager" , Name = "NSM", IsMeasure = true,  IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct},
            new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = /*linkNames.*/ "ZonalSalesManager" , Name = "ZSM", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct},
            new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = /*linkNames.*/ "RegionalSalesManager" , Name = "RSM", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct},
            new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = /*linkNames.*/ "AreaSalesManager" , Name = "ASM", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct},
            new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = /*linkNames.*/ "Employee" , Name = "ESM", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct},
            new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = "Reporting Manager", Name = "ReportingManager", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Unknown},
            new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = "FieldUser Name" , Name = "FieldUserName", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct},
            new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = "Rank" , Name = "FieldUserRank", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Unknown},
            new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = "FieldUser HQ" , Name = "FieldUserHQ", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Unknown},
            new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = "Field User ERP ID" , Name = "FieldUserERPID", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Unknown},
            new PerspectiveColumnModel() { Attribute = "Sales" , DisplayName = "Distributor" , Name = "Distributor", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct},
            new PerspectiveColumnModel() { Attribute = "Sales" , DisplayName = "Distributor Erp Id" , Name = "DistributorErpId", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct},
            new PerspectiveColumnModel() { Attribute = "Sales Territory" , DisplayName = /*linkNames.*/ "Zone" , Name = "Zone", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct},
            new PerspectiveColumnModel() { Attribute = "Sales Territory" , DisplayName = /*linkNames.*/ "Region" , Name = "Region", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct},
            new PerspectiveColumnModel() { Attribute = "Product" , DisplayName = "Std. Unit" , Name = "ProductStandardUnit", IsMeasure = false, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Unknown},
            new PerspectiveColumnModel() { Attribute = "Product" , DisplayName = "Unit" , Name = "ProductUnit", IsMeasure = false, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Unknown},
            new PerspectiveColumnModel() { Attribute = "Product" , DisplayName = "ProductERPID" , Name = "ProductERPID", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct},
            new PerspectiveColumnModel() { Attribute = "Product" , DisplayName = "Product" , Name = "ProductName", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct},
            new PerspectiveColumnModel() { Attribute = "Product" , DisplayName = "SecondaryCategory" , Name = "SecondaryCategory", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct},
            new PerspectiveColumnModel() { Attribute = "Product" , DisplayName = "PrimaryCategory" , Name = "PrimaryCategory", IsMeasure = false,  IsDimension = true,PerspectiveMeasure = PerspectiveMeasure.CountDistinct},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "Discount" , Name = "ProductWiseDiscount", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "Focused Product" , Name = "IsFocused", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Bool},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "Promoted Product" , Name = "IsPromoted", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Bool},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "Fast Moving Product" , Name = "IsFastMoving", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Count},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "Order Qty (Unit)" , Name = "OrderInUnits", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "Retailer Stock Qty (Unit)" , Name = "RetailerStockQty", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "Order Qty (Std Unit)" , Name = "OrderQtyInStdUnit", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "Value" , Name = "Value", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "NetValue" , Name = "NetValue", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},

        };
    }
}
