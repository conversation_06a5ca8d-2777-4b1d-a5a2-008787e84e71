﻿using Library.Infrastructure.QueueService;
using Library.StorageWriter;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Library.SlackService
{
    public class ErrorMessenger
    {
        private readonly string channel;
        private readonly string masterStorageConnectionString;
        private readonly string username;

        public ErrorMessenger(string masterStorageConnectionString, string username, string channel)
        {
            this.channel = channel;
            this.masterStorageConnectionString = masterStorageConnectionString;
            this.username = username;

        }
        public async Task SendToSlack(Exception ex, string info)
        {
            var action = new QueueActions<SlackMessage>(QueueType.SlackGeneric, masterStorageConnectionString);
            await action.AddtoQueue("Fake", new SlackMessage
            {
                Channel = channel,
                Username = username,
                Text = info,
                Attachments = new List<Attachment>
                {
                    new Attachment
                    {
                        Title=$"Error: {ex?.GetBaseException().Message}",
                        Fields=GetFieldsForException(ex),
                        Color="danger"
                    }
                }
            }).ConfigureAwait(false);
        }
        public async Task SendToSlack(string info, string detailedInfo)
        {
            var action = new QueueActions<SlackMessage>(QueueType.SlackGeneric, masterStorageConnectionString);
            var blobWriter = new AnonymousBlobWriter(masterStorageConnectionString);

            var url = await blobWriter.WriteToBlob(Guid.NewGuid().ToString(), detailedInfo);

            await action.AddtoQueue("Fake", new SlackMessage
            {
                Channel = channel,
                Username = username,
                Text = $"{info}: {url}",
            }).ConfigureAwait(false);
        }

        private List<Field> GetFieldsForException(Exception exception)
        {
            var fields = new List<Field>();
            if (exception != null)
            {
                int i = 0;
                var ex = exception;
                do
                {
                    fields.Add(new Field
                    {
                        Short = false,
                        Title = $"Exception Level{i++}",
                        Value = ex.Message
                    });
                    ex = ex.InnerException;
                } while (ex != null);
                try
                {
                    var blobWriter = new AnonymousBlobWriter(masterStorageConnectionString);
                    var url = blobWriter.WriteToBlob(Guid.NewGuid().ToString() + ".txt", exception?.ToString()).Result;
                    fields.Add(new Field
                    {
                        Short = true,
                        Title = $"StackTrace",
                        Value = url
                    });
                }
                catch
                {
                    fields.Add(new Field
                    {
                        Short = true,
                        Title = $"StackTrace",
                        Value = "Some Error Occured while Attaching Details!"
                    });
                }

            }
            else
            {
                return null;
            }
            return fields;
        }
        public async Task SendToSlack(string info)
        {
            var action = new QueueActions<SlackMessage>(QueueType.SlackGeneric, masterStorageConnectionString);
            await action.AddtoQueue("Fake", new SlackMessage
            {
                Channel = channel,
                Username = username,
                Text = info,
            }).ConfigureAwait(false);

        }
        public async Task SendGeoCodingErorToSlack(string info,string channelName)
        {
            var action = new QueueActions<SlackMessage>(QueueType.SlackGeneric, masterStorageConnectionString);
            await action.AddtoQueue("Fake", new SlackMessage
            {
                Channel = channelName,
                Username = username,
                Text = info,
            }).ConfigureAwait(false);

        }
        public async Task SendToSlackGeoCoding(string info, string detailedInfo, string channelName)
        {
            var action = new QueueActions<SlackMessage>(QueueType.SlackGeneric, masterStorageConnectionString);
            var blobWriter = new AnonymousBlobWriter(masterStorageConnectionString);

            var url = await blobWriter.WriteToBlob(Guid.NewGuid().ToString(), detailedInfo);

            await action.AddtoQueue("Fake", new SlackMessage
            {
                Channel = channelName,
                Username = username,
                Text = $"{info}: {url}",
            }).ConfigureAwait(false);
        }
        public async Task SendToSlack(string info, string detailedInfo, string channelName)
        {
            var action = new QueueActions<SlackMessage>(QueueType.SlackGeneric, masterStorageConnectionString);
            var blobWriter = new AnonymousBlobWriter(masterStorageConnectionString);

            var url = await blobWriter.WriteToBlob(Guid.NewGuid().ToString(), detailedInfo);

            await action.AddtoQueue("Fake", new SlackMessage
            {
                Channel = channelName,
                Username = username,
                Text = $"{info}:{detailedInfo}: {url}",
            }).ConfigureAwait(false);
        }
    }
}

