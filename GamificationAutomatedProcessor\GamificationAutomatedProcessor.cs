﻿using GamificationAutomatedProcessor.Configuration;
using GamificationAutomatedProcessor.Core.Services;
using Library.AsyncLock;
using Library.ResiliencyHelpers;
using Library.SlackService;
using Microsoft.Azure.WebJobs;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace GamificationAutomatedProcessor
{

    public class GamificationAutomatedProcessor
    {
        private readonly IGamificationAutomatedProcessor gamificationAutomatedProcessor;

        public GamificationAutomatedProcessor(IServiceProvider serviceProvider, ResilientAction resilientAction,IGamificationAutomatedProcessor gamificationAutomatedProcessor)
        {
            this.gamificationAutomatedProcessor=gamificationAutomatedProcessor;
        }

        public async Task Process()
        {
            var forDate = DateTime.UtcNow.AddMinutes(330);
            try
            {
                Console.WriteLine($"Started Running GamificationAutomatedProcessor for {forDate}!");
                await gamificationAutomatedProcessor.Process(forDate.Date);
                Console.WriteLine("GamificationAutomatedProcessor finished Successfully");

            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error while working on GamificationAutomatedProcessor!\n{forDate} :\n{ex}");
            }
        }
    }
}
