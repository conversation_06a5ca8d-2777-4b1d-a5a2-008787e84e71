﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;
using ResilientHttpClient;

namespace GamificationProcessor.Core.Helpers
{
    public class ReportDataAPIsHelper : FAResilientHttpClient
    {
        private readonly AppConfigSettings configSettig;

        public ReportDataAPIsHelper(AppConfigSettings configSettig) : base(1)
        {
            base.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", configSettig.reportApiToken);
            this.configSettig = configSettig;
        }

        public async Task<string> Get(string api)
        {
            var apiWithBaseurl = $"{configSettig.reportApiBaseUrl}{api}";
            var data = await base.GetAsync(apiWithBaseurl);
            data.EnsureSuccessStatusCode();
            return await data.Content.ReadAsStringAsync();
        }
    }
}
