﻿using Libraries.CommonEnums;
using Libraries.PerspectiveColumns.Interface;
using System.Collections.Generic;

namespace Libraries.PerspectiveColumns
{
    public class MTProductSalePerspective : IPerspective
    {
        //private linkNames linkNames;

        public MTProductSalePerspective(long companyId)
        {
            //linkNames = InMemorySettings.GetLinkNames(companyId);
        }
        List<PerspectiveColumnModel> IPerspective.Columns { get => Columns; set => throw new System.NotImplementedException(); }
        public List<PerspectiveColumnModel> Columns => new List<PerspectiveColumnModel>
        {
            new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = "National Sales Manager" , Name = "NSM", IsMeasure = true,  IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct, IsPivot = true , IsOtherMeasure= true},
            new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = "Zonal Sales Manager" , Name = "ZSM", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsPivot = true , IsOtherMeasure= true},
            new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = "Regional Sales Manager" , Name = "RSM", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct, IsPivot = true, IsOtherMeasure = true},
            new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = "Area Sales Manager" , Name = "ASM", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct, IsPivot = true, IsOtherMeasure = true},
            new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = "Employee" , Name = "ESM", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct, IsOtherMeasure = true},
            new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = "Reporting Manager", Name = "ReportingManager", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Unknown},
            new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = "FieldUser Name" , Name = "FieldUserName", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct, IsOtherMeasure = true},
            new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = "Rank" , Name = "FieldUserRank", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Unknown , IsPivot = true, IsOtherMeasure = true },
            new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = "FieldUser HQ" , Name = "FieldUserHQ", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Unknown},
            new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = "Field User ERP ID" , Name = "FieldUserERPID", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Unknown},
            new PerspectiveColumnModel() { Attribute = "Sales Territory" , DisplayName = "Beat" , Name = "Beat", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct, IsOtherMeasure = true},
            new PerspectiveColumnModel() { Attribute = "Sales Territory" , DisplayName = "Territory" , Name = "Territory", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsOtherMeasure = true},
            new PerspectiveColumnModel() { Attribute = "Sales Territory" , DisplayName = "Zone" , Name = "Zone", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsPivot = true, IsOtherMeasure = true },
            new PerspectiveColumnModel() { Attribute = "Sales Territory" , DisplayName = "Region" , Name = "Region", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct, IsPivot = true , IsOtherMeasure = true},
            new PerspectiveColumnModel() { Attribute = "Sales Territory" , DisplayName = "Level5" , Name = "Level5", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsPivot = true, IsOtherMeasure = true },
            new PerspectiveColumnModel() { Attribute = "Sales Territory" , DisplayName = "Level6" , Name = "Level6", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsPivot = true, IsOtherMeasure = true },
            new PerspectiveColumnModel() { Attribute = "Sales Territory" , DisplayName = "Level7" , Name = "Level7", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsPivot = true, IsOtherMeasure = true },
            new PerspectiveColumnModel() { Attribute = /*linkNames.*/"Outlets" , DisplayName = /*linkNames.*/"Outlets" , Name = "Shop", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsOtherMeasure = true},
            new PerspectiveColumnModel() { Attribute = /*linkNames.*/"Outlets" , DisplayName = /*linkNames.*/"Outlets"+ " ERPID" , Name = "ShopERPID", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct, IsOtherMeasure = true},
            new PerspectiveColumnModel() { Attribute = /*linkNames.*/"Outlets" , DisplayName = /*linkNames.*/"Outlets"+" Owners Name" , Name = "ShopOwnersName", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Unknown},
            new PerspectiveColumnModel() { Attribute = /*linkNames.*/"Outlets" , DisplayName = /*linkNames.*/"Outlets"+" Owners Number" , Name = "ShopOwnersNumber", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Unknown},
            new PerspectiveColumnModel() { Attribute = /*linkNames.*/"Outlets" , DisplayName = /*linkNames.*/"Outlets"+" Address" , Name = "ShopAddress", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Unknown},
            new PerspectiveColumnModel() { Attribute = /*linkNames.*/"Outlets" , DisplayName = /*linkNames.*/"Outlets"+" Market" , Name = "ShopMarket", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Unknown},
            new PerspectiveColumnModel() { Attribute = /*linkNames.*/"Outlets" , DisplayName = /*linkNames.*/"Outlets"+" Town" , Name = "ShopSubCity", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsOtherMeasure = true},
            new PerspectiveColumnModel() { Attribute = /*linkNames.*/"Outlets" , DisplayName = /*linkNames.*/"Outlets"+" City" , Name = "ShopCity", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct, IsOtherMeasure = true},
            new PerspectiveColumnModel() { Attribute = /*linkNames.*/"Outlets" , DisplayName = /*linkNames.*/"Outlets"+" State" , Name = "ShopState", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct, IsOtherMeasure = true},
            new PerspectiveColumnModel() { Attribute = /*linkNames.*/"Outlets" , DisplayName = /*linkNames.*/"Outlets"+" Type" , Name = "ShopType", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Unknown},
            new PerspectiveColumnModel() { Attribute = /*linkNames.*/"Outlets" , DisplayName = /*linkNames.*/"Outlets"+" Segmentation" , Name = "ShopSegmentation", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Unknown},
            new PerspectiveColumnModel() { Attribute = "Visit" , DisplayName = "CheckIn Id" , Name = "AttendanceId", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct},
            new PerspectiveColumnModel() { Attribute = "Product" , DisplayName = "Conversion Factor" , Name = "ProductStandardUnitConversionFactor", IsMeasure = false, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Unknown},
            new PerspectiveColumnModel() { Attribute = "Product" , DisplayName = "Std. Unit" , Name = "ProductStandardUnit", IsMeasure = false, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Unknown},
            new PerspectiveColumnModel() { Attribute = "Product" , DisplayName = "Unit" , Name = "ProductUnit", IsMeasure = false, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Unknown},
            new PerspectiveColumnModel() { Attribute = "Product" , DisplayName = "ProductERPID" , Name = "ProductERPID", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct},
            new PerspectiveColumnModel() { Attribute = "Product" , DisplayName = "Product" , Name = "ProductName", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct, IsPivot = true},
            new PerspectiveColumnModel() { Attribute = "Product" , DisplayName = "SecondaryCategory" , Name = "SecondaryCategory", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsPivot = true},
            new PerspectiveColumnModel() { Attribute = "Product" , DisplayName = "PrimaryCategory", Name = "PrimaryCategory", IsMeasure = true,  IsDimension = true,PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsPivot = true},
            //new PerspectiveColumnModel() { Attribute = "Product" , DisplayName = "Product Divison" , Name = "ProductDivision",  IsMeasure = true,  IsDimension = true,PerspectiveMeasure = PerspectiveMeasure.CountDistinct, IsPivot = true },
            new PerspectiveColumnModel() { Attribute = "Time" , DisplayName = "Date" , Name = "Date", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Unknown , IsPivot = true },
            new PerspectiveColumnModel() { Attribute = "Time" , DisplayName = "Time" , Name = "Time", IsMeasure = false, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Unknown, NotForReports = true},
            new PerspectiveColumnModel() { Attribute = "Time" , DisplayName = "Month" , Name = "Month", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Unknown, IsPivot = true },
            //new PerspectiveColumnModel() { Attribute = "Time" , DisplayName = "Week" , Name = "Week", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Unknown, IsPivot = true },
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "Close to Expiry Stock Qty" , Name = "NearExpiryStockQuantity", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},
            //new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "Promoted Product" , Name = "IsPromoted", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Bool},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "Tertiary Sales (Unit)" , Name = "TertiarySalesInUnit", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "Tertiary Sales (Std. Unit)" , Name = "TertiarySalesInStdUnit", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "Tertiary Sales (Revenue)" , Name = "TertiarySalesValue", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "Closing Stock (Unit)" , Name = "ClosingStockInUnits", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "Closing Stock (Std. Unit)" , Name = "ClosingStockInStdUnit", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "Closing Stock (Revenue)" , Name = "ClosingStockInRevenue", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "CalculatedClosing Stock (Unit)" , Name = "CalculatedClosingStockInUnits", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "CalculatedClosing Stock (Std. Unit)" , Name = "CalculatedClosingStockInStdUnit", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "CalculatedClosing Stock (Revenue)" , Name = "CalculatedClosingStockInRevenue", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "Opening Stock Value" , Name = "OpeningStockInRevenue", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "Opening Stock Qty (Std Unit)" , Name = "OpeningStockInStdUnits", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "Opening Stock Qty (Unit)" , Name = "OpeningStockQty", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "Inward Stock Value" , Name = "InwardStockInRevenue", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "Inward Stock Qty (Std Unit)" , Name = "InwardStockInStdUnits", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "Inward Stock Qty (Unit)" , Name = "InwardStockQty", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "Return Quantity" , Name = "ReturnQuantity", IsMeasure = true , PerspectiveMeasure = PerspectiveMeasure.Sum},
        };
    }
}
