﻿using Libraries.CommonEnums;
using Libraries.PerspectiveColumns.Interface;
using System.Collections.Generic;

namespace Libraries.PerspectiveColumns
{
    public class ProductSalePerspective : IPerspective
    {
        //private linkNames linkNames;

        public ProductSalePerspective(long companyId)
        {
            //linkNames = InMemorySettings.GetLinkNames(companyId);
        }
        List<PerspectiveColumnModel> IPerspective.Columns { get => Columns; set => throw new System.NotImplementedException(); }
        public List<PerspectiveColumnModel> Columns => new List<PerspectiveColumnModel>
        {
            new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = /*/*linkNames.*/ "GlobalSalesManager" , Name = "GSM" , IsMeasure = true , IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.CountDistinct, IsPivot = false , IsOtherMeasure=true },
            new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = /*linkNames.*/ "NationalSalesManager" , Name = "NSM", IsMeasure = true,  IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct, IsPivot = false , IsOtherMeasure= true},
            new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = /*linkNames.*/ "ZonalSalesManager" , Name = "ZSM", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsPivot = false , IsOtherMeasure= true},
            new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = /*linkNames.*/ "RegionalSalesManager" , Name = "RSM", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct, IsPivot = false, IsOtherMeasure = true},
            new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = /*linkNames.*/ "AreaSalesManager" , Name = "ASM", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct, IsPivot = false, IsOtherMeasure = true},
            new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = /*linkNames.*/ "Employee" , Name = "ESM", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct, IsOtherMeasure = true},
            new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = "Reporting Manager", Name = "ReportingManager", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Unknown},
            new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = "FieldUser Name" , Name = "FieldUserName", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct, IsOtherMeasure = true},
            new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = "Rank" , Name = "FieldUserRank", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Unknown , IsPivot = false, IsOtherMeasure = true },
            new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = "FieldUser HQ" , Name = "FieldUserHQ", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Unknown},
            new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = "Field User ERP ID" , Name = "FieldUserERPID", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Unknown},
            new PerspectiveColumnModel() { Attribute = "Sales" , DisplayName = "SuperStockist" , Name = "SuperStockist", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Unknown, IsPivot = false},
            new PerspectiveColumnModel() { Attribute = "Sales" , DisplayName = "SuperStockist Erp Id" , Name = "SuperStockistErpId", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Unknown, IsPivot = false},
            new PerspectiveColumnModel() { Attribute = "Sales" , DisplayName = "Distributor" , Name = "Distributor", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct, IsPivot = false, IsOtherMeasure= true},
            new PerspectiveColumnModel() { Attribute = "Sales" , DisplayName = "Distributor Erp Id" , Name = "DistributorErpId", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct, IsPivot = false, IsOtherMeasure= true},
            new PerspectiveColumnModel() { Attribute = "Sales Territory" , DisplayName = /*linkNames.*/ "Beat" , Name = "Beat", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct, IsOtherMeasure = true},
            new PerspectiveColumnModel() { Attribute = "Sales Territory" , DisplayName = /*linkNames.*/ "Territory" , Name = "Territory", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsOtherMeasure = true},
            new PerspectiveColumnModel() { Attribute = "Sales Territory" , DisplayName = /*linkNames.*/"Zone" , Name = "Zone", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsPivot = false, IsOtherMeasure = true },
            new PerspectiveColumnModel() { Attribute = "Sales Territory" , DisplayName = /*linkNames.*/"Region" , Name = "Region", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct, IsPivot = false , IsOtherMeasure = true},
            new PerspectiveColumnModel() { Attribute = /*linkNames.*/"Outlets" , DisplayName = /*linkNames.*/"Outlets" , Name = "Shop", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsOtherMeasure = true},
            new PerspectiveColumnModel() { Attribute = /*linkNames.*/"Outlets" , DisplayName = /*linkNames.*/"Outlets"+ " ERPID" , Name = "ShopERPID", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct, IsOtherMeasure = true},
            new PerspectiveColumnModel() { Attribute = /*linkNames.*/"Outlets" , DisplayName = /*linkNames.*/"Outlets"+" Owners Name" , Name = "ShopOwnersName", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Unknown},
            new PerspectiveColumnModel() { Attribute = /*linkNames.*/"Outlets" , DisplayName = /*linkNames.*/"Outlets"+" Owners Number" , Name = "ShopOwnersNumber", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Unknown},
            new PerspectiveColumnModel() { Attribute = /*linkNames.*/"Outlets" , DisplayName = /*linkNames.*/"Outlets"+" Address" , Name = "ShopAddress", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Unknown},
            new PerspectiveColumnModel() { Attribute = /*linkNames.*/"Outlets" , DisplayName = /*linkNames.*/"Outlets"+" Market" , Name = "ShopMarket", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Unknown},
            new PerspectiveColumnModel() { Attribute = /*linkNames.*/"Outlets" , DisplayName = /*linkNames.*/"Outlets"+" Town" , Name = "ShopSubCity", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsOtherMeasure = true},
            new PerspectiveColumnModel() { Attribute = /*linkNames.*/"Outlets" , DisplayName = /*linkNames.*/"Outlets"+" City" , Name = "ShopCity", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct, IsOtherMeasure = true},
            new PerspectiveColumnModel() { Attribute = /*linkNames.*/"Outlets" , DisplayName = /*linkNames.*/"Outlets"+" State" , Name = "ShopState", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct, IsOtherMeasure = true},
            new PerspectiveColumnModel() { Attribute = /*linkNames.*/"Outlets" , DisplayName = /*linkNames.*/"Outlets"+" Type" , Name = "ShopType", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Unknown},
            new PerspectiveColumnModel() { Attribute = /*linkNames.*/"Outlets" , DisplayName = /*linkNames.*/"Outlets"+" Segmentation" , Name = "ShopSegmentation", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Unknown},
            new PerspectiveColumnModel() { Attribute = /*linkNames.*/"Outlets" , DisplayName = "Focussed "+/*linkNames.*/"Outlets", Name = "FocussedShop", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsOtherMeasure = true},
            new PerspectiveColumnModel() { Attribute = "Visit" , DisplayName = "Orders" , Name = "AttendanceId", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct},
            new PerspectiveColumnModel() { Attribute = "Product" , DisplayName = "Conversion Factor" , Name = "ProductStandardUnitConversionFactor", IsMeasure = false, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Unknown},
            new PerspectiveColumnModel() { Attribute = "Product" , DisplayName = "Std. Unit" , Name = "ProductStandardUnit", IsMeasure = false, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Unknown},
            new PerspectiveColumnModel() { Attribute = "Product" , DisplayName = "Unit" , Name = "ProductUnit", IsMeasure = false, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Unknown},
            new PerspectiveColumnModel() { Attribute = "Product" , DisplayName = "ProductERPID" , Name = "ProductERPID", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct},
            new PerspectiveColumnModel() { Attribute = "Product" , DisplayName = "Product" , Name = "ProductName", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct, IsPivot = true},            
            new PerspectiveColumnModel() { Attribute = "Product" , DisplayName = "Focussed Product Name" , Name = "FocussedProductName", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct},
            new PerspectiveColumnModel() { Attribute = "Product" , DisplayName = "SecondaryCategory" , Name = "SecondaryCategory", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsPivot = true},
            new PerspectiveColumnModel() { Attribute = "Product" , DisplayName = "PrimaryCategory" , Name = "PrimaryCategory", IsMeasure = true,  IsDimension = true,PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsPivot = true},
            new PerspectiveColumnModel() { Attribute = "Product" , DisplayName = "Alternate Category" , Name = "AlternateCategory",  IsMeasure = true,  IsDimension = true,PerspectiveMeasure = PerspectiveMeasure.CountDistinct, IsPivot = true },
            new PerspectiveColumnModel() { Attribute = "Product" , DisplayName = "Style" , Name = "DisplayCategory",  IsMeasure = true,  IsDimension = true,PerspectiveMeasure = PerspectiveMeasure.CountDistinct, IsPivot = true },
            //new PerspectiveColumnModel() { Attribute = "Product" , DisplayName = "PTR" , Name = "PTR", IsMeasure = false, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Unknown},
            //new PerspectiveColumnModel() { Attribute = "Product" , DisplayName = "Original PTR" , Name = "OriginalPTR", IsMeasure = false, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Unknown},
            new PerspectiveColumnModel() { Attribute = "Time" , DisplayName = "Date" , Name = "Date", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsPivot = true },
            new PerspectiveColumnModel() { Attribute = "Time" , DisplayName = "Time" , Name = "Time", IsMeasure = false, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Unknown, NotForReports = true},
            new PerspectiveColumnModel() { Attribute = "Time" , DisplayName = "Month" , Name = "Month", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct, IsPivot = true },
            new PerspectiveColumnModel() { Attribute = "Time" , DisplayName = "Week" , Name = "Week", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsPivot = true },
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "Close to Expiry Stock Qty" , Name = "NearExpiryStockQuantity", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "Fast Moving Product" , Name = "IsFastMoving", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Bool},
            //new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "Stock Quantity" , Name = "StockQuantity", IsMeasure = true , PerspectiveMeasure = PerspectiveMeasure.Sum},
            //new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "Stock Change" , Name = "StockChange", IsMeasure = true , PerspectiveMeasure = PerspectiveMeasure.Bool},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "Suggested Qty" , Name = "SuggestedQuantity", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "Discount" , Name = "ProductWiseDiscount", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "MRP" , Name = "ProductMRP", IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Value},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "Focused Product" , Name = "IsFocused", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Bool},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "Promoted Product" , Name = "IsPromoted", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Bool},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "Recommended Product" , Name = "IsRecommended", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Bool},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "Order Qty (Unit)" , Name = "OrderInUnits", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "Dispatch Status" , Name = "DispatchStatus", IsMeasure = false, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Unknown},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "Retailer Stock Value" , Name = "RetailerStockInRevenue", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "Retailer Stock Qty (Std Unit)" , Name = "RetailerStockInStdUnits", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "Retailer Stock Qty (Unit)" , Name = "RetailerStockQty", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "Net Value (Dispatch)" , Name = "DispatchInRevenue", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "Dispatch Qty (Std Unit)" , Name = "DispatchInStdUnits", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "Dispatch Qty (Unit)" , Name = "DispatchInUnits", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "Scheme Discount" , Name = "SchemeDiscount", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "FOC" , Name = "SchemeQty", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "Order Qty (Std Unit)" , Name = "OrderQtyInStdUnit", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "Value" , Name = "Value", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "NetValue" , Name = "NetValue", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "Assorted Product" , Name = "AssortedProduct", IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "Focussed Product" , Name = "FocussedProduct", IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "Must Sell Product" , Name = "MustSellProduct", IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "Unique Line Cut" , Name = "ULC", IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Value, IsOtherMeasure = true},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = /*linkNames.*/"UPC" , Name = "UPC", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Value, IsOtherMeasure = true},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "Focussed Product UPC" , Name = "FocussedProductUPC", IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Sum, IsOtherMeasure = false},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "Focussed Product Achivement (Std Units)" , Name = "FocussedProductStdUnits", IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Sum, IsOtherMeasure = false},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "Focussed Product Achivement (Revenue)" , Name = "FocussedProductRevenue", IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Sum, IsOtherMeasure = false},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "PC" , Name = "PC", IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Sum, IsOtherMeasure = true},
            //new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "Return Quantity" , Name = "ReturnQuantity", IsMeasure = true , PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Sales Territory" , DisplayName = "Level5" , Name = "Level5" , IsMeasure = true , IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsPivot = false , IsOtherMeasure = true },
            new PerspectiveColumnModel() { Attribute = "Sales Territory" , DisplayName = "Level6" , Name = "Level6" , IsMeasure = true , IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsPivot = false , IsOtherMeasure = true },
            new PerspectiveColumnModel() { Attribute = "Sales Territory" , DisplayName = "Level7" , Name = "Level7" , IsMeasure = true , IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsPivot = false , IsOtherMeasure = true },
            new PerspectiveColumnModel() { Attribute = "Position" , DisplayName = "L8Position" , Name = "L8Position" , IsMeasure = true  , IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsPivot = false , IsOtherMeasure = true },
            new PerspectiveColumnModel() { Attribute = "Position" , DisplayName = "L7Position" , Name = "L7Position" , IsMeasure = true  , IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsPivot = false , IsOtherMeasure = true },
            new PerspectiveColumnModel() { Attribute = "Position" , DisplayName = "L6Position" , Name = "L6Position" , IsMeasure = true  , IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsPivot = false , IsOtherMeasure = true },
            new PerspectiveColumnModel() { Attribute = "Position" , DisplayName = "L5Position" , Name = "L5Position" , IsMeasure = true  , IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsPivot = false , IsOtherMeasure = true },
            new PerspectiveColumnModel() { Attribute = "Position" , DisplayName = "L4Position" , Name = "L4Position" , IsMeasure = true  , IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsPivot = false , IsOtherMeasure = true },
            new PerspectiveColumnModel() { Attribute = "Position" , DisplayName = "L3Position" , Name = "L3Position" , IsMeasure = true  , IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsPivot = false , IsOtherMeasure = true },
            new PerspectiveColumnModel() { Attribute = "Position" , DisplayName = "L2Position" , Name = "L2Position" , IsMeasure = true  , IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsPivot = false , IsOtherMeasure = true },
            new PerspectiveColumnModel() { Attribute = "Position" , DisplayName = "L1Position" , Name = "L1Position" , IsMeasure = true  , IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.CountDistinct , IsPivot = false , IsOtherMeasure = true },
            new PerspectiveColumnModel() { Attribute = "Master Measure"  , DisplayName = "Focussed Product Target (Revenue)"          , Name = "FPTargetsRevenue"                 , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value },
            new PerspectiveColumnModel() { Attribute = "Master Measure"  , DisplayName = "Focussed Product Target (UPC)"          , Name = "FPTargetsUPC"                 , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value },
            new PerspectiveColumnModel() { Attribute = "Master Measure"  , DisplayName = "Focussed Product Target (Std Units)"          , Name = "FPTargetsStdUnits"                 , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value },
        };
    }
}
