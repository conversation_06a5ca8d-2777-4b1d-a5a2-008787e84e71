﻿using Microsoft.WindowsAzure.Storage;
using Microsoft.WindowsAzure.Storage.Auth;
using Microsoft.WindowsAzure.Storage.Queue;
using Newtonsoft.Json;
using System;
using System.Threading.Tasks;

namespace Library.Infrastructure.QueueService
{
    public class QueueActions<T> where T : IQueueMessage
    {
        private readonly QueueSubcription _queueSubscription;
        private readonly string masterConnectionString;

        public QueueActions(QueueType subscription, string masterConnectionString)
        {
            _queueSubscription = new QueueSubcription(subscription);
            this.masterConnectionString = masterConnectionString;
        }
        public async Task AddtoQueue(string ContextGuid, T data)
        {
            await AddtoQueue(ContextGuid, data, "Generic").ConfigureAwait(false);
        }
        private async Task AddtoQueue(string ContextGuid, T data, string eventType)
        {
            var account = CloudStorageAccount.Parse(masterConnectionString);
            var queueClient = account.CreateCloudQueueClient();
            var queue = queueClient.GetQueueReference(_queueSubscription.Queuestr);

            var queueEvent = new GridEvent<T>
            {
                Data = data,
                Id = ContextGuid ?? Guid.NewGuid().ToString(),
                EventTime = DateTime.UtcNow,
                EventType = eventType,
                Subject = _queueSubscription.QueueType.ToString()
            };
            var msg = new CloudQueueMessage(JsonConvert.SerializeObject(queueEvent));
            //await queue.CreateIfNotExistsAsync().ConfigureAwait(false);
            await queue.AddMessageAsync(msg).ConfigureAwait(false);
        }
    }
}
