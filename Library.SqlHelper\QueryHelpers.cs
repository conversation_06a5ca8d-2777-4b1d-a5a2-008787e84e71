﻿using Libraries.CommonEnums;
using System;
using System.Collections.Generic;
using System.Text;
using static Libraries.CommonEnums.TypesUsedInReports;

namespace Library.SqlHelper
{
    public static class QueryHelpers
    {
        public static class EmployeeHierrachyFilters
        {
            public static string EmployeeHierarchyNewIds(PortalUserRole userRole,long userId)
            {
                return $@"ISNULL(GSMUserId,0) = CASE 
                    WHEN '{userRole}'='{PortalUserRole.GlobalSalesManager}' THEN {userId}
                    ELSE  ISNULL(GSMUserId,0)
                    END
                    AND 
                    ISNULL(NSMUserId,0) = CASE 
                    WHEN '{userRole}'='{PortalUserRole.NationalSalesManager}' THEN {userId}
                    ELSE  ISNULL(NSMUserId,0)
                    END
                    AND 
                    ISNULL(ZSMUserId,0) = CASE 
                    WHEN '{userRole}'='{PortalUserRole.ZonalSalesManager}' THEN {userId}
                    ELSE ISNULL(ZSMUserId,0)
                    END
                    AND
                    ISNULL(RSMUserId,0) = CASE 
                    WHEN '{userRole}'='{PortalUserRole.RegionalSalesManager}' THEN {userId}
                    ELSE ISNULL(RSMUserId,0)
                    END   
                    AND
                    ISNULL(ASMUserId,0) = CASE 
                    WHEN '{userRole}'='{PortalUserRole.AreaSalesManager}' THEN {userId}
                    ELSE ISNULL(ASMUserId,0)
                    END  
                    AND
                    ESMId = CASE 
                    WHEN '{userRole}'='{PortalUserRole.ClientEmployee}' THEN {userId}
                    ELSE ESMId
                    END";
            }
            public static string ManagerIdsInString(PortalUserRole userRole,string placeholder="$$Ids$$")
            {
                switch (userRole)
                {
                    case PortalUserRole.GlobalSalesManager:
                        return $@"GSMId in ({placeholder})";
                    case PortalUserRole.NationalSalesManager:
                        return $@"NSMId in ({placeholder})";
                    case PortalUserRole.ZonalSalesManager:
                        return $@"ZSMId in ({placeholder})";
                    case PortalUserRole.RegionalSalesManager:
                        return $@"RSMId in ({placeholder})";
                    case PortalUserRole.AreaSalesManager:
                        return $@"ASMId in ({placeholder})";
                    case PortalUserRole.ClientEmployee:
                        return $@"ESMId in ({placeholder})";
                    default:return "1=1";
                }
            }
            public static string ManagerIds(PortalUserRole userRole)
            {
                switch (userRole)
                {
                    case PortalUserRole.GlobalSalesManager:
                        return $@"GSMId";
                    case PortalUserRole.NationalSalesManager:
                        return $@"NSMId";
                    case PortalUserRole.ZonalSalesManager:
                        return $@"ZSMId";
                    case PortalUserRole.RegionalSalesManager:
                        return $@"RSMId";
                    case PortalUserRole.AreaSalesManager:
                        return $@"ASMId";
                    case PortalUserRole.ClientEmployee:
                        return $@"ESMId";
                    default: return "ESMId";
                }
            }
            public static string ManagerNewIds(PortalUserRole userRole)
            {
                switch (userRole)
                {
                    case PortalUserRole.GlobalSalesManager:
                        return $@"GSMUSerId";
                    case PortalUserRole.NationalSalesManager:
                        return $@"NSMUserId";
                    case PortalUserRole.ZonalSalesManager:
                        return $@"ZSMUserId";
                    case PortalUserRole.RegionalSalesManager:
                        return $@"RSMUserId";
                    case PortalUserRole.AreaSalesManager:
                        return $@"ASMUserId";
                    case PortalUserRole.ClientEmployee:
                        return $@"ESMId";
                    default: return "ESMId";
                }
            }
            public static string ManagerNewIdsInString(PortalUserRole userRole, string placeholder = "$$Ids$$")
            {
                switch (userRole)
                {
                    case PortalUserRole.GlobalSalesManager:
                        return $@"GSMUserId in ({placeholder})";
                    case PortalUserRole.NationalSalesManager:
                        return $@"NSMUserId in ({placeholder})";
                    case PortalUserRole.ZonalSalesManager:
                        return $@"ZSMUserId in ({placeholder})";
                    case PortalUserRole.RegionalSalesManager:
                        return $@"RSMUserId in ({placeholder})";
                    case PortalUserRole.AreaSalesManager:
                        return $@"ASMUserId in ({placeholder})";
                    case PortalUserRole.ClientEmployee:
                        return $@"ESMId in ({placeholder})";
                    default: return "1=1";
                }
            }
            public static string EmployeeHierarchyOldIds(PortalUserRole userRole, string placeholder = "$$Ids$$")
            {
                switch (userRole)
                {
                    case PortalUserRole.GlobalSalesManager:
                        return $@"AND GSMId in ({placeholder})";
                    case PortalUserRole.NationalSalesManager:
                        return $@"AND NSMId in ({placeholder})";
                    case PortalUserRole.ZonalSalesManager:
                        return $@"AND ZSMId in ({placeholder})";
                    case PortalUserRole.RegionalSalesManager:
                        return $@"AND RSMId in ({placeholder})";
                    case PortalUserRole.AreaSalesManager:
                        return $@"AND ASMId in ({placeholder})";
                    case PortalUserRole.ClientEmployee:
                        return $@"AND ESMId in ({placeholder})";
                    default:
                        return "";
                }
            }
            public static string EmployeeHierarchyOldIds(PortalUserRole userRole, long userId)
            {
                return $@"ISNULL(GSMId,0) = CASE 
                    WHEN '{userRole}'='{PortalUserRole.GlobalSalesManager}' THEN {userId}
                    ELSE  ISNULL(GSMId,0)
                    END
                    AND 
                    ISNULL(NSMId,0) = CASE 
                    WHEN '{userRole}'='{PortalUserRole.NationalSalesManager}' THEN {userId}
                    ELSE  ISNULL(NSMId,0)
                    END
                    AND 
                    ISNULL(ZSMId,0) = CASE 
                    WHEN '{userRole}'='{PortalUserRole.ZonalSalesManager}' THEN {userId}
                    ELSE ISNULL(ZSMId,0)
                    END
                    AND
                    ISNULL(RSMId,0) = CASE 
                    WHEN '{userRole}'='{PortalUserRole.RegionalSalesManager}' THEN {userId}
                    ELSE ISNULL(RSMId,0)
                    END   
                    AND
                    ISNULL(ASMId,0) = CASE 
                    WHEN '{userRole}'='{PortalUserRole.AreaSalesManager}' THEN {userId}
                    ELSE ISNULL(ASMId,0)
                    END  
                    AND
                    ESMId = CASE 
                    WHEN '{userRole}'='{PortalUserRole.ClientEmployee}' THEN {userId}
                    ELSE ESMId
                    END";
            }            
        }
        public static class GeographicalHierarchyFilters
        {
            public static string GeographicalHierarchyTillRegion(GeographicalHierarchy geographicalHierarchy, long geoFilterId)
            {
                //return $@"ISNULL(Zone,0) = CASE 
                //    WHEN '{geographicalHierarchy}'='{GeographicalHierarchy.zoneId}' THEN {geoFilterId}
                //    ELSE  ISNULL(Zone,0)
                //    END
                //    AND 
                //    ISNULL(Region,0) = CASE 
                //    WHEN '{geographicalHierarchy}'='{GeographicalHierarchy.regionId}' THEN {geoFilterId}
                //    ELSE  ISNULL(Region,0)
                //    END";
                return $@"
                    ISNULL(Region,0) = CASE 
                    WHEN '{geographicalHierarchy}'='{GeographicalHierarchy.regionId}' THEN {geoFilterId}
                    ELSE  ISNULL(Region,0)
                    END";
            }
        }

        public static class PositionHierarchyFilters
        {
            public static string PositionFilterForPositionIdsAndLevels(List<PositionCodeLevel> pcLevels,List<long> pcIds)
            {
                string query = "(";
                var i = 0;

                foreach(var l in pcLevels)
                {
                    if (i > 0) query += "or";
                    if(l==PositionCodeLevel.Level1)  query+=$"(isnull(PositionLevel1,-1) in ({String.Join(",",pcIds)}))";
                    if (l == PositionCodeLevel.Level2) query += $"(isnull(PositionLevel2,-1) in ({String.Join(",", pcIds)}))";
                    if (l == PositionCodeLevel.Level3) query +=$"(isnull(PositionLevel3,-1) in ({String.Join(",", pcIds)}))";
                    if (l == PositionCodeLevel.Level4) query+= $"(isnull(PositionLevel4,-1) in ({String.Join(",", pcIds)}))";
                    if (l == PositionCodeLevel.Level5) query +=$"(isnull(PositionLevel5,-1) in ({String.Join(",", pcIds)}))";
                    if (l == PositionCodeLevel.Level6) query += $"(isnull(PositionLevel6,-1) in ({String.Join(",", pcIds)}))";
                    if (l == PositionCodeLevel.Level7) query += $"(isnull(PositionLevel7,-1) in ({String.Join(",", pcIds)}))";
                    if (l == PositionCodeLevel.Level8) query+= $"(isnull(PositionLevel8,-1) in ({String.Join(",", pcIds)}))";
                    i += 1;
                }


                if (pcLevels.Count == 0) query += "1=0";

                query += ")";
                return (query);
            }
        }
    }
}
