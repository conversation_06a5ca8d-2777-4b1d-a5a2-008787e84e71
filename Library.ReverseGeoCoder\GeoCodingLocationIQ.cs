﻿using Library.SlackService;
using System;
using System.Collections.Generic;
using System.Text;
using System.Linq;
using System.Runtime.Serialization;
using Library.StringHelpers.Extensions;

namespace Library.ReverseGeoCoder
{
    public class GeoCodingLocationIQ
    {
        private static string api_key = "***********************************";
        private static string api_key_master = "***********************************";
        private readonly ErrorMessenger _errorMessanger;

        public GeoCodingLocationIQ(ErrorMessenger errorMessanger)
        {
            this._errorMessanger = errorMessanger;
        }
        public GeoCodedReturnData GetDataFromLatLng(decimal lat, decimal lng, bool useMasterKey = false, bool skipInvalidPincode = false)
        {
            try
            {
                if (useMasterKey)
                {
                    api_key = api_key_master;
                }
                var url = $"http://locationiq.org/v1/reverse.php?format=json&key={api_key}&lat={lat}&lon={lng}&zoom=16";
                var data = new APIActions<LocationIQReturnModel>(_errorMessanger).Get(url, 1).Result;
                if (data != null)
                {
                    var location = new GeoCodedReturnData();
                    location.Address = data.Address;
                    location.City = data.AddressDetails.FormattedCity;
                    location.Latitude = (decimal)data.Latitude;
                    location.Longitude = (decimal)data.Longitude;
                    location.State = data.AddressDetails.State;
                    if (skipInvalidPincode)
                    {
                        try
                        {
                            location.PinCode = StringHelper.ExtractPinCode(data.AddressDetails.PinCode);
                        }
                        catch { }
                    }
                    else
                    {
                        location.PinCode = StringHelper.ExtractPinCode(data.AddressDetails.PinCode);
                    }
                    location.Locality = data.AddressDetails.FormattedLocality;
                    location.Country = data.AddressDetails.Country;
                    location.District = data.AddressDetails.FormattedDistrict;
                    return location;
                }
            }
            catch
            {
                return new GeoCodedReturnData();
            }
            return new GeoCodedReturnData();
        }
    }

    [DataContract]
    public class LocationIQReturnModel
    {
        [DataMember(Name = "display_name")]
        public string Address { set; get; }

        [DataMember(Name = "address")]
        public LocationIQAddressComponents AddressDetails { get; set; }

        [DataMember(Name = "lat")]
        public double Latitude { get; set; }

        [DataMember(Name = "lon")]
        public double Longitude { get; set; }


        [DataContract]
        public class LocationIQAddressComponents
        {
            [DataMember(Name = "state")]
            public string State { set; get; }

            [DataMember(Name = "suburb")]
            public string Locality { set; get; }

            [DataMember(Name = "postcode")]
            public string PinCode { set; get; }

            [DataMember(Name = "city")]
            public string City { set; get; }

            [DataMember(Name = "state_district")]
            public string District { set; get; }


            [DataMember(Name = "country")]
            public string Country { set; get; }

            public string FormattedCity => City ?? District;

            public string FormattedDistrict => District ?? City;

            public string FormattedLocality => Locality ?? Country;
        }
    }
}
