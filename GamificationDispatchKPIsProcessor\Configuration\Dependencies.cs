﻿using DispatchKPIGamification;
using GamificationProcessor.Core.Helpers;
using GamificationProcessor.Core.Repositories;
using GamificationProcessor.Core.Services;
using GamificationProcessor.DbStorage.DbContexts;
using GamificationProcessor.DbStorage.Repositories;
using GamificationProcessor.DBStorage.Repositories;
using Library.ConnectionStringParsor;
using Library.ResiliencyHelpers;
using Library.SlackService;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System;


namespace GamificationProcessor.Configuration
{
   public static class Dependencies
    {
     

        public static string storageConnectionString = "";
         public const string GamificationQueue = "gamification-queue";

        public static void SqlResiliencyBuilder(SqlServerDbContextOptionsBuilder o)
        {
            o.EnableRetryOnFailure();
        }

        public static void SetUp(IServiceCollection serviceProvider, IConfiguration configuration)
        {

            storageConnectionString = configuration.GetConnectionString("StorageConnectionString");
            var masterStorageConnectionString = configuration.GetConnectionString("MasterStorageConnectionString");
            serviceProvider.AddDbContext<MasterDbContext>(options =>
            options.UseSqlServer(configuration.GetConnectionString("MasterDbConnectionString"), SqlResiliencyBuilder), ServiceLifetime.Transient);

          
            serviceProvider.AddDbContext<WritableTransactionDbContext>(options =>
                    options.UseSqlServer(configuration.GetConnectionString("WritableTransactionDbConnectionString"), SqlResiliencyBuilder), ServiceLifetime.Transient);

            serviceProvider.AddDbContext<ReportDbContext>(options =>
        options.UseSqlServer(configuration.GetConnectionString("WritableReportDbConnectionString"), SqlResiliencyBuilder), ServiceLifetime.Transient);

          
            //helpers
          
            var reportApiConnection = ApiConnectionString.GetConnection(configuration.GetConnectionString("NSDataApiConnectionString"));

            var logReportProcessorEvent = configuration.GetValue<string>("AppSettings:logReportProcessorEvent");

            serviceProvider.AddSingleton(s => new AppConfigSettings
            {
                reportApiBaseUrl = reportApiConnection.BaseUrl,
                reportApiToken = reportApiConnection.AuthToken,
                logReportProcessorEvent = string.IsNullOrEmpty(logReportProcessorEvent) ? false : Convert.ToBoolean(logReportProcessorEvent),
            });
            serviceProvider.AddScoped<IGamificationRepository, GamificationRepository>();
            serviceProvider.AddScoped<ICompanySettingsRepository, CompanySettingsRepository>();
            serviceProvider.AddScoped<MTD_LMTDDatesService>();
            serviceProvider.AddScoped<ReportDataAPIsHelper>();;
            serviceProvider.AddScoped<IGamificationProcessor, Core.Services.GamificationProcessor>();
            serviceProvider.AddSingleton<DispatchKPIGamificationTrigger>();


            //Infra

            serviceProvider.AddScoped<ResilientAction>();


            serviceProvider.AddSingleton<IConfiguration>(c => configuration);
     
            serviceProvider.AddSingleton(s => new ErrorMessenger(masterStorageConnectionString, "DispatchKPIGamificationTrigger", "gamification"));

        }
    }
}
