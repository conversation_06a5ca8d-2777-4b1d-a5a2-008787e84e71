﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Library.CommonHelpers
{
    public class DistanceCalculation
    {
        public static double GetDistanceBetweenTwoLocations(decimal originLatitude = 0, decimal originLongitude = 0, decimal destinationLatitude = 0, decimal destinationLongitude = 0)
        {
            if ((originLatitude == 0 && originLongitude == 0) || (destinationLatitude == 0 && destinationLongitude == 0))
                return 0;
            var R = 6378137; // Earth’s mean radius in meter
            var dLat = rad(destinationLatitude - originLatitude);
            var dLong = rad(destinationLongitude - originLongitude);
            var a = Math.Sin(dLat / 2) * Math.Sin(dLat / 2) +
                    Math.Cos(rad(originLatitude)) * Math.Cos(rad(destinationLatitude)) *
                    Math.Sin(dLong / 2) * Math.Sin(dLong / 2);
            var c = 2 * Math.Atan2(Math.Sqrt(a), Math.Sqrt(1 - a));
            var d = (R * c) / 1000;
            return d;

        }
        private static double rad(decimal x)
        {
            return ((double)(x) * Math.PI / 180);
        }
        public static double? CalculateDistance(
           double? Lat1,
           double? Long1,
           double? Lat2,
           double? Long2)
        {
            /*
                The Haversine formula according to Dr. Math.
                http://mathforum.org/library/drmath/view/51879.html

                dlon = lon2 - lon1
                dlat = lat2 - lat1
                a = (sin(dlat/2))^2 + cos(lat1) * cos(lat2) * (sin(dlon/2))^2
                c = 2 * atan2(sqrt(a), sqrt(1-a))
                d = R * c

                Where
                    * dlon is the change in longitude
                    * dlat is the change in latitude
                    * c is the great circle distance in Radians.
                    * R is the radius of a spherical Earth.
                    * The locations of the two points in
                        spherical coordinates (longitude and
                        latitude) are lon1,lat1 and lon2, lat2.
            */
            try
            {
                if (!Lat1.HasValue
                    || !Lat2.HasValue
                    || !Long1.HasValue
                    || !Long2.HasValue)
                {
                    return default(double?);
                }


                if (Lat1 == 0
                    || Lat2 == 0
                    || Long1 == 0
                    || Long2 == 0)
                {
                    return default(double?);
                }

                double dDistance = Double.MinValue;
                double dLat1InRad = Lat1.Value * (Math.PI / 180.0);
                double dLong1InRad = Long1.Value * (Math.PI / 180.0);
                double dLat2InRad = Lat2.Value * (Math.PI / 180.0);
                double dLong2InRad = Long2.Value * (Math.PI / 180.0);

                double dLongitude = dLong2InRad - dLong1InRad;
                double dLatitude = dLat2InRad - dLat1InRad;

                // Intermediate result a.
                double a = Math.Pow(Math.Sin(dLatitude / 2.0), 2.0) +
                           Math.Cos(dLat1InRad) * Math.Cos(dLat2InRad) *
                           Math.Pow(Math.Sin(dLongitude / 2.0), 2.0);

                // Intermediate result c (great circle distance in Radians).
                double c = 2.0 * Math.Asin(Math.Sqrt(a));

                // Distance.
                // const Double kEarthRadiusMiles = 3956.0;
                const Double kEarthRadiusKms = 6376.5;
                dDistance = kEarthRadiusKms * c;

                // distance in metres

                var distanceInMetres = dDistance * 1000;

                return distanceInMetres;

            }
            catch (Exception)
            {
                return default(double?);
            }
        }
    }
}
