﻿using Library.Infrastructure.QueueService;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace Library.SMSHelpers
{
    public class SMSSender
    {
        private readonly QueueActions<SMSMessage> smsClient;
        public SMSSender(string masterStorageConnStrin)
        { 
            smsClient = new QueueActions<SMSMessage>(QueueType.SendSMS, masterStorageConnStrin);
        }

        public async Task Send(string to,string body)
        {
            var message = new SMSMessage
            {
                Message = body,
                To = to,
            };
            await Send(message);
        }

        public async Task Send(SMSMessage smsMessage)
        {

            var refId =  Guid.NewGuid().ToString();
            await smsClient.AddtoQueue(refId, smsMessage).ConfigureAwait(false);
        }
    }
}
