﻿//using System.Threading.Tasks;
//using System.Net;
//using System.Linq;
//using Libraries.CommonEnums;
//using Library.SlackService;
//using Library.Infrastructure.QueueService;

//namespace Library.ApiLogger.CosmosLogs
//{
//    public class ApiLogHandler
//    {
//        private const string ApiResponseDescriptionHeaderKey = "FA-Api-Response-Description";
//        public const string ApiResponseRequestIdHeaderKey = "FA-Api-Request-Id";
//        private ILogWriter _logWriter;
//        private readonly BugChannel _bugChannel;

//        public ApiLogHandler(ILogWriter logWriter, string deploymentType = "dev")
//        {
//            _logWriter = logWriter;
//            _bugChannel = deploymentType == "dev" ? BugChannel.Debug : (deploymentType == "manage" ? BugChannel.Manage : BugChannel.Unknown);
//        }

//        public async Task AddErrorToSlackQueue(ApiLogRequest apiLogObject)
//        {
//            try
//            {
//                var queueCreator = new QueueActions<ISlackMessage>(QueueType.SlackAutoBug);
//                var channel = _bugChannel;
//                if (channel == BugChannel.Manage)
//                {
//                    if (apiLogObject.ApiType == ApiType.ExtApi)
//                    {
//                        channel = BugChannel.ExternalApiErrors;
//                    }
//                    else if (apiLogObject.StatusCode == (int)HttpStatusCode.GatewayTimeout)
//                    {
//                        channel = BugChannel.TimeOuts;
//                    }
//                    else if (apiLogObject.StatusCode == (int)HttpStatusCode.BadRequest && apiLogObject.ApiType == ApiType.FAApi)
//                    {
//                        channel = BugChannel.Api400Errors;
//                    }
//                }
//                var appVersion = apiLogObject.RequestHeaders.Where(h => h.Key == "FA-App-Version").SelectMany(h => h.Value).ToList();
//                var extraInfo = appVersion.Count > 0 ? $"(AppVersion: {string.Join(",", appVersion)})" : "";
//                await queueCreator.AddtoQueue(apiLogObject.RequestId, new SlackExceptionMessage(channel, apiLogObject.ApiType)
//                {
//                    ErrorMessage = $"For CompanyId:{apiLogObject.CompanyId} @ {extraInfo} {apiLogObject.RequestPath}",
//                    HashedToken = string.IsNullOrWhiteSpace(apiLogObject.UserName) ? 0 : apiLogObject.UserName.GetHashCode(),
//                    RequestId = apiLogObject.RequestId,
//                    Heading = $"{apiLogObject.StatusCode}: {apiLogObject.Description}"
//                }.GetSlackMessage());
//            }
//            catch
//            {
//                //ignore
//            }
//        }
//    }
//}