﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace Libraries.CommonEnums
{
    public enum InventoryTransactionSource
    {
        [Display(Name = "excel_upload")]
        EXCEL_UPLOAD,
        [Display(Name = "good_received")]
        GOOD_RECEIVED,
        [Display(Name = "delivery")]
        DELIVERY,
        [Display(Name = "good_return")]
        GOOD_RETURN
    }

    public enum MovementType
    {
        [Display(Name = "in")]
        IN,
        [Display(Name = "out")]
        OUT,
        [Display(Name = "excel_upload")]
        EXCEL_UPLOAD
    }
}
