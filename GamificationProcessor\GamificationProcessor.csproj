﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>netcoreapp2.0</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Azure.WebJobs" Version="3.0.16" />
    <PackageReference Include="Microsoft.Azure.WebJobs.Extensions.Storage" Version="4.0.2" />
    <PackageReference Include="Microsoft.Azure.WebJobs.Logging.ApplicationInsights" Version="3.0.14" />
    <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="3.1.5" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\GamificationProcessor.Core\GamificationProcessor.Core.csproj" />
    <ProjectReference Include="..\GamificationProcessor.DBStorage\GamificationProcessor.DBStorage.csproj" />
    <ProjectReference Include="..\Library.AsyncLock\Library.AsyncLock.csproj" />
    <ProjectReference Include="..\Library.ConnectionStringParsor\Library.ConnectionStringParsor.csproj" />
    <ProjectReference Include="..\Library.ResiliencyHelpers\Library.ResiliencyHelpers.csproj" />
    <ProjectReference Include="..\Library.SlackService\Library.SlackService.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="appsettings.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Settings.job">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
