﻿<Project Sdk="Microsoft.NET.Sdk;Microsoft.NET.Sdk.Publish">
  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>netcoreapp3.1</TargetFramework>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.Azure.WebJobs" Version="3.0.16" />
    <PackageReference Include="Microsoft.Azure.WebJobs.Extensions.Storage" Version="4.0.2" />
    <PackageReference Include="Microsoft.Azure.WebJobs.Logging.ApplicationInsights" Version="3.0.14" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="2.0.2" />
    <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="3.1.5" />
  </ItemGroup>
  <ItemGroup>
    <!--TODO: Combine All Core and DbStorage Projects-->
    <ProjectReference Include="..\GamificationAutomatedProcessor.Core\GamificationAutomatedProcessor.Core.csproj" />
    <ProjectReference Include="..\GamificationAutomatedProcessor.DbStorage\GamificationAutomatedProcessor.DbStorage.csproj" />
    <ProjectReference Include="..\Library.AsyncLock\Library.AsyncLock.csproj" />
    <ProjectReference Include="..\Library.ConnectionStringParsor\Library.ConnectionStringParsor.csproj" />
    <ProjectReference Include="..\Library.ResiliencyHelpers\Library.ResiliencyHelpers.csproj" />
    <ProjectReference Include="..\Library.SlackService\Library.SlackService.csproj" />
  </ItemGroup>
  <ItemGroup>
    <None Update="appsettings.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="settings.job">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>
</Project>