﻿using FileGenerator.Attributes;
using FileGenerator.Interfaces;
using OfficeOpenXml;
using OfficeOpenXml.Style;
using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using iTextSharp.text;
using iTextSharp.text.pdf;
using FileGenerator.DataTableHelpers;
using System.Reflection;
using iTextSharp.text.pdf.draw;
using System.Collections;

namespace FileGenerator
{
    public class PDFGenerator
    {
        private readonly CompanyNomenclatureSpecifier nomenclatureSpecifier;
        private readonly bool _useNomenclature;

        public PDFGenerator(CompanyNomenclatureSpecifier nomenclatureSpecifier = null)
        {
            this.nomenclatureSpecifier = nomenclatureSpecifier;
            _useNomenclature = nomenclatureSpecifier != null;
        }
        public void MakePDF<T1>(long companyId, Stream output, string sheetName, IEnumerable<T1> data, IEnumerable<T1> dataForSecTable, int? noOfColumnsInInfoTable, Paragraph singnature = null, string GroupColumn = null, string ValueColumn = null, List<string> columnsToDelete = null, PdfPTable TotalTable = null)
        {        
            if (data == null || data.Count()==0)
            {
                    var document = new Document(PageSize.A2.Rotate(), 50, 50, 25, 25);
                    var writer = PdfWriter.GetInstance(document, output);
                    writer.CloseStream = false;
                    document.Open();
                    var message = new Paragraph("Day not started by User Or Preferences not set")
                    {
                        Alignment = Element.ALIGN_CENTER
                    };
                    document.Add(message);
                    document.Close();
            }
            else
            {
                PivotCreater pivotCreater = new PivotCreater();

                var columns = data.FirstOrDefault()?.GetType().GetProperties().Where(p => Attribute.GetCustomAttribute(p, typeof(TableFieldAttribute)) != null);
                var Table1 = CreateInfoTable(companyId, data, noOfColumnsInInfoTable, columnsToDelete);
                var document = new Document(PageSize.A2.Rotate(), 50, 50, 25, 25);
                var writer = PdfWriter.GetInstance(document, output);
                writer.CloseStream = false;
                document.Open();
                document.Add(AddCompanyLogo(companyId));
                //document.Add(new Paragraph(sheetName));
                //document.Add(AddFALogo());
                document.Add(Table1);
                if (dataForSecTable.Count() > 0)
                {
                    ListToDataTableConverter ConvertToDataTable = new ListToDataTableConverter();
                    var dt = ConvertToDataTable.GetDataTableFromList(dataForSecTable, sheetName);
                    if (GroupColumn != null)
                    {
                        dt = RemoveColumnsAndChangeHeader(companyId, columns, dt, columnsToDelete);
                        var pivotDt = pivotCreater.Pivot(dt, dt.Columns[GroupColumn], dt.Columns[ValueColumn]);
                        var pdfTableForPivto = CreateDataTable(pivotDt);
                        document.Add(pdfTableForPivto);
                    }
                    else
                    {
                        dt = RemoveColumns(companyId, columns, dt, columnsToDelete);
                        var pdfTableFlat = CreateDataTable(dt, columns, columnsToDelete);
                        document.Add(pdfTableFlat);
                    }
                    if (TotalTable != null)
                    {
                        document.Add(TotalTable);
                    }
                }
                else
                {
                    var nosale = new Paragraph("User have not booked any order")
                    {
                        Alignment = Element.ALIGN_CENTER,


                    };
                    document.Add(nosale);
                }
                if (singnature != null)
                {
                    singnature.Alignment = Element.ALIGN_LEFT;
                    document.Add(singnature);
                }
                var asmPara = new Paragraph()
                {
                    Alignment = Element.ALIGN_CENTER
                };
                document.Add(asmPara);
                document.Close();
                //output.Close();
            }
            
        }
        public void MakeMultipleDataPDF<T1,T2>(long companyId, Stream output, string sheetName, IEnumerable<T1> data, IEnumerable<T2> dataForSecTable, int? noOfColumnsInInfoTable, Paragraph singnature = null, string GroupColumn = null, string ValueColumn = null, List<string> columnsToDelete = null, List<PdfPTable> TotalTable = null)
        {
            if (data == null || data.Count() == 0)
            {
                var document = new Document(PageSize.A2, 50, 50, 25, 25);
                var writer = PdfWriter.GetInstance(document, output);
                writer.CloseStream = false;
                document.Open();
                var message = new Paragraph("Day not started by User Or Preferences not set")
                {
                    Alignment = Element.ALIGN_CENTER
                };
                document.Add(message);
                document.Close();
            }
            else
            {
                PivotCreater pivotCreater = new PivotCreater();
                var document = new Document(PageSize.A2, 50, 50, 25, 25);
                var writer = PdfWriter.GetInstance(document, output);
                writer.CloseStream = false;
                document.Open();
                document.Add(AddCompanyLogo(companyId));
                foreach (var d in data)
                {
                    //var columns = d.GetType().GetProperties().Where(p => Attribute.GetCustomAttribute(p, typeof(TableFieldAttribute)) != null);
                    //var columnsforsec = dataForSecTable.FirstOrDefault()?.GetType().GetProperties().Where(p => Attribute.GetCustomAttribute(p, typeof(TableFieldAttribute)) != null);
                    //var Table1 = CreateInfoTable(companyId, data, noOfColumnsInInfoTable, columnsToDelete);


                    ListToDataTableConverter ConvertToDataTable = new ListToDataTableConverter();
                    //var dt = ConvertToDataTable.GetDataTableFromList(data, sheetName);
                    //  dt = RemoveColumns(companyId, columns, dt, columnsToDelete);

                    //var dt = ConvertToDataTable.GetDataTableFromList(dataForSecTable, sheetName);
                    //dt = RemoveColumns(companyId, columnsforsec, dt, columnsToDelete);
                    var pdfTableFlat = CreateFlatDataTable(d,companyId, sheetName, dataForSecTable, noOfColumnsInInfoTable, columnsToDelete);
                            //document.Add(pdfTableFlat);
                            foreach(var pdf in pdfTableFlat)
                        {
                            document.Add(pdf);
                        }
                    document.Add(new Paragraph(" "));
                    LineSeparator line2 = new LineSeparator(1f, 100f, iTextSharp.text.BaseColor.Black, Element.ALIGN_LEFT, 1);
                    document.Add(line2);
                }
                document.Add(new Paragraph(" "));
                if (TotalTable != null && TotalTable.Count()>0)
                {
                    foreach (var pdf in TotalTable)
                    {
                        document.Add(pdf);
                    }
                }
                if (singnature != null)
                {
                    singnature.Alignment = Element.ALIGN_LEFT;
                    document.Add(singnature);
                }
                var asmPara = new Paragraph()
                {
                    Alignment = Element.ALIGN_CENTER
                };
                document.Add(asmPara);
                document.Close();
                //output.Close();
            }

        }

        private IElement AddCompanyLogo(long companyId)
        {
            string companyLogoURL = "https://manage.fieldassist.in/FieldAssistPOC/Company/GetCompanyLogoViaId?companyId=" + companyId.ToString();

            iTextSharp.text.Image jpg = iTextSharp.text.Image.GetInstance(new Uri(companyLogoURL));
            //Resize image depend upon your need
           
            jpg.ScaleToFit(140f, 120f);

            //Give space before image

            //jpg.SpacingBefore = 10f;

            //Give some space after the image

            //jpg.SpacingAfter = 1f;

            jpg.Alignment = Element.ALIGN_LEFT;


            return jpg;
        }

        public iTextSharp.text.Image AddFALogo()
        {

            string faLogoURL = "https://i0.wp.com/www.fieldassist.in/wp-content/uploads/2016/11/logo_1-1.png";

            iTextSharp.text.Image jpg = iTextSharp.text.Image.GetInstance(faLogoURL);
            //Resize image depend upon your need

            jpg.ScaleToFit(140f, 120f);

            //Give space before image

            //jpg.SpacingBefore = 10f;

            //Give some space after the image

            jpg.SpacingAfter = 1f;

            jpg.Alignment = Element.ALIGN_RIGHT;

            return jpg;
        }
        public List<PdfPTable> CreateFlatDataTable<T,T1>(T data,long companyId, string sheetName,IEnumerable<T1> dataforsec , int? noOfColumnsInInfoTable, List<string> columnsToDelete = null)
        {
            List<PdfPTable> toret = new List<PdfPTable>();
            var columns = data.GetType().GetProperties().Where(p => Attribute.GetCustomAttribute(p, typeof(TableFieldAttribute)) != null);

            var item = data;
                foreach (var colProp in columns)
                {
                    if (columnsToDelete == null || !columnsToDelete.Contains(colProp.Name))
                    {
                        var Tattr = Attribute.GetCustomAttribute(colProp, typeof(TableFieldAttribute));
                        if (Tattr is TableFieldAttribute excelAttrOfT)
                        {
                            var Tcolumnname = excelAttrOfT.ColumnName;
                            var dataValue = colProp.GetValue(item) ?? "";
                            if (excelAttrOfT.PDFColCategory == PDFColCategory.Info && (dataValue.ToString() != "" || excelAttrOfT.ColumnRequirement == Requirement.Required) )
                            {
                            PdfPTable table = new PdfPTable(noOfColumnsInInfoTable ?? 12)
                            {
                                WidthPercentage = 30,
                                HorizontalAlignment= Element.ALIGN_LEFT,
                            };
                            if (_useNomenclature & excelAttrOfT.NomenclatureRequirement)
                                    {

                                        var listforheader = excelAttrOfT.ColumnName.Split(' ').ToList();
                                        var nomenclaturename = nomenclatureSpecifier.GetHeaderName(listforheader[0]);
                                        var updatelist = listforheader.Remove(listforheader[0]);
                                        var striingotherthennomenclature = String.Join(" ", listforheader);
                                        Tcolumnname = nomenclaturename + " " + striingotherthennomenclature;


                                        PdfPCell headerCell = new PdfPCell(new Phrase(excelAttrOfT.ColumnName))
                                        {
                                            //BackgroundColor = new iTextSharp.text.BaseColor(211, 211, 211),
                                            HorizontalAlignment = Element.ALIGN_LEFT,
                                            VerticalAlignment = Element.ALIGN_CENTER,
                                            Padding = 3,
                                            BorderWidth = 0,
                                            

                                        };
                                        table.AddCell(headerCell);

                                        PdfPCell dataCell = new PdfPCell(new Phrase(dataValue.ToString()))
                                        {
                                            //BackgroundColor = new iTextSharp.text.BaseColor(255, 255, 255),
                                            HorizontalAlignment = Element.ALIGN_LEFT,
                                            VerticalAlignment = Element.ALIGN_CENTER,
                                            Padding = 3,
                                            BorderWidth = 0
                                        };
                                        table.AddCell(dataCell);
                                    }

                                else
                                {


                                    PdfPCell headerCell = new PdfPCell(new Phrase(excelAttrOfT.ColumnName))
                                    {
                                        //BackgroundColor = new iTextSharp.text.BaseColor(211, 211, 211),
                                        HorizontalAlignment = Element.ALIGN_LEFT,
                                        VerticalAlignment = Element.ALIGN_CENTER,
                                        Padding = 3,
                                        BorderWidth = 0
                                    };
                                    table.AddCell(headerCell);

                                    PdfPCell dataCell = new PdfPCell(new Phrase(dataValue.ToString()))
                                    {
                                        //BackgroundColor = new iTextSharp.text.BaseColor(255, 255, 255),
                                        HorizontalAlignment = Element.ALIGN_LEFT,
                                        VerticalAlignment = Element.ALIGN_CENTER,
                                        Padding = 3,
                                        BorderWidth = 0
                                    };
                                    table.AddCell(dataCell);

                                }
                                toret.Add(table);
                            }
                            else if (excelAttrOfT.PDFColCategory == PDFColCategory.Table)
                            {
                            ListToDataTableConverter ConvertToDataTable = new ListToDataTableConverter();
                                if (dataValue.ToString() != "")
                                {
                                    var result = (IEnumerable<T1>)dataValue;
                                    var columnsforsec = result.FirstOrDefault()?.GetType().GetProperties().Where(p => Attribute.GetCustomAttribute(p, typeof(TableFieldAttribute)) != null);
                                    var dt = ConvertToDataTable.GetDataTableFromList(result, sheetName);
                                    dt = RemoveColumns(companyId, columnsforsec, dt, columnsToDelete);
                                    var pdfTableFlat = CreateDataTable(dt, columnsforsec, columnsToDelete);
                                    toret.Add(pdfTableFlat);
                            }
                            }
                        }
                    }

                
                }
            return toret;

        }

        public PdfPTable CreateInfoTable<T>(long companyId, IEnumerable<T> data, int? noOfColumnsInInfoTable, List<string> columnsToDelete = null)
        {
            var columns = data.FirstOrDefault()?.GetType().GetProperties().Where(p => Attribute.GetCustomAttribute(p, typeof(TableFieldAttribute)) != null);
            var firstrowofdata = data.Take(1);
            PdfPTable table = new PdfPTable(noOfColumnsInInfoTable ?? 12)
            {
                SpacingBefore = 10f,
                SpacingAfter = 12.5f
            };
            foreach (var item in firstrowofdata)
            {
                foreach (var colProp in columns)
                {
                    if (columnsToDelete == null || !columnsToDelete.Contains(colProp.Name))
                    {
                        var Tattr = Attribute.GetCustomAttribute(colProp, typeof(TableFieldAttribute));
                        if (Tattr is TableFieldAttribute excelAttrOfT)
                        {
                            var Tcolumnname = excelAttrOfT.ColumnName;
                            var dataValue = colProp.GetValue(item) ?? "";
                            if (excelAttrOfT.PDFColCategory == PDFColCategory.Info)
                            {
                                if (_useNomenclature & excelAttrOfT.NomenclatureRequirement)
                                {

                                    var listforheader = excelAttrOfT.ColumnName.Split(' ').ToList();
                                    var nomenclaturename = nomenclatureSpecifier.GetHeaderName(listforheader[0]);
                                    var updatelist = listforheader.Remove(listforheader[0]);
                                    var striingotherthennomenclature = String.Join(" ", listforheader);
                                    Tcolumnname = nomenclaturename + " " + striingotherthennomenclature;


                                    PdfPCell headerCell = new PdfPCell(new Phrase(excelAttrOfT.ColumnName))
                                    {
                                        BackgroundColor = new iTextSharp.text.BaseColor(211, 211, 211),
                                        HorizontalAlignment = Element.ALIGN_CENTER,
                                        VerticalAlignment = Element.ALIGN_CENTER,
                                        Padding = 3,

                                    };

                                    table.AddCell(headerCell);

                                    PdfPCell dataCell = new PdfPCell(new Phrase(dataValue.ToString()))
                                    {
                                        BackgroundColor = new iTextSharp.text.BaseColor(255, 255, 255),
                                        HorizontalAlignment = Element.ALIGN_CENTER,
                                        VerticalAlignment = Element.ALIGN_CENTER,
                                        Padding = 3,
                                    };
                                    table.AddCell(dataCell);
                                }

                                else
                                {


                                    PdfPCell headerCell = new PdfPCell(new Phrase(excelAttrOfT.ColumnName))
                                    {
                                        BackgroundColor = new iTextSharp.text.BaseColor(211, 211, 211),
                                        HorizontalAlignment = Element.ALIGN_CENTER,
                                        VerticalAlignment = Element.ALIGN_CENTER,
                                        Padding = 3,
                                    };
                                    table.AddCell(headerCell);

                                    PdfPCell dataCell = new PdfPCell(new Phrase(dataValue.ToString()))
                                    {
                                        BackgroundColor = new iTextSharp.text.BaseColor(255, 255, 255),
                                        HorizontalAlignment = Element.ALIGN_CENTER,
                                        VerticalAlignment = Element.ALIGN_CENTER,
                                        Padding = 3,
                                    };
                                    table.AddCell(dataCell);

                                }
                            }
                        }
                    }


                }
            }

            return table;

        }

        public PdfPTable CreateDataTable(DataTable data)
        {


            PdfPTable table = new PdfPTable(data.Columns.Count);
            table.DefaultCell.HorizontalAlignment = Element.ALIGN_CENTER;
            table.DefaultCell.VerticalAlignment = Element.ALIGN_CENTER;
            table.DefaultCell.Padding = 3;
            table.SpacingBefore = 10f;
            table.SpacingAfter = 12.5f;
            table.WidthPercentage = 100f;
            foreach (DataColumn col in data.Columns)
            {
                table.AddCell(col.ColumnName);
            }
            for (var i = 0; i < data.Rows.Count; i++)
            {
                for (var j = 0; j < data.Columns.Count; j++)
                {
                    table.AddCell(data.Rows[i].ItemArray[j].ToString());
                }

            }

            return table;

        }
        public PdfPTable CreateDataTable(DataTable data, IEnumerable<PropertyInfo> columns, List<string> columnsToDelete = null)
        {
            PdfPTable table = new PdfPTable(data.Columns.Count);
            table.DefaultCell.HorizontalAlignment = Element.ALIGN_CENTER;
            table.DefaultCell.VerticalAlignment = Element.ALIGN_CENTER;
            table.DefaultCell.Padding = 3;
            table.SpacingBefore = 10f;
            table.SpacingAfter = 12.5f;
            table.WidthPercentage = 100f;
            foreach (DataColumn col in data.Columns)
            {
                if (!columnsToDelete.Contains(col.ColumnName))
                {
                    var colProp = columns.ToList().Where(d => d.Name == col.ColumnName).Select(d => d).FirstOrDefault();
                    var Tattr = Attribute.GetCustomAttribute(colProp, typeof(TableFieldAttribute));
                    if (Tattr is TableFieldAttribute excelAttrOfT)
                    {
                        if (excelAttrOfT.PDFColCategory == PDFColCategory.Table)
                        {
                            table.AddCell(excelAttrOfT.ColumnName);
                        }
                    }
                }
            }
            for (var i = 0; i < data.Rows.Count; i++)
            {
                for (var j = 0; j < data.Columns.Count; j++)
                {
                    if (!columnsToDelete.Contains(data.Columns[j].ColumnName))
                    {
                        var colProp = columns.ToList().Where(d => d.Name == data.Columns[j].ColumnName).Select(d => d).FirstOrDefault();
                        var Tattr = Attribute.GetCustomAttribute(colProp, typeof(TableFieldAttribute));
                        if (Tattr is TableFieldAttribute excelAttrOfT)
                        {
                            if (excelAttrOfT.PDFColCategory == PDFColCategory.Table)
                            {
                                var Tcolumnname = excelAttrOfT.ColumnName;
                                if (excelAttrOfT.ColumnDataType == DataType.Date)
                                {
                                    var dataValue = ((DateTimeOffset)data.Rows[i].ItemArray[j]).ToString("dd/MM/yyyy");
                                    table.AddCell(dataValue);
                                }
                                else if (excelAttrOfT.ColumnDataType == DataType.Time)
                                {
                                    var dataValue = data.Rows[i].ItemArray[j] != DBNull.Value ? ((DateTimeOffset)data.Rows[i].ItemArray[j]).ToString(@"HH: mm"):"";
                                    table.AddCell(dataValue);
                                }
                                else
                                {
                                    var dataValue = data.Rows[i].ItemArray[j].ToString();
                                    table.AddCell(dataValue);
                                }

                            }
                        }
                    }
                }

            }

            return table;
        }
        public PdfPTable CreateDataTableWithoutHeader(DataTable data, IEnumerable<PropertyInfo> columns, List<string> columnsToDelete = null)
        {
            PdfPTable table = new PdfPTable(data.Columns.Count);
            table.DefaultCell.HorizontalAlignment = Element.ALIGN_CENTER;
            table.DefaultCell.VerticalAlignment = Element.ALIGN_CENTER;
            table.DefaultCell.Padding = 3;
            table.SpacingBefore = 10f;
            table.SpacingAfter = 12.5f;
            table.WidthPercentage = 100f;
            for (var i = 0; i < data.Rows.Count; i++)
            {
                for (var j = 0; j < data.Columns.Count; j++)
                {
                    if (!columnsToDelete.Contains(data.Columns[j].ColumnName))
                    {
                        var colProp = columns.ToList().Where(d => d.Name == data.Columns[j].ColumnName).Select(d => d).FirstOrDefault();
                        var Tattr = Attribute.GetCustomAttribute(colProp, typeof(TableFieldAttribute));
                        if (Tattr is TableFieldAttribute excelAttrOfT)
                        {
                            if (excelAttrOfT.PDFColCategory == PDFColCategory.Table)
                            {
                                var Tcolumnname = excelAttrOfT.ColumnName;
                                if (excelAttrOfT.ColumnDataType == DataType.Date)
                                {
                                    var dataValue = ((DateTimeOffset)data.Rows[i].ItemArray[j]).ToString("dd/MM/yyyy");
                                    table.AddCell(dataValue);
                                }
                                else if (excelAttrOfT.ColumnDataType == DataType.Time)
                                {
                                    var dataValue = data.Rows[i].ItemArray[j] != DBNull.Value ? ((DateTimeOffset)data.Rows[i].ItemArray[j]).ToString(@"HH: mm") : "";
                                    table.AddCell(dataValue);
                                }
                                else
                                {
                                    var dataValue = data.Rows[i].ItemArray[j].ToString();
                                    table.AddCell(dataValue);
                                }

                            }
                        }
                    }
                }

            }

            return table;
        }
        public DataTable RemoveColumnsAndChangeHeader(long companyId, IEnumerable<PropertyInfo> columns, DataTable dt, List<string> columnsToDelete)
        {
            var ordinal = 0;
            foreach (var colProp in columns)
            {
                if (columnsToDelete != null && columnsToDelete.Contains(colProp.Name))
                {
                    dt.Columns.Remove(colProp.Name);
                }
                else
                {
                    var Tattr = Attribute.GetCustomAttribute(colProp, typeof(TableFieldAttribute));
                    if (Tattr is TableFieldAttribute excelAttrOfT)
                    {
                        var Tcolumnname = excelAttrOfT.ColumnName;

                        if (excelAttrOfT.PDFColCategory != PDFColCategory.Table)
                        {
                            dt.Columns.Remove(colProp.Name);
                        }
                        else
                        {
                            if (_useNomenclature && excelAttrOfT.NomenclatureRequirement == true)
                            {
                                var listforheader = excelAttrOfT.ColumnName.Split(' ').ToList();
                                var nomenclaturename = nomenclatureSpecifier.GetHeaderName(listforheader[0]);
                                var updatelist = listforheader.Remove(listforheader[0]);
                                var striingotherthennomenclature = String.Join(" ", listforheader);
                                Tcolumnname = nomenclaturename + " " + striingotherthennomenclature;
                                dt.Columns[colProp.Name].SetOrdinal(ordinal);
                                dt.Columns[colProp.Name].ColumnName = Tcolumnname;
                            }
                            else
                            {
                                dt.Columns[colProp.Name].SetOrdinal(ordinal);
                                dt.Columns[colProp.Name].ColumnName = excelAttrOfT.ColumnName;
                            }

                            ordinal++;
                        }
                    }
                }
            }
            return dt;
        }
        public DataTable RemoveColumns(long companyId, IEnumerable<PropertyInfo> columns, DataTable dt, List<string> columnsToDelete)
        {
            var ordinal = 0;
            foreach (var colProp in columns)
            {
                if (columnsToDelete != null && columnsToDelete.Contains(colProp.Name))
                {
                    dt.Columns.Remove(colProp.Name);
                }
                else
                {
                    var Tattr = Attribute.GetCustomAttribute(colProp, typeof(TableFieldAttribute));
                    if (Tattr is TableFieldAttribute excelAttrOfT)
                    {
                        var Tcolumnname = excelAttrOfT.ColumnName;

                        if (excelAttrOfT.PDFColCategory != PDFColCategory.Table)
                        {
                            dt.Columns.Remove(colProp.Name);
                        }
                        else
                        {
                            dt.Columns[colProp.Name].SetOrdinal(ordinal++);
                        }
                    }
                }
            }
            return dt;
        }

        public PdfPTable CreateTotalTable<T>(long companyId, T data, List<string> columnsToDelete)
        {
            if (data == null)
                return null;
            var columns = data?.GetType().GetProperties().Where(p => Attribute.GetCustomAttribute(p, typeof(TableFieldAttribute)) != null);
            ListToDataTableConverter ConvertToDataTable = new ListToDataTableConverter();
            var list = new List<T>();
            list.Add(data);
            var dt = ConvertToDataTable.GetDataTableFromList(list, "Aggregates");
            dt = RemoveColumns(companyId, columns, dt, columnsToDelete);
            var totalTable = CreateDataTableWithoutHeader(dt, columns, columnsToDelete);
            return totalTable;
        }
        public List<PdfPTable> CreateFlatTotalTable<T>(long companyId, T data, List<string> columnsToDelete)
        {
            if (data == null)
                return null;
            var columns = data?.GetType().GetProperties().Where(p => Attribute.GetCustomAttribute(p, typeof(TableFieldAttribute)) != null);
            ListToDataTableConverter ConvertToDataTable = new ListToDataTableConverter();
            var list = new List<T>();
            list.Add(data);
            var dt = ConvertToDataTable.GetDataTableFromList(list, "Aggregates");
            dt = RemoveColumns(companyId, columns, dt, columnsToDelete);
            var totalTable = CreateFlatDataTable(data, companyId, "", new List<string>(), 2, columnsToDelete);
            return totalTable;
        }
    }
}
