﻿using Library.CommonHelpers;
using Library.SlackService;
using Library.StringHelpers.Extensions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Library.ReverseGeoCoder
{
    public class GeoCodingV2 : IGeoCoding
    {
        public enum Source
        {
            GoogleApi = 0,
            GoogleApi_FO1 = 1,
            GoogleApi_FO2 = 2,
            GoogleApi_FO3 = 3,
            GoogleApi_FO4 = 4,
            GoogleApi_FO5 = 5,
            GoogleApi_FO6 = 6,
            GoogleApi_FO7 = 7,
        }
        private readonly ErrorMessenger _errorMessanger;
        private readonly Source source;
        public GeoCodingV2(ErrorMessenger errorMessanger, Source source = Source.GoogleApi)
        {
            this._errorMessanger = errorMessanger;
            this.source = source;
        }
        public GeoCodedReturnData GetDataFromLatLongFromGoogle(decimal lat , decimal lng)
        {
            if (!CheckForZeroLatLng(lat, lng))
            {
                return new GeoCodedReturnData() { Latitude = lat, Longitude = lng };
            }
            var geocoderGoogle = new GeoCodingGoogle(_errorMessanger, source);
            var geoCoderLocationIQ = new GeoCodingLocationIQ(_errorMessanger);
            var data = geoCoderLocationIQ.GetDataFromLatLng(lat, lng, true);
            if (!IsGeoDataValid(data))
            {
                data = geocoderGoogle.GetDataFromLatLng(lat, lng);
            }
            if (string.IsNullOrEmpty(data.Address) || string.IsNullOrWhiteSpace(data.Address))
            {
                geocoderGoogle = new GeoCodingGoogle(_errorMessanger, Source.GoogleApi_FO1);
                data = geocoderGoogle.GetDataFromLatLng(lat, lng);
            }
            if (string.IsNullOrEmpty(data.Address) || string.IsNullOrWhiteSpace(data.Address))
            {
                geocoderGoogle = new GeoCodingGoogle(_errorMessanger, Source.GoogleApi_FO2);
                data = geocoderGoogle.GetDataFromLatLng(lat, lng);
            }
            if (string.IsNullOrEmpty(data.Address) || string.IsNullOrWhiteSpace(data.Address))
            {
                geocoderGoogle = new GeoCodingGoogle(_errorMessanger, Source.GoogleApi_FO3);
                data = geocoderGoogle.GetDataFromLatLng(lat, lng);
            }
            if (string.IsNullOrEmpty(data.Address) || string.IsNullOrWhiteSpace(data.Address))
            {
                geocoderGoogle = new GeoCodingGoogle(_errorMessanger, Source.GoogleApi_FO4);
                data = geocoderGoogle.GetDataFromLatLng(lat, lng);
            }
            if (string.IsNullOrEmpty(data.Address) || string.IsNullOrWhiteSpace(data.Address))
            {
                geocoderGoogle = new GeoCodingGoogle(_errorMessanger, Source.GoogleApi_FO5);
                data = geocoderGoogle.GetDataFromLatLng(lat, lng);
            }
            if (string.IsNullOrEmpty(data.Address) || string.IsNullOrWhiteSpace(data.Address))
            {
                geocoderGoogle = new GeoCodingGoogle(_errorMessanger, Source.GoogleApi_FO6);
                data = geocoderGoogle.GetDataFromLatLng(lat, lng);
            }
            if (string.IsNullOrEmpty(data.Address) || string.IsNullOrWhiteSpace(data.Address))
            {
                geocoderGoogle = new GeoCodingGoogle(_errorMessanger, Source.GoogleApi_FO7);
                data = geocoderGoogle.GetDataFromLatLng(lat, lng);
            }
            data.Locality.TrimToLength(200);//Database Limits it to 200
            return data;
        }
        public GeoCodedReturnData GetDataFromLatLngFromMapMyIndia(decimal lat,decimal lng)
        {
            var geocoderMapMyIndia = new GeoCodingMapMyIndia(_errorMessanger);
            var data = geocoderMapMyIndia.GetDataFromLatLng(lat, lng);
            data.Locality.TrimToLength(200);//Database Limits it to 200
            return data;
        }

        public GeoCodedReturnData GetDataFromLatLngFromLocationIQ(decimal lat, decimal lng)
        {
            if (!CheckForZeroLatLng(lat, lng))
            {
                return new GeoCodedReturnData() { Latitude = lat, Longitude = lng };
            }
            var geocoderLocationIQ = new GeoCodingLocationIQ(_errorMessanger);
            var data = geocoderLocationIQ.GetDataFromLatLng(lat, lng, true, true);
            data.Locality.TrimToLength(200);//Database Limits it to 200
            return data;
        }
        //Date: Dec 31,2020 ; Asana:https://app.asana.com/0/282003586492747/1198702698567327/f ; Change: Added a new method to get reverse geo coded data using a specific Google API
        public GeoCodedReturnData GetDataFromLatLongFromGoogleAPI(decimal lat, decimal lng)
        {
            if (!CheckForZeroLatLng(lat, lng))
            {
                return new GeoCodedReturnData() { Latitude = lat, Longitude = lng };
            }
            var geocoderGoogle = new GeoCodingGoogle(_errorMessanger, source);
            var data = geocoderGoogle.GetDataFromLatLng(lat, lng);
            return data;
        }
        private bool IsGeoDataValid(GeoCodedReturnData data)
        {
            if (data == null)
            {
                return false;
            }
            if (string.IsNullOrWhiteSpace(data.Address))
            {
                return false;
            }
            if (string.IsNullOrWhiteSpace(data.State))
            {
                return false;
            }
            if (string.IsNullOrWhiteSpace(data.City))
            {
                return false;
            }
            if (string.IsNullOrWhiteSpace(data.PinCode))
            {
                return false;
            }
            return true;
        }
        //public GeoCodedReturnData GetDataFromLatLng(decimal lat, decimal lng)
        //{
        //    if (!CheckForZeroLatLng(lat, lng))
        //    {
        //        return new GeoCodedReturnData() { Latitude = lat, Longitude = lng };
        //    }
        //    var geocoderGoogle = new GeoCodingGoogle(_errorMessanger, source);
        //    var data = geocoderGoogle.GetDataFromLatLng(lat, lng);
        //    //var isDataValid = CheckForValidGeoData(data);
        //    if (!isDataValid)
        //    {
        //        var geocoderMapMyIndia = new GeoCodingMapMyIndia(_errorMessanger);
        //        data = geocoderMapMyIndia.GetDataFromLatLng(lat, lng);
        //        //isDataValid = CheckForValidGeoData(data);
        //        if (!isDataValid)
        //        {
        //            data = geocoderGoogle.GetDataFromLatLng(lat, lng);
        //        }
        //    }
        //        data.Locality.TrimToLength(200);//Database Limits it to 200
        //    return data;
        //}
        private static bool CheckForZeroLatLng(decimal lat, decimal lng)
        {
            if (lat == decimal.Zero && lng == decimal.Zero)
            {
                return false;
            }
            else
            {
                return true;
            }
        }
        //public bool CheckForValidGeoData(GeoCodedReturnData data)
        //{
        //    if (data.Country.NormalizeCaps() != "GHANA" && data.Country.NormalizeCaps() != "NIGERIA")
        //    {
        //        if (!(!String.IsNullOrWhiteSpace(data.PinCode) && data.PinCode.Length >= 5 && data.PinCode.Length <= 6 && data.PinCode.All(char.IsNumber) && !string.IsNullOrWhiteSpace(data.City)))
        //            return false;
        //    }

        //    if (!string.IsNullOrEmpty(data.City) && !string.IsNullOrEmpty(data.State))
        //    {
        //        if (!(StringHelper.Is_StringEnglish(data.City) && StringHelper.Is_StringEnglish(data.State)))
        //            return false;
        //    }
        //    return true;
        //    //return string.IsNullOrWhiteSpace(Pincode) ?
  

        //}
    }
}
