﻿using System.ComponentModel.DataAnnotations.Schema;
using Libraries.CommonEnums;

namespace GamificationProcessor.DbStorage.MasterDBModels
{
    [Table("ClientEmployees")]
    public class Employee
    {
        public long Id { get; set; }
        [Column("Deleted")]
        public bool IsDeactive { get; set; }
        [Column("Company")]
        public long CompanyId { get; set; }
        public bool IsTrainingUser { get; set; }
        public PortalUserRole UserRole { get; set; }
        public long? ParentId { get; set; }
        public long? OldTableId { get; set; }
        public Employee Parent { get; set; }
        public Region Region { get; set; }
        public long? RegionId { get; set; }
        public bool IsFieldAppuser { get; set; }
        public EmployeeRank Rank { get; set; }
        public UserType UserType { get; set; }
        public long? KRATagId { get; set; }
    }

    public class Region
    {
        public long Id { get; set; }
        public long ZoneId { get; set; }
        public bool IsDeactive { get; set; }
        public Zone Zone { get; set; }
    }

    [Table("FACompanyZones")]
    public class Zone
    {
        public long Id { get; set; }
        public string Name { get; set; }
        public bool IsDeactive { get; set; }

        public long? ParentId { set; get; }
        public Geography Parent { get; set; }
    }

    public class Geography
    {
        public long Id { get; set; }
        public bool IsDeactive { get; set; }
        public long? ParentId { get; set; }
        public Geography Parent { get; set; }
    }
}
