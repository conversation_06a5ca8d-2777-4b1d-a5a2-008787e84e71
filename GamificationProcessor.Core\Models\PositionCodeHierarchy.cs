﻿using Libraries.CommonEnums;
using System;
using System.Collections.Generic;
using System.Text;

namespace GamificationProcessor.Core.Models
{
    public class PositionCodeHierarchy
    {
        public PositionCodeLevel PositionCodeLevel { get; set; }
        public long? Level1Id { get; set; }
        public long? Level2Id { get; set; }
        public long? Level3Id { get; set; }
        public long? Level4Id { get; set; }
        public long? Level5Id { get; set; }
        public long? Level6Id { get; set; }
        public long? Level7Id { get; set; }
        public long? Level8Id { get; set; }

        public PositionCodeHierarchy GetUpdatedPositionCodeLevels()
        {
            switch (PositionCodeLevel)
            {
                case PositionCodeLevel.Level8:
                    Level8Id = Level1Id;
                    Level7Id = null;
                    Level6Id = null;
                    Level5Id = null;
                    Level4Id = null;
                    Level3Id = null;
                    Level2Id = null;
                    Level1Id = null;
                    break;
                case PositionCodeLevel.Level7:
                    Level8Id = Level2Id;
                    Level7Id = Level1Id;
                    Level6Id = null;
                    Level5Id = null;
                    Level4Id = null;
                    Level3Id = null;
                    Level2Id = null;
                    Level1Id = null;
                    break;
                case PositionCodeLevel.Level6:
                    Level8Id = Level3Id;
                    Level7Id = Level2Id;
                    Level6Id = Level1Id;
                    Level5Id = null;
                    Level4Id = null;
                    Level3Id = null;
                    Level2Id = null;
                    Level1Id = null;
                    break;
                case PositionCodeLevel.Level5:
                    Level8Id = Level4Id;
                    Level7Id = Level3Id;
                    Level6Id = Level2Id;
                    Level5Id = Level1Id;
                    Level4Id = null;
                    Level3Id = null;
                    Level2Id = null;
                    Level1Id = null;
                    break;
                case PositionCodeLevel.Level4:
                    Level8Id = Level5Id;
                    Level7Id = Level4Id;
                    Level6Id = Level3Id;
                    Level5Id = Level2Id;
                    Level4Id = Level1Id;
                    Level3Id = null;
                    Level2Id = null;
                    Level1Id = null;
                    break;
                case PositionCodeLevel.Level3:
                    Level8Id = Level6Id;
                    Level7Id = Level5Id;
                    Level6Id = Level4Id;
                    Level5Id = Level3Id;
                    Level4Id = Level2Id;
                    Level3Id = Level1Id;
                    Level2Id = null;
                    Level1Id = null;
                    break;
                case PositionCodeLevel.Level2:
                    Level8Id = Level7Id;
                    Level7Id = Level6Id;
                    Level6Id = Level5Id;
                    Level5Id = Level4Id;
                    Level4Id = Level3Id;
                    Level3Id = Level2Id;
                    Level2Id = Level1Id;
                    Level1Id = null;
                    break;
            }
            return this;
        }
    }
}
