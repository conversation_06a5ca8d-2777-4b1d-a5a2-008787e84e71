﻿# Steps for Setting up the FA_FA_Gamification repository:
#### **Prerequisite**: _Access to the repository and FA databases and **Vault Access**, and Visual Studio Community installed._
1. Clone the repository on Visual Studio through azure login.
2. Now open FA_Gamification.sln > Set GamificationProcessor as a start up Project.
3. In VS>tools>options>Azure Authentication Service>Choose your f2k azure account > OK. (Do Login in Windows Work Account too with your f2k profile)
4. To Run the GamificationProcessor
   - Go to GamificationProcessor.Tests
   - Open StartProcessorTest.cs
   - Set KEYVAULT_ENDPOINT : "https://v3DebugWritable.vault.azure.net/"
   - Change json details like Id,EmployeeId,CompanyId,QualifiedDateKey,EventTime
5. Select StartProcessorTest method and right click and click on debug test.