﻿using Libraries.Cryptography;
using Microsoft.WindowsAzure.Storage;
using Microsoft.WindowsAzure.Storage.Blob;
using System;
using System.IO;
using System.Threading.Tasks;

namespace Library.StorageWriter
{
    public class BlobReader
    {
        protected readonly CloudBlobContainer _container;

        public BlobReader(string storageConnectionString, string containerName)
        {

            var storageAccount = CloudStorageAccount.Parse(storageConnectionString);
            var blobClient = storageAccount.CreateCloudBlobClient();
            _container = blobClient.GetContainerReference(containerName);
        }
        public async Task<bool> IsExists(string file)
        {
            var blockBlob = _container.GetBlockBlobReference(file);
            return await blockBlob.ExistsAsync();
        }
        public async Task<BlobResultSegment> GetBlobNames(string lastToken)
        {
            var lastBlobToken = new BlobContinuationToken
            {
                NextMarker = lastToken
            };
            return await _container.ListBlobsSegmentedAsync(lastBlobToken);
        }
        public async Task<byte[]> GetImageBytes(string image)
        {
            var blockBlob = _container.GetBlockBlobReference(image);
            var stream = await blockBlob.OpenReadAsync();
            byte[] buffer = new byte[16 * 1024];
            using (MemoryStream ms = new MemoryStream())
            {
                int read;
                while ((read = stream.Read(buffer, 0, buffer.Length)) > 0)
                {
                    ms.Write(buffer, 0, read);
                }
                return ms.ToArray();
            }
        }
        public async Task<Stream> DownloadFileAsStream(string name)
        {
            var blob = _container.GetBlockBlobReference(name) as CloudBlockBlob;
            try
            {
                var stream = new MemoryStream();
                await blob.DownloadToStreamAsync(stream);
                stream.Position = 0;
                return stream;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        public async Task<string> GetBlobAsString(string sourceName)
        {
            var str = await DownloadFileAsStream(sourceName);
            StreamReader reader = new StreamReader(str);
            return reader.ReadToEnd();
        }

        public string GetPublicPath(string filename)
        {
            var path = _container.Uri.AbsoluteUri + "/" + filename;
            path = path.Replace("https://locationsnetwork.blob.core.windows.net", "http://static.fieldassist.io");
            //25 jan 2022; Asana: https://app.asana.com/0/305436650865282/1201691116024935/f; Change: Add WhiteSpace Check
            return !String.IsNullOrWhiteSpace(filename) ? path : null;
        }

        public virtual string GetPrivatePath(string filename, string portalPath, long userId)
        {
            if (filename == null) return null;
            var encryptedFilename = AESEncryptor.Encrypt(filename, $"Nm5BycFeL3AucSmN_{userId}");
            encryptedFilename = Uri.EscapeDataString(encryptedFilename);
            return $"{portalPath}/FieldAssistPOC/Download/ReportsDownload?encryptedFileName={encryptedFilename}";
        }

    }
}
