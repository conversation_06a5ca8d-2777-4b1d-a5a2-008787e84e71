﻿using Microsoft.VisualStudio.TestTools.UnitTesting;
using ResilientHttpClient;
using System;
using System.Collections.Generic;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace Library.Tests
{
    [TestClass]
    public class ResilientHttpClientTest
    {
        private FAResilientHttpClient resilientClient;
        [TestInitialize]
        public void Initialise()
        {
        }


        [TestMethod]
        public async Task HttpTaskResiliency()
        {
            //await resilientAction.RetryResilientlyAsync(DemoTaskAsync);
            var maxRetry = 3;
            resilientClient = new FAResilientHttpClient(new List<HttpStatusCode> { HttpStatusCode.NotFound });
            try
            {
                var abc = await resilientClient.GetAsync("http://www.googlee.com");
                Assert.Fail();
            }
            catch (Exception ex)
            {
                Assert.AreEqual(ex.Message, $"ErrorParams {maxRetry}");
            }
        }
    }
}
