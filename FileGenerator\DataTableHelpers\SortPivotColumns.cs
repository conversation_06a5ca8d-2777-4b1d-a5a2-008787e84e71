﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Text;

namespace FileGenerator.DataTableHelpers
{
    public class SortPivotColumns
    {
        public DataTable GetSortedPivotTable(DataTable dt,List<Dictionary<string,int>> sortingDictionaryList)
        {
            if(sortingDictionaryList == null || sortingDictionaryList.Count==0)
            {
                return dt;
            }
            dt.Columns["Id"].SetOrdinal(0);
            foreach (var sortingDictionary in sortingDictionaryList)
            {
                foreach (var item in sortingDictionary)
                {
                    if (dt.Columns.Contains(item.Key))
                    {
                        dt.Columns[item.Key].SetOrdinal(item.Value);
                    }
                }
            }
            return dt;

        }
    }
}
