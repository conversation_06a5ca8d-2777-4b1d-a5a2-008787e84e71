﻿using GamificationAutomatedProcessor.Core.Repositories;
using GamificationAutomatedProcessor.DbStorage.DbContexts;
using GamificationAutomatedProcessor.DbStorage.DbModels;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GamificationAutomatedProcessor.DbStorage.Repositories
{
    public class PositionCodeEntityMappingRepository : IPositionCodeEntityMappingRepository
    {
        private readonly MasterDbContext db;
        public PositionCodeEntityMappingRepository(MasterDbContext db)
        {
            this.db = db;
        }

        public async Task<long?> GetPositionCodeCorrespondingToUser(long empId)
        {
            return await db.PositionCodeEntityMappings.Where(a => a.EntityId == empId && !a.IsDetached ).OrderBy(x=> x.CreatedAt).Select(a => a.PositionCodeId).FirstOrDefaultAsync();
        }
    }
}
