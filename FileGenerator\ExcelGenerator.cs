﻿using FileGenerator.Attributes;
using FileGenerator.DataTableHelpers;
using FileGenerator.HelperModels;
using FileGenerator.Interfaces;
using Libraries.CommonEnums;
using OfficeOpenXml;
using OfficeOpenXml.Style;
using Org.BouncyCastle.Asn1.X509;
using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Reflection;
using Newtonsoft.Json;
using Libraries.CommonEnums.Helpers;

namespace FileGenerator
{
    public class ExcelGenerator
    {
        private readonly CompanyNomenclatureSpecifier nomenclatureSpecifier;
        private readonly bool _useNomenclature;
        private readonly bool _showAllColumns;
        private readonly bool isSettingEnabled;
        private readonly Dictionary<string, object> companySettings;
        private readonly Dictionary<string, bool> selectedCols;

        public ExcelGenerator(CompanyNomenclatureSpecifier nomenclatureSpecifier = null,bool showAllColumns = false, bool isSettingEnabled = false, Dictionary<string,object> companySettings = null, Dictionary<string, bool> selectedCols= null)
        {
            this.nomenclatureSpecifier = nomenclatureSpecifier;
            _useNomenclature = nomenclatureSpecifier != null;
            _showAllColumns = showAllColumns;
            this.isSettingEnabled = isSettingEnabled;
            this.companySettings = companySettings;
            this.selectedCols = selectedCols;
        }
        public ExcelPackage MakeExcel(long companyId, string[] sheetNames, params IEnumerable<object>[] listsOfData)
        {
            var stream = new MemoryStream();
            ExcelPackage excelPackage = MakeExcel(companyId, stream, sheetNames, listsOfData);
            excelPackage.Save();
            return excelPackage;
        }

        public void MakeExcelWithGrouping(long companyId, Stream stream, string sheetName,  IEnumerable<object> listsOfData, Dictionary<string, string> groupData = null)
        {
            ExcelPackage excelPackage = MakeExcel(companyId, stream, sheetName, listsOfData, groupData);
            excelPackage.Dispose();
        }
        public void MakeExcelWithFlexibleGrouping(long companyId, Stream stream, string[] sheetName, Dictionary<string, List<string>> groupData, bool showSubotals, bool useNomenclatureUpdated = false, params IEnumerable<object>[] listsOfData)
        {
            ExcelPackage excelPackage = MakeExcel(companyId, stream, sheetName, listsOfData, groupData, showSubotals, useNomenclatureUpdated: useNomenclatureUpdated);
            excelPackage.Dispose();
        }
        public void MakeExcelLTFWithFlexibleGrouping(long companyId, Stream stream, string[] sheetName, Dictionary<string, List<string>> groupData, bool showSubotals, DateTime StartDate, DateTime EndDate, string[] Measures, bool useNomenclatureUpdated = false, params IEnumerable<object>[] listsOfData)
        {
            ExcelPackage excelPackage = MakeLTFExcel(companyId, stream, sheetName, listsOfData, groupData, showSubotals, StartDate, EndDate, Measures, useNomenclatureUpdated);
            excelPackage.Dispose();
        }
        private ExcelPackage MakeLTFExcel(long companyId, Stream stream, string[] sheetNames, IEnumerable<object>[] listsOfData, Dictionary<string, List<string>> groupData, bool showSubotals, DateTime StartDate, DateTime EndDate, string[] Measures, bool useNomenclatureUpdated = false)
        {
            stream = stream ?? new MemoryStream();
            ExcelPackage excelPackage;
            MemoryStream memoryStream = null;
            bool shouldCopyFromMemoryStream = false;
            if (stream.CanWrite)
            {
                if (stream.CanRead)
                {
                    excelPackage = new ExcelPackage(stream);
                }
                else
                {
                    memoryStream = new MemoryStream();
                    shouldCopyFromMemoryStream = true;
                    excelPackage = new ExcelPackage(memoryStream);
                }
            }
            else
            {
                throw new Exception("Stream should be Writable!");
            }
            sheetNames = sheetNames ?? new string[] { "DataSheet_0" };

            for (int i = 0; i < listsOfData.Length; i++)
            {
                var listOfData = listsOfData[i];
                var sheetname = sheetNames.Length > i ? sheetNames[i] : $"DataSheet_{i}";
                var worksheet = excelPackage.Workbook.Worksheets.Add(sheetname);
                if (listOfData.Count() > 0)
                {
                    worksheet = CreateLTExcel(companyId, listOfData, worksheet, showSubotals: showSubotals, StartDate : StartDate, EndDate : EndDate, Measures: Measures,useNomenclatureUpdated : useNomenclatureUpdated);
                    GroupLTExcelData(worksheet, groupData);
                }
                else
                {
                    worksheet = CreateExcelForErrorAndNoData(worksheet, "Data not available for this request");
                }
            }
            excelPackage.Save();
            if (shouldCopyFromMemoryStream)
            {
                memoryStream.Flush();
                memoryStream.Position = 0;
                memoryStream.CopyTo(stream);
            }
            if (stream != null)
            {
                stream.Flush();
                if (stream.CanSeek)
                {
                    stream.Position = 0;
                }
                else
                {
                    stream.Close();
                }
            }
            return excelPackage;
        }
        private ExcelPackage MakeExcel(long companyId, Stream stream, string[] sheetNames, IEnumerable<object>[] listsOfData, Dictionary<string, List<string>> groupData, bool showSubotals, bool useNomenclatureUpdated = false)
        {
            stream = stream ?? new MemoryStream();
            ExcelPackage excelPackage;
            MemoryStream memoryStream = null;
            bool shouldCopyFromMemoryStream = false;
            if (stream.CanWrite)
            {
                if (stream.CanRead)
                {
                    excelPackage = new ExcelPackage(stream);
                }
                else
                {
                    memoryStream = new MemoryStream();
                    shouldCopyFromMemoryStream = true;
                    excelPackage = new ExcelPackage(memoryStream);
                }
            }
            else
            {
                throw new Exception("Stream should be Writable!");
            }
            sheetNames = sheetNames ?? new string[] { "DataSheet_0" };

            for (int i = 0; i < listsOfData.Length; i++)
            {
                var listOfData = listsOfData[i];
                var sheetname = sheetNames.Length > i ? sheetNames[i] : $"DataSheet_{i}";
                var worksheet = excelPackage.Workbook.Worksheets.Add(sheetname);
                if (listOfData.Count() > 0)
                {
                    worksheet = CreateExcel(companyId, listOfData, worksheet, showSubotals: showSubotals, useNomenclatureUpdated: useNomenclatureUpdated);
                    GroupExcelData(worksheet, groupData);
                }
                else
                {
                    worksheet = CreateExcelForErrorAndNoData(worksheet, "Data not available for this request");
                }
            }
            excelPackage.Save();
            if (shouldCopyFromMemoryStream)
            {
                memoryStream.Flush();
                memoryStream.Position = 0;
                memoryStream.CopyTo(stream);
            }
            if (stream != null)
            {
                stream.Flush();
                if (stream.CanSeek)
                {
                    stream.Position = 0;
                }
                else
                {
                    stream.Close();
                }
            }
            return excelPackage;
        }

        public void GroupExcelData(ExcelWorksheet worksheet, Dictionary<string, List<string>> groupData, bool removeGroupKey = true)
        {
            if (groupData != null && groupData.Count > 0)
            {
                worksheet.InsertRow(1, 1);
                var mergeColumns = new List<string>();
                foreach (var group in groupData)
                {
                    var seconRow = worksheet.Cells["A2:XFD2"];
                    foreach (var cell in seconRow)
                    {
                        var cellVal = cell.GetValue<string>();
                        if (group.Value.Contains(cellVal))
                        {
                            mergeColumns.Add(cell.Address.Replace("2", "1"));
                            if (removeGroupKey)
                            {
                                cell.Value = cellVal.Replace(group.Key, "");
                            }
                        }
                    }
                    if (mergeColumns.Count > 0)
                    {
                        //mergeColumns = mergeColumns.OrderBy(s => s).ToList();
                        var firstCellAddress = mergeColumns.First().Split(':').First();
                        var lastCellAddress = mergeColumns.Last().Split(':').Last();
                        var range = firstCellAddress + ":" + lastCellAddress;
                        worksheet.Cells[range].Merge = true;
                        worksheet.Cells[range].Value = group.Key;
                        worksheet.Cells[range].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                        worksheet.Cells[range].Style.Fill.PatternType = ExcelFillStyle.Solid;
                        worksheet.Cells[range].Style.Border.BorderAround(ExcelBorderStyle.Medium);
                        worksheet.Cells[range].Style.Fill.BackgroundColor.SetColor(Color.GreenYellow);
                        mergeColumns.RemoveAll(s => true);
                    }
                }
                if (worksheet.Cells["A1:XFD1"].All(eb => string.IsNullOrWhiteSpace(eb.GetValue<string>())))
                {
                    worksheet.DeleteRow(1);
                }
            }
        }

        public void GroupLTExcelData(ExcelWorksheet worksheet, Dictionary<string, List<string>> groupData, bool removeGroupKey = true)
        {
            if (groupData != null && groupData.Count > 0)
            {
                worksheet.InsertRow(1, 1);
                var mergeColumns = new List<string>();
                foreach (var group in groupData)
                {
                    var seconRow = worksheet.Cells["A2:XFD2"];
                    foreach (var cell in seconRow)
                    {
                        var oldcellVal = cell.GetValue<string>();
                        var newcellVal = oldcellVal != null ? oldcellVal.Split('_')[0] : null;
                        if (group.Value.Contains(newcellVal))
                        {
                            mergeColumns.Add(cell.Address.Replace("2","1"));
                            if (removeGroupKey)
                            {
                                cell.Value = oldcellVal.Split('_')[1];
                            }
                        }
                    }
                    if(mergeColumns.Count > 0)
                    {
                        //mergeColumns = mergeColumns.OrderBy(s => s).ToList();
                        var firstCellAddress = mergeColumns.First().Split(':').First();
                        var lastCellAddress = mergeColumns.Last().Split(':').Last();
                        var range = firstCellAddress + ":" + lastCellAddress;
                        worksheet.Cells[range].Merge = true;
                        worksheet.Cells[range].Value = group.Key;
                        worksheet.Cells[range].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                        worksheet.Cells[range].Style.Fill.PatternType = ExcelFillStyle.Solid;
                        worksheet.Cells[range].Style.Border.BorderAround(ExcelBorderStyle.Medium);
                        worksheet.Cells[range].Style.Fill.BackgroundColor.SetColor(Color.LightGray);
                        worksheet.Cells[range].Style.Font.Bold = true;
                        mergeColumns.RemoveAll(s => true);
                    }
                }
                if (worksheet.Cells["A1:XFD1"].All(eb => string.IsNullOrWhiteSpace(eb.GetValue<string>())))
                {
                    worksheet.DeleteRow(1);
                }
                try
                {
                    worksheet.Cells[worksheet.Dimension.Address].AutoFitColumns();
                }
                catch
                {

                }
            }
        }

        private bool CheckIfColRequired<T>(PropertyInfo colProp, IEnumerable<T> listOfData)
        {
            if (isSettingEnabled)
            {
                foreach (var item in listOfData)
                {
                    if (colProp.GetValue(item) != null && !string.IsNullOrWhiteSpace(colProp.GetValue(item).ToString()))
                    {
                         return true;
                                             
                    }
                    else
                    {
                        if (_showAllColumns)
                        {
                            return true;
                        }
                    }
                }
                return false;
            }

            return false;
        }

        private ExcelPackage MakeExcel(long companyId, Stream stream, string sheetName,  IEnumerable<object> listOfData, Dictionary<string,string> groupData = null)
        {
            stream = stream ?? new MemoryStream();
            ExcelPackage excelPackage;
            MemoryStream memoryStream = null;
            bool shouldCopyFromMemoryStream = false;
            if (stream.CanWrite)
            {
                if (stream.CanRead)
                {
                    excelPackage = new ExcelPackage(stream);
                }
                else
                {
                    memoryStream = new MemoryStream();
                    shouldCopyFromMemoryStream = true;
                    excelPackage = new ExcelPackage(memoryStream);
                }
            }
            else
            {
                throw new Exception("Stream should be Writable!");
            }

           
            var worksheet = excelPackage.Workbook.Worksheets.Add(sheetName); 
            if (listOfData.Count() > 0)
            {
                worksheet = CreateExcel(companyId, listOfData, worksheet, groupData);
            }
            else
            {
                worksheet = CreateExcelForErrorAndNoData(worksheet, "Data not available for this request");
            }
            excelPackage.Save();
            if (shouldCopyFromMemoryStream)
            {
                memoryStream.Flush();
                memoryStream.Position = 0;
                memoryStream.CopyTo(stream);
            }
            if (stream != null)
            {
                stream.Flush();
                if (stream.CanSeek)
                {
                    stream.Position = 0;
                }
                else
                {
                    stream.Close();
                }
            }
            return excelPackage;
        }
        private ExcelPackage MakeExcel(long companyId, Stream stream, string[] sheetNames, params IEnumerable<object>[] listsOfData)
        {
            stream = stream ?? new MemoryStream();
            ExcelPackage excelPackage;
            MemoryStream memoryStream = null;
            bool shouldCopyFromMemoryStream = false;
            if (stream.CanWrite)
            {
                if (stream.CanRead)
                {
                    excelPackage = new ExcelPackage(stream);
                }
                else
                {
                    memoryStream = new MemoryStream();
                    shouldCopyFromMemoryStream = true;
                    excelPackage = new ExcelPackage(memoryStream);
                }
            }
            else
            {
                throw new Exception("Stream should be Writable!");
            }
            sheetNames = sheetNames ?? new string[] { "DataSheet_0" };

            for (int i = 0; i < listsOfData.Length; i++)
            {
                var listOfData = listsOfData[i];
                var sheetname = sheetNames.Length > i ? sheetNames[i] : $"DataSheet_{i}";
                var worksheet = excelPackage.Workbook.Worksheets.Add(sheetname);
                if (listOfData.Count() > 0)
                {
                    worksheet = CreateExcel(companyId, listOfData, worksheet);
                }
                else
                {
                    worksheet = CreateExcelForErrorAndNoData(worksheet, "Data not available for this request");
                }
            }
            excelPackage.Save();
            if (shouldCopyFromMemoryStream)
            {
                memoryStream.Flush();
                memoryStream.Position = 0;
                memoryStream.CopyTo(stream);
            }
            if (stream != null)
            {
                stream.Flush();
                if (stream.CanSeek)
                {
                    stream.Position = 0;
                }
                else
                {
                    stream.Close();
                }
            }
            return excelPackage;
        }
        public void MakeExcelToStream(long companyId, Stream stream, string[] sheetNames, params IEnumerable<object>[] listsOfData)
        {

            ExcelPackage excelPackage = MakeExcel(companyId, stream, sheetNames, listsOfData);
            excelPackage.Dispose();
        }
        public ExcelPackage MakeExcel<T>(long companyId, ExcelPackage excelPackage, string sheetName, IEnumerable<T> data, string pivotColumn, string pivotValue, string defaultGroupColumnName = null, Dictionary<string, string> conditionalFormattingDictionary = null, List<Dictionary<string, int>> sortingDictionaryList = null, Dictionary<string, string> groupDisplayTextDictionary = null)
        {
            excelPackage = excelPackage ?? new ExcelPackage();
            var worksheet = excelPackage.Workbook.Worksheets.Add(sheetName);
            if (data.Count() > 0)
            {
                var pivotColumnObject = new PivotColumn
                {
                    ParentColumn = pivotColumn,
                    ValueColumn = pivotValue,
                    
                };
                UpdatePivotExcelSheet(companyId, worksheet, data, pivotColumnObject, defaultGroupColumnName, conditionalFormattingDictionary,sortingDictionaryList, groupDisplayTextDictionary);
            }
            else
            {
                CreateExcelForErrorAndNoData(worksheet, "Data not available for this request");
            }
            excelPackage.Save();
            return excelPackage;
        }
        public ExcelPackage MakeExcel<T>(long companyId, ExcelPackage excelPackage, string sheetName, IEnumerable<T> data, PivotColumn pivotColumn, string defaultGroupColumnName = null, Dictionary<string, string> conditionalFormattingDictionary = null, List<Dictionary<string, int>> sortingDictionaryList = null, Dictionary<string, string> groupDisplayTextDictionary = null)
        {
            excelPackage = excelPackage ?? new ExcelPackage();
            var worksheet = excelPackage.Workbook.Worksheets.Add(sheetName);
            if (data.Count() > 0)
            {
                UpdatePivotExcelSheet(companyId, worksheet, data, pivotColumn, defaultGroupColumnName, conditionalFormattingDictionary, sortingDictionaryList, groupDisplayTextDictionary);
            }
            else
            {
                CreateExcelForErrorAndNoData(worksheet, "Data not available for this request");
            }
            excelPackage.Save();
            return excelPackage;
        }

        public ExcelPackage MakeDMSExcel<T>(long companyId, ExcelPackage excelPackage, string sheetName, IEnumerable<T> data, PivotColumn pivotColumn, string defaultGroupColumnName = null, Dictionary<string, string> conditionalFormattingDictionary = null, List<Dictionary<string, int>> sortingDictionaryList = null, Dictionary<string, string> groupDisplayTextDictionary = null, List<List<CumulativeReportData>> cumulativeReportData = null)
        {
            excelPackage = excelPackage ?? new ExcelPackage();
            var worksheet = excelPackage.Workbook.Worksheets.Add(sheetName);
            if (data.Count() > 0)
            {
                UpdatePivotExcelSheet(companyId, worksheet, data, pivotColumn, defaultGroupColumnName, conditionalFormattingDictionary, sortingDictionaryList, groupDisplayTextDictionary);
                GroupDMSExcelData(worksheet, cumulativeReportData);
            }
            else
            {
                CreateExcelForErrorAndNoData(worksheet, "Data not available for this request");
            }
            excelPackage.Save();
            return excelPackage;
        }

        public ExcelPackage MakeHeaders<T>(long companyId, ExcelPackage excelPackage, string sheetName, IEnumerable<T> headers, List<List<CumulativeReportData>> groupData)
        {
            excelPackage = excelPackage ?? new ExcelPackage();
            var worksheet = excelPackage.Workbook.Worksheets.Add(sheetName);
            int index = 1;
            foreach(var header in headers)
            {
                worksheet.Cells[1, index].Value = header;
                worksheet.Cells[1, index].Style.Font.Bold = true;
                worksheet.Cells[1, index].Style.Border.BorderAround(ExcelBorderStyle.Thin);
                worksheet.Cells[1, index].Style.Fill.PatternType = ExcelFillStyle.Solid;
                worksheet.Cells[1, index++].Style.Fill.BackgroundColor.SetColor(Color.FromArgb(0, 150, 207));
            }
            excelPackage.Save();
            GroupDMSExcelDataWithPivoting(worksheet, groupData);
            worksheet.Cells[worksheet.Dimension.Address].AutoFitColumns();
            excelPackage.Save();
            return excelPackage;
        }

        public ExcelPackage MakeSummaryExcel(long companyId, ExcelPackage excelPackage, string sheetName, List<DMSReportTableCreator> tableField, Dictionary<string, string> salesData, Dictionary<string, string> callsData)
        {
            excelPackage = excelPackage ?? new ExcelPackage();
            var worksheet = excelPackage.Workbook.Worksheets.Add(sheetName);
            foreach(var field in tableField)
            {
                worksheet.Cells[field.Range].Value = field.Value;
                worksheet.Cells[field.Range].Merge = field.Merge;
                worksheet.Cells[field.Range].Style.HorizontalAlignment = field.AlignCenter ? ExcelHorizontalAlignment.Center 
                    : ExcelHorizontalAlignment.Left;
                worksheet.Cells[field.Range].Style.Border.BorderAround(ExcelBorderStyle.Thin);
            }
            int index = 5;
            foreach (var header in salesData)
            {
                worksheet.Cells[index, 1].Value = header.Key;
                worksheet.Cells[index, 1].Style.Font.Bold = true;
                worksheet.Cells[index, 1].Style.Border.BorderAround(ExcelBorderStyle.Thin);
                worksheet.Cells[index, 2].Value = header.Value;
                worksheet.Cells[index, 2].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                worksheet.Cells[index++, 2].Style.Border.BorderAround(ExcelBorderStyle.Thin);
            }

            while (index <= 32)
            {
                worksheet.Cells[index, 1].Style.Border.BorderAround(ExcelBorderStyle.Thin);
                worksheet.Cells[index++, 2].Style.Border.BorderAround(ExcelBorderStyle.Thin);
            }

            index = 23;
            foreach (var header in callsData)
            {
                var address = "H" + index + ":K" + index;
                worksheet.Cells[address].Merge = true;
                worksheet.Cells[address].Value = header.Key;
                worksheet.Cells[address].Style.Font.Bold = true;
                worksheet.Cells[address].Style.Border.BorderAround(ExcelBorderStyle.Thin);
                worksheet.Cells[index, 12].Value = header.Value;
                worksheet.Cells[index, 12].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                worksheet.Cells[index, 13].Style.Border.BorderAround(ExcelBorderStyle.Thin);
                worksheet.Cells[index++, 12].Style.Border.BorderAround(ExcelBorderStyle.Thin);
            }
            worksheet.Cells[worksheet.Dimension.Address].AutoFitColumns();
            excelPackage.Save();
            return excelPackage;
        }

        public void GroupDMSExcelDataWithPivoting(ExcelWorksheet worksheet, List<List<CumulativeReportData>> groupData, bool removeGroupKey = true)
        {
            try
            {
                if (groupData != null && groupData.Count() > 0)
                {
                    foreach (var cumulativeData in groupData)
                    {
                        var mergeColumns = new List<string>();
                        int newRow = worksheet.Dimension.Rows + 1;
                        foreach (var data in cumulativeData)
                        {
                            var secondRow = worksheet.Cells["1:1"];
                            var prevPivotVal = "";
                            var curPivotVal = "";
                            foreach (var cell in secondRow)
                            {
                                var cellVal = cell.GetValue<string>();
                                var pivotCellVal = worksheet.Cells[cell.Address.Replace("1", "0")].GetValue<string>();
                                if (!string.IsNullOrWhiteSpace(pivotCellVal))
                                {
                                    curPivotVal = pivotCellVal + " ";
                                    prevPivotVal = curPivotVal;
                                }
                                else
                                {
                                    curPivotVal = prevPivotVal;
                                }
                                if (data.Headers.Contains(curPivotVal + cellVal))
                                {
                                    mergeColumns.Add(cell.Address.Replace("1", newRow.ToString()));
                                    /*if (removeGroupKey)
                                    {
                                        cell.Value = cellVal.Replace(data.Value, "");
                                    }*/
                                }
                            }
                            if (mergeColumns.Count > 0)
                            {
                                var firstCellAddress = mergeColumns.First().Split(':').First();
                                var lastCellAddress = mergeColumns.Last().Split(':').Last();
                                var range = firstCellAddress + ":" + lastCellAddress;
                                worksheet.Cells[range].Merge = true;
                                worksheet.Cells[range].Value = data.Value;
                                worksheet.Cells[range].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                                worksheet.Cells[range].Style.Border.BorderAround(ExcelBorderStyle.Thin);
                                mergeColumns.RemoveAll(s => true);
                            }
                            if (worksheet.Cells["A1:XFD1"].All(eb => string.IsNullOrWhiteSpace(eb.GetValue<string>())))
                            {
                                worksheet.DeleteRow(1);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw;
            }
        }


        public void MakeExcelToStream<T>(long companyId, Stream stream, string sheetName, IEnumerable<T> data, string pivotColumn, string pivotValue, string defaultGroupColumnName = null)
        {
            var excelPackage = MakeExcel(companyId, stream, sheetName, data, pivotColumn, pivotValue, defaultGroupColumnName);
            excelPackage.Dispose();
        }
        public ExcelPackage MakeExcel<T>(long companyId, Stream stream, string sheetName, IEnumerable<T> data, string pivotColumn, string pivotValue, string defaultGroupColumnName = null)
        {
            ExcelPackage excelPackage;
            MemoryStream memoryStream = null;
            bool shouldCopyFromMemoryStream = false;
            stream = stream ?? new MemoryStream();
            if (stream.CanWrite)
            {
                if (stream.CanRead)
                {
                    excelPackage = new ExcelPackage(stream);
                }
                else
                {
                    memoryStream = new MemoryStream();
                    shouldCopyFromMemoryStream = true;
                    excelPackage = new ExcelPackage(memoryStream);
                }
            }
            else
            {
                throw new Exception("Stream should be Writable!");
            }
            var worksheet = excelPackage.Workbook.Worksheets.Add(sheetName);
            var pivotColumnObject = new PivotColumn
            {
                ParentColumn = pivotColumn,
                ValueColumn = pivotValue
            };
            if (data.Count() > 0)
            {
                UpdatePivotExcelSheet(companyId, worksheet, data, pivotColumnObject, defaultGroupColumnName);
            }
            else
            {
                CreateExcelForErrorAndNoData(worksheet, "Data not available for this request");
            }
            excelPackage.Save();
            if (shouldCopyFromMemoryStream)
            {
                memoryStream.Flush();
                memoryStream.Position = 0;
                memoryStream.CopyTo(stream);
            }

            stream.Flush();
            if (stream.CanSeek)
            {
                stream.Position = 0;
            }
            else
            {
                stream.Close();
            }
            return excelPackage;
        }

        public void MakeExcelToStream<T>(long companyId, Stream stream, string sheetName, IEnumerable<T> data, PivotColumn pivotColumns, string defaultGroupColumnName = null, Dictionary<string, string> conditionalFormattingDictionary = null,List<Dictionary<string,int>> sortingDictionaryList = null, Dictionary<string, string> groupDisplayTextDictionary=null, IEnumerable<T> horizonSubgroupData = null, string VerticalTotalColumn = null, bool useDoublePivot = false, string delimiter = "_", IEnumerable<string> columnsToDeleteFromPivot = null, bool forceNumberTypeInpivot = false,List<string> derivedkpis =null, bool isUsingDMS = false, List<List<CumulativeReportData>> dmsCumulativeData = null)
        {
            ExcelPackage excelPackage = MakeExcel(companyId, stream, sheetName, data, pivotColumns, defaultGroupColumnName, conditionalFormattingDictionary, sortingDictionaryList, groupDisplayTextDictionary, horizontalSubgroup: horizonSubgroupData, VerticalTotalColumn : VerticalTotalColumn, useDoublePivot: useDoublePivot, delimiter: delimiter, columnsToDeleteFromPivot: columnsToDeleteFromPivot, forceNumberTypeInpivot : forceNumberTypeInpivot, derivedkpis : derivedkpis, isUsingDMS : isUsingDMS, dmsCumulativeData : dmsCumulativeData);
            excelPackage.Dispose();
        }

        private ExcelPackage MakeExcel<T>(long companyId, Stream stream, string sheetName, IEnumerable<T> data, PivotColumn pivotColumns, string defaultGroupColumnName = null, Dictionary<string, string> conditionalFormattingDictionary = null, List<Dictionary<string, int>> sortingDictionaryList = null, Dictionary<string, string> groupDisplayTextDictionary = null, IEnumerable<T> horizontalSubgroup = null, string VerticalTotalColumn = null, bool useDoublePivot = false, string delimiter = "_", IEnumerable<string> columnsToDeleteFromPivot = null, bool forceNumberTypeInpivot = false, List<string> derivedkpis = null, bool isUsingDMS = false, List<List<CumulativeReportData>> dmsCumulativeData = null)
        {
            ExcelPackage excelPackage;
            MemoryStream memoryStream = null;
            bool shouldCopyFromMemoryStream = false;
            stream = stream ?? new MemoryStream();
            if (stream.CanWrite)
            {
                if (stream.CanRead)
                {
                    excelPackage = new ExcelPackage(stream);
                }
                else
                {
                    memoryStream = new MemoryStream();
                    shouldCopyFromMemoryStream = true;
                    excelPackage = new ExcelPackage(memoryStream);
                }
            }
            else
            {
                throw new Exception("Stream should be Writable!");
            }
            var worksheet = excelPackage.Workbook.Worksheets.Add(sheetName);
            if (data == null || data.Count() == 0)
            {
                CreateExcelForErrorAndNoData(worksheet, "Data not available for this request");
            }
            else
            {
                UpdatePivotExcelSheet(companyId, worksheet, data, pivotColumns,  defaultGroupColumnName, conditionalFormattingDictionary, sortingDictionaryList, groupDisplayTextDictionary, horizontalSubgroup: horizontalSubgroup, VerticalTotalColumn: VerticalTotalColumn, useDoublePivot: useDoublePivot, delimiter, columnsToDeleteFromPivot: columnsToDeleteFromPivot, forceNumberTypeInpivot: forceNumberTypeInpivot,derivedkpis : derivedkpis);
                if (isUsingDMS)
                {
                    GroupDMSExcelData(worksheet, dmsCumulativeData);
                }
            }
            excelPackage.Save();
            if (shouldCopyFromMemoryStream)
            {
                memoryStream.Flush();
                memoryStream.Position = 0;
                memoryStream.CopyTo(stream);
            }

            stream.Flush();
            if (stream.CanSeek)
            {
                stream.Position = 0;
            }
            else
            {
                stream.Close();
            }
            return excelPackage;
        }

        public void GroupDMSExcelData(ExcelWorksheet worksheet, List<List<CumulativeReportData>> groupData, bool removeGroupKey = true)
        {
            try
            {
                if (groupData != null && groupData.Count() > 0)
                {
                    foreach (var cumulativeData in groupData)
                    {
                        var firstRow = worksheet.Cells["1:1"];
                        var mergeColumns = new List<string>();
                        int newRow = worksheet.Dimension.Rows + 1;
                        foreach (var data in cumulativeData)
                        {
                            var secondRow = worksheet.Cells["2:2"];
                            var prevPivotVal = "";
                            var curPivotVal = "";
                            foreach (var cell in secondRow)
                            {
                                var cellVal = cell.GetValue<string>();
                                var pivotCellVal = worksheet.Cells[cell.Address.Replace("2", "1")].GetValue<string>();
                                if (!string.IsNullOrWhiteSpace(pivotCellVal))
                                {
                                    curPivotVal = pivotCellVal + " ";
                                    prevPivotVal = curPivotVal;
                                }
                                else
                                {
                                    curPivotVal = prevPivotVal;
                                }
                                if (data.Headers.Contains(curPivotVal + cellVal))
                                {
                                    mergeColumns.Add(cell.Address.Replace("2", newRow.ToString()));
                                    if (removeGroupKey)
                                    {
                                        cell.Value = cellVal.Replace(data.Value, "");
                                    }
                                }
                            }
                            if (mergeColumns.Count > 0)
                            {
                                //mergeColumns = mergeColumns.OrderBy(s => s).ToList();
                                var firstCellAddress = mergeColumns.First().Split(':').First();
                                var lastCellAddress = mergeColumns.Last().Split(':').Last();
                                var range = firstCellAddress + ":" + lastCellAddress;
                                worksheet.Cells[range].Merge = true;
                                worksheet.Cells[range].Value = data.Value;
                                worksheet.Cells[range].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                                worksheet.Cells[range].Style.Fill.PatternType = ExcelFillStyle.Solid;
                                worksheet.Cells[range].Style.Border.BorderAround(ExcelBorderStyle.Medium);
                                worksheet.Cells[range].Style.Fill.BackgroundColor.SetColor(Color.LightGray);
                                mergeColumns.RemoveAll(s => true);
                            }
                            if (worksheet.Cells["A1:XFD1"].All(eb => string.IsNullOrWhiteSpace(eb.GetValue<string>())))
                            {
                                worksheet.DeleteRow(1);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        private ExcelWorksheet CreateExcel<T>(long companyId, IEnumerable<T> data, ExcelWorksheet worksheet, Dictionary<string, string> groupData = null, bool showSubotals = false, bool useNomenclatureUpdated = false)
        {

            int col = 1;
            var columns = data.FirstOrDefault()?.GetType().GetProperties().Where(p => Attribute.GetCustomAttribute(p, typeof(TableFieldAttribute)) != null);
            if (columns == null)
            {
                return worksheet;
            }
            var showDataForHeader = new Dictionary<int, bool>();
            int i = 0;
            bool colGroupColoring = false;
            var prevgroup = ColGroupType.None;
            bool isPrevGroupColored = false;
            var columnsToColor = new List<TableFieldAttribute>();
            TableHelpers tableHelpers = new TableHelpers();
            foreach (var colProp in columns)
            {
                var Tattr = Attribute.GetCustomAttribute(colProp, typeof(TableFieldAttribute));
                if (Tattr is TableFieldAttribute excelAttrOfT)
                {

                    var Tcolumnname = excelAttrOfT.ColumnName;
                    if (_showAllColumns ||( excelAttrOfT.ColumnRequirement != Requirement.HideIfNull || !CheckIfColNull(colProp, data)))
                    {
                        if(excelAttrOfT.ColumnRequirement == Requirement.SettingBased && !CheckIfColRequired(colProp, data))
                        {
                            showDataForHeader.Add(i++, false);
                        }
                        else if (!_showAllColumns && excelAttrOfT.ColumnRequirement == Requirement.HideIfZero && CheckIfColZero(colProp, data))
                        {
                            showDataForHeader.Add(i++, false);
                        }
                        else if (excelAttrOfT.ColumnRequirement == Requirement.SpecificSettingBased && !CheckIfColRequiredBySetting(colProp, excelAttrOfT.CompanySettingsToCheck))
                        {
                            showDataForHeader.Add(i++, false);
                        }
                        else
                        {
                            if (!string.IsNullOrWhiteSpace(excelAttrOfT.CellFormat))
                            {
                                worksheet.Column(col).Style.Numberformat.Format = excelAttrOfT.CellFormat;
                            }
                            if (excelAttrOfT.ColGroupType != ColGroupType.None)
                            {
                                if (prevgroup == excelAttrOfT.ColGroupType)
                                {
                                    if (isPrevGroupColored)
                                    {
                                        worksheet.Column(col).Style.Fill.PatternType = ExcelFillStyle.Solid;
                                        worksheet.Column(col).Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
                                        isPrevGroupColored = true;
                                    }
                                }
                                else
                                {
                                    if (!isPrevGroupColored)
                                    {
                                        worksheet.Column(col).Style.Fill.PatternType = ExcelFillStyle.Solid;
                                        worksheet.Column(col).Style.Fill.BackgroundColor.SetColor(Color.LightGray);
                                        isPrevGroupColored = true;
                                    }
                                    else
                                    {
                                        isPrevGroupColored = false;
                                    }

                                }
                                prevgroup = excelAttrOfT.ColGroupType;
                                colGroupColoring = true;
                            }
                            else
                            {
                                prevgroup = ColGroupType.None;
                                isPrevGroupColored = false;
                            }

                            if (_useNomenclature && excelAttrOfT.NomenclatureRequirement == true)
                            {
                                if (useNomenclatureUpdated && excelAttrOfT.NomenclatureUpdated !=null)
                                {
                                    var j = 0;
                                    var listforheader = Tcolumnname.Split(' ').ToList();
                                    var listforNewHeader = Tcolumnname.Split(' ').ToList();
                                    foreach (var item in listforheader)
                                    {
                                        var nomenclaturename = nomenclatureSpecifier.GetHeaderName(item);
                                        listforNewHeader[j] = "'" + nomenclaturename + "' "+ excelAttrOfT.NomenclatureUpdated;
                                        j++;
                                    }
                                    Tcolumnname = String.Join(" ", listforNewHeader);

                                }
                                else
                                {
                                    var j = 0;
                                    var listforheader = Tcolumnname.Split(' ').ToList();
                                    var listforNewHeader = Tcolumnname.Split(' ').ToList();
                                    foreach (var item in listforheader)
                                    {
                                        var nomenclaturename = nomenclatureSpecifier.GetHeaderName(item);
                                        listforNewHeader[j] = nomenclaturename;
                                        j++;
                                    }
                                    Tcolumnname = String.Join(" ", listforNewHeader);

                                }
                            }
                            if (!string.IsNullOrWhiteSpace(excelAttrOfT.ConditionalFormat))
                            {
                                columnsToColor.Add(excelAttrOfT);
                            }
                            worksheet.Cells[1, col++].Value = Tcolumnname;
                            showDataForHeader.Add(i++, true);
                        }
                       
                    }
                    else
                    {
                        showDataForHeader.Add(i++, false);
                    }
                }
                else
                {
                    showDataForHeader.Add(i++, false);
                }
            }
            //add derived columns
            var derivedCols = data.FirstOrDefault()?.GetType().GetProperties().Where(x => x.Name == "derivedMeasures").FirstOrDefault();
            var derivedColstring = derivedCols == null ? "" : (string)derivedCols.GetValue(data.First())?? "";
            var derivedColsList = derivedColstring !="" ? JsonConvert.DeserializeObject<Dictionary<string, string>>(derivedColstring).Keys.ToList() : new List<string>();
            if (derivedColsList.Count != 0)
            {
                for (int x = 0; x < derivedColsList.Count; x++)
                {
                    worksheet.Cells[1, col++].Value = derivedColsList[x];
                }
                
            }
            //populate the data
            int totalCols = col - (col == 0 ? 0 : 1);
            int rowIndex = 2;
            foreach (var item in data)
            {
                col = 1; i = 0;
                if (!showSubotals)
                {
                    if (rowIndex % 2 == 0 && !colGroupColoring)
                    {
                        worksheet.Cells[rowIndex, 1, rowIndex, totalCols].Style.Fill.PatternType = ExcelFillStyle.Solid;
                        worksheet.Cells[rowIndex, 1, rowIndex, totalCols].Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
                    }
                }
                else if (showSubotals)
                {
                    var IsgroupRow = item.GetType().GetProperties().Where(p => p.Name == "IsSubGroupRow").FirstOrDefault();
                    bool subGroupRow = IsgroupRow == null ? false : (bool)IsgroupRow.GetValue(item);
                    if (subGroupRow)
                    {
                        worksheet.Cells[rowIndex, 1, rowIndex, totalCols].Style.Fill.PatternType = ExcelFillStyle.Solid;
                        worksheet.Cells[rowIndex, 1, rowIndex, totalCols].Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
                        worksheet.Row(rowIndex).Style.Font.Bold = true;
                    }                  
                }
                
                foreach (var colProp in columns)
                {
                    if (showDataForHeader[i++])
                    {
                        var dataValue = colProp.GetValue(item);
                        var excelAttrOfT = (TableFieldAttribute)Attribute.GetCustomAttribute(colProp, typeof(TableFieldAttribute));
                        if (dataValue != null)
                        {
                            worksheet.Cells[rowIndex, col].Value = dataValue;
                            //if (!string.IsNullOrWhiteSpace(excelAttrOfT.ConditionalFormat))
                            //{
                            //    AddConditionalFormatting(worksheet.Cells[rowIndex, col], excelAttrOfT.ConditionalFormat);
                            //}

                            if (!string.IsNullOrWhiteSpace(dataValue.ToString()) && !string.IsNullOrWhiteSpace(excelAttrOfT.HyperLinkText))
                            {
                                worksheet.Cells[rowIndex, col].Hyperlink = new Uri(dataValue.ToString(), UriKind.Absolute);
                                worksheet.Cells[rowIndex, col].Value = excelAttrOfT.HyperLinkText;
                                worksheet.Cells[rowIndex, col].Style.Font.Color.SetColor(System.Drawing.Color.Blue);
                            }
                            else
                            {
                                worksheet.Cells[rowIndex, col].Value = dataValue;
                            }

                        }
                        col++;
                    }

                }
                // add derived Measures
                if (derivedColsList.Count != 0)
                {
                    var derivedMeasuresStr = data.FirstOrDefault()?.GetType().GetProperties().Where(p => Attribute.GetCustomAttribute(p, typeof(TableFieldAttribute)) == null).Where(x => x.Name == "derivedMeasures").FirstOrDefault().GetValue(item).ToString();

                    Dictionary<string, string> derivedMeasuresDic = JsonConvert.DeserializeObject<Dictionary<string, string>>(derivedMeasuresStr);
                    foreach (var derCol in derivedColsList)
                    {
                        if (derivedMeasuresDic[derCol] != null)
                        {
                            worksheet.Cells[rowIndex, col].Value = derivedMeasuresDic[derCol];

                        }
                        col++;

                    }
                }   
                rowIndex++;
            }
            var totalRows = data.Count();

            //Styling
            worksheet.Row(1).Style.Font.Bold = true;

            var modelTable = worksheet.Cells[1, 1, totalRows + 1, totalCols];
            // Assign borders
            modelTable.Style.Border.Top.Style = ExcelBorderStyle.Thin;
            modelTable.Style.Border.Left.Style = ExcelBorderStyle.Thin;
            modelTable.Style.Border.Right.Style = ExcelBorderStyle.Thin;
            modelTable.Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
            modelTable.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
            modelTable.Style.VerticalAlignment = ExcelVerticalAlignment.Center;
            worksheet.Cells[1, 1, 1, totalCols].Style.Fill.PatternType = ExcelFillStyle.Solid;
            worksheet.Cells[1, 1, 1, totalCols].Style.Fill.BackgroundColor.SetColor(Color.FromArgb(0, 150, 207));

            try
            {
                worksheet.Cells[worksheet.Dimension.Address].AutoFitColumns();
            }
            catch
            {

            }
            var headerRow = 1;
            foreach (var colorColumn in columnsToColor)
            {
                for (int j = 1; j <= totalCols; j++)
                {
                    if (worksheet.Cells[headerRow, j].Value?.ToString() == colorColumn.ColumnName)
                    {
                        var conditionsString = colorColumn.ConditionalFormat;
                        var conditions = conditionsString.Split(',').Select(s => new
                        {
                            condition = s.Split('_')[0],
                            formula = s.Split('_')[1],
                            color = s.Split('_')[2]
                        }).ToList();
                        ExcelRange rng = worksheet.Cells[headerRow + 1, j, totalRows + 1, j];
                        foreach (var condition in conditions)
                        {
                            var condFormat = worksheet.ConditionalFormatting.AddExpression(rng);
                            condFormat.Style.Fill.BackgroundColor.Color = Color.FromName(condition.color);
                            condFormat.Formula = new ExcelFormulaAddress(rng.Address) + condition.formula;
                        }


                    }
                }
            }
           if(groupData != null)
            {
                worksheet.InsertRow(1, 1);
                var startIndex = groupData.First().Key.Split(':')[0];
                var lastIndex = groupData.Last().Key.Split(':')[1];
                var destination = startIndex + ":" + lastIndex;
                var source = destination.Replace('1', '2');
                worksheet.Cells[source].Copy(worksheet.Cells[destination]);

                foreach (KeyValuePair<string, string> x in groupData)
                {
                    worksheet.Cells[x.Key].Merge = true;
                    worksheet.Cells[x.Key].Value = x.Value;
                   
                }
            }
            return worksheet;
        }
        private ExcelWorksheet CreateLTExcel<T>(long companyId, IEnumerable<T> data, ExcelWorksheet worksheet, DateTime StartDate, DateTime EndDate, string[] Measures, Dictionary<string, string> groupData = null, bool showSubotals = false, bool useNomenclatureUpdated = false)
        {

            int col = 1;
            var columns = data.FirstOrDefault()?.GetType().GetProperties().Where(p => Attribute.GetCustomAttribute(p, typeof(TableFieldAttribute)) != null);
            if (columns == null)
            {
                return worksheet;
            }
            var showDataForHeader = new Dictionary<int, bool>();
            int i = 0;
            bool colGroupColoring = false;
            var prevgroup = ColGroupType.None;
            bool isPrevGroupColored = false;
            var columnsToColor = new List<TableFieldAttribute>();
            TableHelpers tableHelpers = new TableHelpers();
            foreach (var colProp in columns)
            {
                var Tattr = Attribute.GetCustomAttribute(colProp, typeof(TableFieldAttribute));
                if (Tattr is TableFieldAttribute excelAttrOfT)
                {

                    var Tcolumnname = excelAttrOfT.ColumnName;
                    if (_showAllColumns || (excelAttrOfT.ColumnRequirement != Requirement.HideIfNull || !CheckIfColNull(colProp, data)))
                    {
                        if (excelAttrOfT.ColumnRequirement == Requirement.SettingBased && !CheckIfColRequired(colProp, data))
                        {
                            showDataForHeader.Add(i++, false);
                        }
                        else if (excelAttrOfT.ColumnRequirement == Requirement.SpecificSettingBased && !CheckIfColRequiredBySetting(colProp, excelAttrOfT.CompanySettingsToCheck))
                        {
                            showDataForHeader.Add(i++, false);
                        }
                        else
                        {
                            if (!string.IsNullOrWhiteSpace(excelAttrOfT.CellFormat))
                            {
                                worksheet.Column(col).Style.Numberformat.Format = excelAttrOfT.CellFormat;
                            }
                            if (excelAttrOfT.ColGroupType != ColGroupType.None)
                            {
                                if (prevgroup == excelAttrOfT.ColGroupType)
                                {
                                    if (isPrevGroupColored)
                                    {
                                        worksheet.Column(col).Style.Fill.PatternType = ExcelFillStyle.Solid;
                                        worksheet.Column(col).Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
                                        isPrevGroupColored = true;
                                    }
                                }
                                else
                                {
                                    if (!isPrevGroupColored)
                                    {
                                        worksheet.Column(col).Style.Fill.PatternType = ExcelFillStyle.Solid;
                                        worksheet.Column(col).Style.Fill.BackgroundColor.SetColor(Color.LightGray);
                                        isPrevGroupColored = true;
                                    }
                                    else
                                    {
                                        isPrevGroupColored = false;
                                    }

                                }
                                prevgroup = excelAttrOfT.ColGroupType;
                                colGroupColoring = true;
                            }
                            else
                            {
                                prevgroup = ColGroupType.None;
                                isPrevGroupColored = false;
                            }

                            if (_useNomenclature && excelAttrOfT.NomenclatureRequirement == true)
                            {
                                if (useNomenclatureUpdated && excelAttrOfT.NomenclatureUpdated != null)
                                {
                                    var j = 0;
                                    var listforheader = Tcolumnname.Split(' ').ToList();
                                    var listforNewHeader = Tcolumnname.Split(' ').ToList();
                                    foreach (var item in listforheader)
                                    {
                                        var nomenclaturename = nomenclatureSpecifier.GetHeaderName(item);
                                        listforNewHeader[j] = "'" + nomenclaturename + "' " + excelAttrOfT.NomenclatureUpdated;
                                        j++;
                                    }
                                    Tcolumnname = String.Join(" ", listforNewHeader);

                                }
                                else
                                {
                                    var j = 0;
                                    var listforheader = Tcolumnname.Split(' ').ToList();
                                    var listforNewHeader = Tcolumnname.Split(' ').ToList();
                                    foreach (var item in listforheader)
                                    {
                                        var nomenclaturename = nomenclatureSpecifier.GetHeaderName(item);
                                        listforNewHeader[j] = nomenclaturename;
                                        j++;
                                    }
                                    Tcolumnname = String.Join(" ", listforNewHeader);

                                }
                            }
                            if (!string.IsNullOrWhiteSpace(excelAttrOfT.ConditionalFormat))
                            {
                                columnsToColor.Add(excelAttrOfT);
                            }
                            worksheet.Cells[1, col++].Value = Tcolumnname;
                            showDataForHeader.Add(i++, true);
                        }

                    }
                    else
                    {
                        showDataForHeader.Add(i++, false);
                    }
                }
                else
                {
                    showDataForHeader.Add(i++, false);
                }
            }
            //making LinearTrendColumns
            //Monthly
            var currentMonth = StartDate;
            //Date: oct 18 2021; Asana: https://app.asana.com/0/1199705056520685/1201655723212625; change: fix month count logic
            var MonthCount = (EndDate.Month - StartDate.Month +1) - 12 * (StartDate.Year - EndDate.Year);
            var counter = 1;
            List<string> MonthList = new List<string>();
            List<string> WeekList = new List<string>();
            List<string> QuaterList = new List<string>() { "Quarter1","Quarter2"};
            while (currentMonth <= EndDate)
            {   //Monthly
                MonthList.Add(currentMonth.ToString("MMMM"));

                //Weekly
                WeekList.Add(currentMonth.ToString("MMMM").Substring(0, 3) + "-W1");
                int currentday = 2;
                int currentWeek = 1;
                DateTime MonthFirstDay = new DateTime(currentMonth.Year, currentMonth.Month, 1);
                int MonthLastDayNumber = System.DateTime.DaysInMonth(currentMonth.Year, currentMonth.Month);
                DateTime MonthCurrentDay = MonthFirstDay.AddDays(1);
                for (currentday = 2; currentday <= MonthLastDayNumber; currentday++)
                {
                    if (MonthCurrentDay.DayOfWeek == DayOfWeek.Sunday && currentday >=2)
                    {
                        currentWeek++;
                        WeekList.Add(currentMonth.ToString("MMMM").Substring(0, 3) + "-W" + currentWeek);
                    }
                    MonthCurrentDay = MonthCurrentDay.AddDays(1);
                }
                WeekList.Add("Subtotal-" + currentMonth.ToString("MMMM").Substring(0, 3));
                currentMonth = currentMonth.AddMonths(1);
            }

            //populate the data
            int totalCols = col - (col == 0 ? 0 : 1);
            int rowIndex = 2;
            foreach (var item in data)
            {
                col = 1; i = 0;
                
                var columnCounter = 1;
                if (!showSubotals)
                {
                    if (rowIndex % 2 == 0 && !colGroupColoring)
                    {
                        worksheet.Cells[rowIndex, 1, rowIndex, totalCols].Style.Fill.PatternType = ExcelFillStyle.Solid;
                        worksheet.Cells[rowIndex, 1, rowIndex, totalCols].Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
                    }
                }
                else if (showSubotals)
                {
                    var IsgroupRow = item.GetType().GetProperties().Where(p => p.Name == "IsSubGroupRow").FirstOrDefault();
                    bool subGroupRow = IsgroupRow == null ? false : (bool)IsgroupRow.GetValue(item);
                    if (subGroupRow)
                    {
                        worksheet.Cells[rowIndex, 1, rowIndex, totalCols].Style.Fill.PatternType = ExcelFillStyle.Solid;
                        worksheet.Cells[rowIndex, 1, rowIndex, totalCols].Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
                        worksheet.Row(rowIndex).Style.Font.Bold = true;
                    }
                }
                foreach (var colProp in columns)
                {
                    if (showDataForHeader[i++])
                    {
                        var dataValue = colProp.GetValue(item);
                        var excelAttrOfT = (TableFieldAttribute)Attribute.GetCustomAttribute(colProp, typeof(TableFieldAttribute));
                        if (dataValue != null)
                        {
                            worksheet.Cells[rowIndex, col].Value = dataValue;
                            
                            if (!string.IsNullOrWhiteSpace(dataValue.ToString()) && !string.IsNullOrWhiteSpace(excelAttrOfT.HyperLinkText))
                            {
                                worksheet.Cells[rowIndex, col].Hyperlink = new Uri(dataValue.ToString(), UriKind.Absolute);
                                worksheet.Cells[rowIndex, col].Value = excelAttrOfT.HyperLinkText;
                                worksheet.Cells[rowIndex, col].Style.Font.Color.SetColor(System.Drawing.Color.Blue);
                            }
                            if (Measures.Contains(colProp.Name))
                            {
                                string[] timeFrameData = dataValue.ToString().Split(',');
                                if (colProp.Name.Contains("Monthly"))
                                {
                                    for (int x = 0; x < MonthCount; x++)
                                    {
                                        worksheet.Cells[rowIndex, col].Value = timeFrameData[x];
                                        if (rowIndex == 2)
                                        {
                                            worksheet.Cells[1, col].Value = colProp.Name + "_" + MonthList[x];
                                            totalCols = totalCols + 1;
                                        }
                                        col++;
                                    }
                                    if (rowIndex == 2)
                                    {
                                        totalCols--;
                                    }
                                    col--;
                                }
                                else if (colProp.Name.Contains("Weekly"))
                                {
                                    for (int x = 0; x < WeekList.Count(); x++)
                                    {
                                       
                                        worksheet.Cells[rowIndex, col].Value = timeFrameData[x];
                                        if (WeekList[x].Contains("Subtotal"))
                                        {
                                            worksheet.Cells[rowIndex, col, rowIndex, col].Style.Fill.PatternType = ExcelFillStyle.Solid;
                                            worksheet.Cells[rowIndex, col, rowIndex, col].Style.Fill.BackgroundColor.SetColor(Color.FromArgb(255, 209, 26));
                                        }
                                        if (rowIndex == 2)
                                        {
                                            worksheet.Cells[1, col].Value = colProp.Name + "_" + WeekList[x];
                                            totalCols = totalCols + 1;
                                        }
                                        col++;
                                    }
                                    if (rowIndex == 2)
                                    {
                                        totalCols--;
                                    }
                                    col--;
                                }
                                else if (colProp.Name.Contains("Quarterly"))
                                {
                                    for (int x = 0; x < QuaterList.Count(); x++)
                                    {
                                        worksheet.Cells[rowIndex, col].Value = timeFrameData[x];
                                        if (rowIndex == 2)
                                        {
                                            worksheet.Cells[1, col].Value = colProp.Name + "_" + QuaterList[x];
                                            totalCols = totalCols + 1;
                                        }
                                        col++;
                                    }
                                    if (rowIndex == 2)
                                    {
                                        totalCols--;
                                    }
                                    col--;
                                }


                            }
                            else
                            {
                                worksheet.Cells[rowIndex, col].Value = dataValue;
                            }

                        }
                        col++;
                        columnCounter++;
                    }

                }
              
                rowIndex++;
            }
            var totalRows = data.Count();

            //Styling
            worksheet.Row(1).Style.Font.Bold = true;

            var modelTable = worksheet.Cells[1, 1, totalRows + 1, totalCols];
            // Assign borders
            modelTable.Style.Border.Top.Style = ExcelBorderStyle.Thin;
            modelTable.Style.Border.Left.Style = ExcelBorderStyle.Thin;
            modelTable.Style.Border.Right.Style = ExcelBorderStyle.Thin;
            modelTable.Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
            modelTable.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
            modelTable.Style.VerticalAlignment = ExcelVerticalAlignment.Center;
            worksheet.Cells[1, 1, 1, totalCols].Style.Fill.PatternType = ExcelFillStyle.Solid;
            worksheet.Cells[1, 1, 1, totalCols].Style.Fill.BackgroundColor.SetColor(Color.FromArgb(0, 150, 207));

            try
            {
                worksheet.Cells[worksheet.Dimension.Address].AutoFitColumns();
            }
            catch
            {

            }
            var headerRow = 1;
            foreach (var colorColumn in columnsToColor)
            {
                for (int j = 1; j <= totalCols; j++)
                {
                    if (worksheet.Cells[headerRow, j].Value?.ToString() == colorColumn.ColumnName)
                    {
                        var conditionsString = colorColumn.ConditionalFormat;
                        var conditions = conditionsString.Split(',').Select(s => new
                        {
                            condition = s.Split('_')[0],
                            formula = s.Split('_')[1],
                            color = s.Split('_')[2]
                        }).ToList();
                        ExcelRange rng = worksheet.Cells[headerRow + 1, j, totalRows + 1, j];
                        foreach (var condition in conditions)
                        {
                            var condFormat = worksheet.ConditionalFormatting.AddExpression(rng);
                            condFormat.Style.Fill.BackgroundColor.Color = Color.FromName(condition.color);
                            condFormat.Formula = new ExcelFormulaAddress(rng.Address) + condition.formula;
                        }


                    }
                }
            }
            if (groupData != null)
            {
                worksheet.InsertRow(1, 1);
                var startIndex = groupData.First().Key.Split(':')[0];
                var lastIndex = groupData.Last().Key.Split(':')[1];
                var destination = startIndex + ":" + lastIndex;
                var source = destination.Replace('1', '2');
                worksheet.Cells[source].Copy(worksheet.Cells[destination]);

                foreach (KeyValuePair<string, string> x in groupData)
                {
                    worksheet.Cells[x.Key].Merge = true;
                    worksheet.Cells[x.Key].Value = x.Value;

                }
            }
            return worksheet;
        }
        private bool CheckIfColRequiredBySetting(PropertyInfo colProp, CompanySetting[] companySettingsToCheck)
        {
            if(companySettings != null && companySettingsToCheck != null && companySettingsToCheck.Length > 0)
            {
                foreach (var companySetting in companySettingsToCheck)
                {
                    switch (companySetting)
                    {
                        case CompanySetting.UsesPositionCodes:
                            if (!companySettings.ContainsKey(companySetting.ToString()) || !(bool)companySettings[companySetting.ToString()])
                                return false;
                            break;
                        case CompanySetting.HighestPositionLevel:
                            PositionCodeLevel colPosLevel, highestPosLevel;
                            if (!companySettings.ContainsKey(companySetting.ToString()) || !Enum.TryParse("Level" + colProp.Name[1].ToString(), out colPosLevel) || !Enum.TryParse(companySettings[companySetting.ToString()].ToString(), out highestPosLevel) || highestPosLevel > colPosLevel)
                                return false;
                            break;
                        case CompanySetting.HighestGeoHierarchy:
                            GeographyLevel colGeoLevel, highestGeoLevel;
                            if (!companySettings.ContainsKey(companySetting.ToString()) || !Enum.TryParse(colProp.Name, out colGeoLevel) || !Enum.TryParse(companySettings[companySetting.ToString()].ToString(), out highestGeoLevel) || highestGeoLevel < colGeoLevel)
                                return false;
                            break;
                        case CompanySetting.UsesFAUnify:
                            if (!companySettings.ContainsKey(companySetting.ToString()) || !(bool)companySettings[companySetting.ToString()])
                                return false;
                            break;
                        case CompanySetting.NotApplicable:
                        default:
                            return true;
                    }
                }
            }
            return true;
        }
        public DataTable addDerivedColumns(DataTable dt, List<string> derivedkpis = null)
        {
            if (derivedkpis != null && derivedkpis.Count != 0)
            {
                foreach (var x in derivedkpis)
                {
                    dt.Columns.Add(x, typeof(string));
                }
                foreach (DataRow row in dt.Rows)
                {
                    var derivedColstring = (string)row["derivedMeasures"];
                    var derivedDic = JsonConvert.DeserializeObject<Dictionary<string, string>>(derivedColstring);
                    foreach (var x in derivedkpis)
                    {
                        row[x] = derivedDic[x];
                    }
                }
            }
            return dt;
        }
        private ExcelWorksheet UpdatePivotExcelSheet<T>(long companyId, ExcelWorksheet workSheet, IEnumerable<T> data, PivotColumn pivotColumn, string defaultGroupColumnName = null, Dictionary<string, string> conditionalFormattingDictionary = null, List<Dictionary<string, int>> sortingDictionaryList = null, Dictionary<string, string> groupDisplayTextDictionary = null, IEnumerable<T> horizontalSubgroup = null, string VerticalTotalColumn = null, bool useDoublePivot= false, string delimiter = "_", IEnumerable<string> columnsToDeleteFromPivot = null, bool forceNumberTypeInpivot = false, List<string> derivedkpis = null)
        {
            ListToDataTableConverter ConvertToDataTable = new ListToDataTableConverter();
            var dt = ConvertToDataTable.GetDataTableFromList(data, "tempName1");
            dt = addDerivedColumns(dt, derivedkpis);
            if (columnsToDeleteFromPivot != null && columnsToDeleteFromPivot.Count() > 0)
            {
                foreach (var col in columnsToDeleteFromPivot)
                {
                    dt.Columns.Remove(col);
                    pivotColumn.ValueColumns= (pivotColumn.ValueColumns).Where(v => v != col).ToArray();
                }
            }
            if (pivotColumn != null)
            {
                PivotCreater pivotCreater = new PivotCreater();
                SeparateTablesForPivotTable separateTablesForPivot = new SeparateTablesForPivotTable();
                var baseTableName = "FlatTable";
                JoinDatatable joinDatatable = new JoinDatatable();
                var dataset = new DataSet();
                if (useDoublePivot && horizontalSubgroup != null)
                {
                    var horizonSubgroupdt = ConvertToDataTable.GetDataTableFromList(horizontalSubgroup, "horizontalSubgroup");
                    horizonSubgroupdt = addDerivedColumns(horizonSubgroupdt, derivedkpis);
                    dataset = separateTablesForPivot.SeparateTables(dt,horizonSubgroupdt, pivotColumn, baseTableName);
                    dataset.Tables.Add(pivotCreater.DoublePivot(dataset.Tables[pivotColumn.PivotTableName], "Id", pivotColumn,
                      $"{pivotColumn.ParentColumn}", defaultGroupColumnName, sortingDictionaryList
                      ));
                }
                else 
                {
                    dataset = separateTablesForPivot.SeparateTables(dt, pivotColumn, baseTableName);
                    dataset.Tables.Add(pivotCreater.Pivot(dataset.Tables[pivotColumn.PivotTableName], "Id", pivotColumn,
                      $"{pivotColumn.ParentColumn}", defaultGroupColumnName, sortingDictionaryList, delimiter
                      ));
                }
                dt = joinDatatable.JoinDataTables(dataset, pivotColumn, baseTableName, forceConvertToNumbers: forceNumberTypeInpivot);  
            }
            if (useDoublePivot)
            {
                workSheet = UpdateWorkSheetDoublePivot<T>(companyId, dt, workSheet, conditionalFormattingDictionary, pivotColumn, groupDisplayTextDictionary, VerticalTotalColumn: VerticalTotalColumn, derivedkpis: derivedkpis);
            }
            else
            {
                workSheet = UpdateWorkSheet<T>(companyId, dt, workSheet, conditionalFormattingDictionary, pivotColumn, groupDisplayTextDictionary,VerticalTotalColumn: VerticalTotalColumn,delimiter: delimiter, derivedkpis : derivedkpis);
            }

            return workSheet;
        }
        public ExcelWorksheet UpdatePivotExcelSheet<T>(long companyId, ExcelWorksheet workSheet, DataTable dt, PivotColumn pivotColumn, string defaultGroupColumnName = null, Dictionary<string, string> conditionalFormattingDictionary = null, List<Dictionary<string, int>> sortingDictionaryList = null, Dictionary<string, string> groupDisplayTextDictionary = null)
        {
            if (pivotColumn != null)
            {
                PivotCreater pivotCreater = new PivotCreater();
                SeparateTablesForPivotTable separateTablesForPivot = new SeparateTablesForPivotTable();
                var baseTableName = "FlatTable";
                JoinDatatable joinDatatable = new JoinDatatable();
                var dataset = separateTablesForPivot.SeparateTables(dt, pivotColumn, baseTableName);
                dataset.Tables.Add(pivotCreater.Pivot(dataset.Tables[pivotColumn.PivotTableName], "Id", pivotColumn,
                      $"{pivotColumn.ParentColumn}", defaultGroupColumnName, sortingDictionaryList
                      ));
                dt = joinDatatable.JoinDataTables(dataset, pivotColumn, baseTableName);
            }

            workSheet = UpdateWorkSheet<T>(companyId, dt, workSheet, conditionalFormattingDictionary, pivotColumn, groupDisplayTextDictionary);

            return workSheet;
        }



        private ExcelWorksheet UpdateWorkSheet<T>(long companyId, DataTable data, ExcelWorksheet worksheet, Dictionary<string, string> conditionalFormattingDictionary = null, PivotColumn pivotColumns = null,Dictionary<string,string> groupDisplayTextDictionary=null, string VerticalTotalColumn = null, string delimiter = "_", List<string> derivedkpis = null)
        {
            var columns = typeof(T).GetProperties();
            if (columns == null && derivedkpis == null && derivedkpis.Count ==0)
            {
                return worksheet;
            }
            var setOrdinal = 0;
            data.PrimaryKey = null;
            var columnsToColor = new List<TableFieldAttribute>();
            var formatDictionary = new Dictionary<string, string>();
            foreach (var colProp in columns)
            {
                var Tattr = Attribute.GetCustomAttribute(colProp, typeof(TableFieldAttribute));
                if (Tattr is TableFieldAttribute excelAttrOfT)
                {
                    var columnname = excelAttrOfT.ColumnName;
                    //Deletes null columns
                    if (data.Columns.Contains(colProp.Name))
                    {
                        if (!_showAllColumns && (excelAttrOfT.ColumnRequirement == Requirement.HideIfNull && data.Rows.Cast<DataRow>().All(dr => string.IsNullOrEmpty(dr[colProp.Name].ToString()))))
                        {
                            data.Columns.Remove(colProp.Name);
                        }
                        else if (!_showAllColumns && (excelAttrOfT.ColumnRequirement == Requirement.HideIfZero && Convert.ToDouble(data.Compute("Max(" + colProp.Name + ")", string.Empty)) == 0))
                        {
                            data.Columns.Remove(colProp.Name);
                        }
                        else if (!_showAllColumns && (excelAttrOfT.ColumnRequirement == Requirement.SpecificSettingBased) && !CheckIfColRequiredBySetting(colProp, excelAttrOfT.CompanySettingsToCheck))
                        {
                            data.Columns.Remove(colProp.Name);
                        }
                        else
                        {
                            data.Columns[colProp.Name].SetOrdinal(setOrdinal);
                                                     
                            if (_useNomenclature && excelAttrOfT.NomenclatureRequirement == true)
                            {
                                var i = 0;
                                var listforheader = columnname.Split(' ').ToList();
                                var listforNewHeader = columnname.Split(' ').ToList();
                                foreach (var item in listforheader)
                                {
                                    var nomenclaturename = nomenclatureSpecifier.GetHeaderName(item);
                                    listforNewHeader[i] = nomenclaturename;
                                    i++;
                                }
                                columnname = String.Join(" ", listforNewHeader);
                                if (colProp.Name == VerticalTotalColumn)
                                {
                                    VerticalTotalColumn = columnname;
                                    if (colProp.Name != columnname && data.Columns.Contains(columnname))
                                    {
                                        VerticalTotalColumn = "$$" + columnname;
                                    }
                                }
                                
                            }
                            if (colProp.Name != columnname && data.Columns.Contains(columnname))
                            {
                                data.Columns[colProp.Name].ColumnName = "$$"+columnname;
                            }
                            else 
                            {
                                data.Columns[colProp.Name].ColumnName = columnname;
                            }
                            setOrdinal++;
                        }
                        if (!string.IsNullOrWhiteSpace(excelAttrOfT.ConditionalMatchColumn))
                        {
                            columnsToColor.Add(excelAttrOfT);
                        }
                        if (!string.IsNullOrWhiteSpace(excelAttrOfT.ConditionalFormat))
                        {
                            columnsToColor.Add(excelAttrOfT);
                        }
                        //Date: oct 18 2021; Asana: https://app.asana.com/0/0/1201216060291345/f; change: add check before adding to dictionary
                        if (!string.IsNullOrWhiteSpace(excelAttrOfT.CellFormat) && !formatDictionary.ContainsKey(columnname))
                        {
                            formatDictionary.Add(columnname,excelAttrOfT.CellFormat);
                        }
                        
                    }
                }
                else
                {
                    if (data.Columns.Contains(colProp.Name))
                    {
                        data.Columns.Remove(colProp.Name);
                    }
                }
            }

            string pivotStartColumn = null;
            var categories = new List<string>();
            if (pivotColumns != null && pivotColumns.ValueColumns != null && pivotColumns.ValueColumns.Count() > 0)
            {
                var pivotDataColumns = data.Columns.Cast<DataColumn>().Where(c => pivotColumns.ValueColumns.Any(i => c.ColumnName.Contains(delimiter + i))).ToList();
                if (pivotDataColumns.Count > 0)
                {
                    //Date: 08-11-2021
                    //Link: https://app.asana.com/0/401576460077807/1201309234234375/f
                    // Reason: Fixing that primary category complete name not appearing in case when primary category contains an underscore

                    categories = pivotDataColumns.Select(c => c.ColumnName.Substring(0,c.ColumnName.LastIndexOf(delimiter))).Distinct().ToList();
                    if (delimiter != "_") // in case of flexible reports
                    {
                        switch (pivotColumns.ParentColumn)
                        {
                            case "Month":
                                categories = categories.OrderBy(s => DateTime.ParseExact(s, "MMMM", new CultureInfo("en-US"))).ToList();
                                break;
                            case "Week":
                                List<string> months = new List<string> { "Week_1", "Week_2", "Week_3", "Week_4", "Week_5", "Week_6" };
                                categories = months.Where(m => categories.Any(o => o == m)).ToList();
                                break;
                            case "Date":
                                categories = categories.OrderBy(s => DateTime.ParseExact(s, "dd/MM/yyyy", new CultureInfo("en-US"))).ToList();
                                break;
                            default:
                                break;
                        }
                    }              
                    var columnPosition = pivotDataColumns.First().Ordinal;
                    var pivotStartPosition = columnPosition;
                    var columnOrdinalDictionary = new Dictionary<int, int>();
                    foreach (var category in categories)
                    {
                        foreach (var valueColumn in pivotColumns.ValueColumns)
                        {
                            if (data.Columns.Contains(category + delimiter + valueColumn))
                            {
                                var column = data.Columns[category + delimiter + valueColumn];
                                column.SetOrdinal(columnPosition++);
                            }
                        }
                    }
                    pivotStartColumn = data.Columns[pivotStartPosition].ColumnName;
                }
            }

            //Styling
            var headerRow = 1;
            worksheet.Row(headerRow).Style.Font.Bold = true;
            var totalRows = data.Rows.Count;
            var totalCols = data.Columns.Count;
            //Create Excel from DataTable
            worksheet.Cells[headerRow, 1].LoadFromDataTable(data, true);
            foreach (var colorColumn in columnsToColor)
            {
                for (int i = 1; i < totalCols; i++)
                {
                    if (worksheet.Cells[headerRow, i].Value?.ToString() == colorColumn.ColumnName)
                    {
                        for (int j = 1; j < totalCols; j++)
                        {
                            if (worksheet.Cells[headerRow, j]?.Value.ToString() == colorColumn.ConditionalMatchColumn)
                            {
                                string statement = $"=AND({worksheet.Cells[headerRow + 1, j].Address}<>\"\",{worksheet.Cells[headerRow + 1, j].Address}<>{worksheet.Cells[headerRow + 1, i].Address})";
                                var condFormat = worksheet.ConditionalFormatting.AddExpression(worksheet.Cells[headerRow + 1, i, totalRows + 1, i]);
                                condFormat.Formula = statement;
                                condFormat.Style.Font.Color.Color = Color.Red;
                            }
                        }
                    }
                }
            }

            foreach (var colorColumn in columnsToColor)
            {
                for (int i = 1; i <= totalCols; i++)
                {
                    if (worksheet.Cells[headerRow, i].Value?.ToString() == colorColumn.ColumnName)
                    {
                        var conditionsString = colorColumn.ConditionalFormat;
                        if (conditionsString != null)
                        {
                            var conditions = conditionsString.Split(',').Select(s => new
                            {
                                condition = s.Split('_')[0],
                                formula = s.Split('_')[1],
                                color = s.Split('_')[2]
                            }).ToList();

                            ExcelRange rng = worksheet.Cells[headerRow + 1, i, totalRows + headerRow, i];
                            foreach (var condition in conditions)
                            {
                                var condFormat = worksheet.ConditionalFormatting.AddExpression(rng);
                                condFormat.Style.Fill.BackgroundColor.Color = Color.FromName(condition.color);
                                condFormat.Formula = new ExcelFormulaAddress(rng.Address) + condition.formula;

                            }
                        }

                    }
                }
            }

            if (conditionalFormattingDictionary != null && conditionalFormattingDictionary.Count() > 0)
            {

                for (int i = 1; i <= totalCols; i++)
                {
                    if (conditionalFormattingDictionary.ContainsKey(worksheet.Cells[headerRow, i].Value?.ToString()))
                    {
                        var conditionsString = conditionalFormattingDictionary[worksheet.Cells[headerRow, i].Value?.ToString()];
                        var conditions = conditionsString.Split(',').Select(s => new
                        {
                            condition = s.Split('_')[0],
                            formula = s.Split('_')[1],
                            color = s.Split('_')[2]
                        }).ToList();

                        ExcelRange rng = worksheet.Cells[headerRow + 1, i, totalRows + headerRow, i];
                        var step = pivotColumns.ValueColumns.Count();
                        if (step > 1)
                        {
                            rng = worksheet.Cells[headerRow + 2, i, totalRows + headerRow + 1, i];
                        }
                        foreach (var condition in conditions)
                        {
                            var multipleFormula = condition.formula.Split(':').ToList();
                            var condFormat = worksheet.ConditionalFormatting.AddExpression(rng);
                            condFormat.Style.Fill.BackgroundColor.Color = Color.FromName(condition.color);
                            if (multipleFormula.Count > 1)
                            {
                                condFormat.Formula = $"=AND(({new ExcelFormulaAddress(rng.Address)}  {multipleFormula[0]}),({new ExcelFormulaAddress(rng.Address)}  {multipleFormula[1]}))";
                            }
                            else
                            {
                                condFormat.Formula = new ExcelFormulaAddress(rng.Address) + condition.formula;
                            }
                        }


                    }
                }
            }
            // coloring total rows

            if (VerticalTotalColumn != null && VerticalTotalColumn != "")
            {
                int count = 1;
                foreach (DataRow dr in data.Rows)
                {
                    //var isTotalRow = dr["Id"];
                    string name = dr[VerticalTotalColumn].ToString();
                    if (name == "Total")
                    {
                        worksheet.Cells[count + 1, 1, count + 1, totalCols].Style.Fill.PatternType = ExcelFillStyle.Solid;
                        worksheet.Cells[count + 1, 1, count + 1, totalCols].Style.Fill.BackgroundColor.SetColor(Color.LightGray);
                        worksheet.Cells[count + 1, 1, count + 1, totalCols].Style.Font.Bold = true;
                    }
                    count++;
                }
            }

            var modelTable = worksheet.Cells[headerRow, 1, totalRows + headerRow, totalCols];
            // Assign borders
            modelTable.Style.Border.Top.Style = ExcelBorderStyle.Thin;
            modelTable.Style.Border.Left.Style = ExcelBorderStyle.Thin;
            modelTable.Style.Border.Right.Style = ExcelBorderStyle.Thin;
            modelTable.Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
            modelTable.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
            modelTable.Style.VerticalAlignment = ExcelVerticalAlignment.Center;
            worksheet.Cells[headerRow, 1, headerRow, totalCols].Style.Fill.PatternType = ExcelFillStyle.Solid;
            worksheet.Cells[headerRow, 1, headerRow, totalCols].Style.Fill.BackgroundColor.SetColor(Color.FromArgb(0, 150, 207));
           if(groupDisplayTextDictionary  == null)
            {
                groupDisplayTextDictionary = new Dictionary<string, string>();
            }

            //Reset Table For Pivot
            if (pivotStartColumn != null)
            {
                int i = 1;
                for (; i < totalCols; i++)
                {
                    if (worksheet.Cells[1, i].Value?.ToString() == pivotStartColumn)
                    {
                        break;
                    }
                    else
                    {
                        if (formatDictionary.ContainsKey(worksheet.Cells[1, i].Value?.ToString()))
                        {
                            worksheet.Column(i).Style.Numberformat.Format = formatDictionary[worksheet.Cells[1, i].Value?.ToString()];
                        }
                    }
                }
                var step = pivotColumns.ValueColumns.Count();
                if (step > 1)
                {
                    worksheet.InsertRow(headerRow++, 1);
                    foreach (var category in categories)
                    {
                        worksheet.Cells[1, i].Value = groupDisplayTextDictionary.ContainsKey(category) ? groupDisplayTextDictionary[category] : category;
                        worksheet.Cells[1, i].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                        var mergedColumn = worksheet.Cells[1, i, 1, i + step - 1];
                        mergedColumn.Merge = true;
                        mergedColumn.Style.Fill.PatternType = ExcelFillStyle.Solid;
                        mergedColumn.Style.Fill.BackgroundColor.SetColor(Color.LightGray);
                        mergedColumn.Style.Border.Top.Style = ExcelBorderStyle.Thin;
                        mergedColumn.Style.Border.Left.Style = ExcelBorderStyle.Thin;
                        mergedColumn.Style.Border.Right.Style = ExcelBorderStyle.Thin;
                        mergedColumn.Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                        mergedColumn.Style.Font.Bold = true;

                        foreach (var item in pivotColumns.ValueColumns)
                        {
                            var colName = item;
                            string format = string.Empty;
                            foreach (var colPro in columns)
                            {
                                var Tattr = Attribute.GetCustomAttribute(colPro, typeof(TableFieldAttribute));
                                if (Tattr is TableFieldAttribute excelAttrOfT)
                                {
                                    if (item == colPro.Name)
                                    {
                                        colName = excelAttrOfT.ColumnName;
                                        format = excelAttrOfT.CellFormat;
                                        var j = 0;
                                        var listforheader = colName.Split(' ').ToList();
                                        var listforNewHeader = colName.Split(' ').ToList();
                                        foreach (var item2 in listforheader)
                                        {
                                            var nomenclaturename = nomenclatureSpecifier.GetHeaderName(item2);
                                            listforNewHeader[j] = nomenclaturename;
                                            j++;
                                        }
                                        colName = String.Join(" ", listforNewHeader);
                                        
                                    }
                                }
                            }
                            worksheet.Cells[2, i].Value = colName;
                            if (!string.IsNullOrEmpty(format))
                            {
                                worksheet.Column(i).Style.Numberformat.Format = format;
                            }
                            i++;

                        }
                    }
                }
                else
                {                  
                        foreach (var category in categories)
                        {
                            worksheet.Cells[1, i++].Value = groupDisplayTextDictionary.ContainsKey(category) ? groupDisplayTextDictionary[category]:category;
                        }
                }

            }
            //Remove _ used in duplicate name in datatable
            for (int i = 1; i < totalCols; i++)
            {
                var step = pivotColumns.ValueColumns.Count();
                if (step > 1)
                {
                    var tempName = worksheet.Cells[2, i].Value?.ToString();
                    worksheet.Cells[2, i].Value = tempName?.Replace("$$", string.Empty);
                }
                if (worksheet.Cells[1, i].Value != null)
                {
                    var tempName = worksheet.Cells[1, i].Value.ToString();
                    worksheet.Cells[1, i].Value = tempName.Replace("$$", string.Empty);
                }              
            }
            worksheet.Cells[worksheet.Dimension.Address].Style.QuotePrefix = false;
            try
            {
                worksheet.Cells[worksheet.Dimension.Address].AutoFitColumns();
            }
            catch
            {
                return worksheet;
            }
            return worksheet;
        }
        private ExcelWorksheet UpdateWorkSheetDoublePivot<T>(long companyId, DataTable data, ExcelWorksheet worksheet, Dictionary<string, string> conditionalFormattingDictionary = null, PivotColumn pivotColumns = null, Dictionary<string, string> groupDisplayTextDictionary = null, string VerticalTotalColumn = null,List<string> derivedkpis =null)
        {
            var columns = typeof(T).GetProperties();
            if (columns == null && derivedkpis == null && derivedkpis.Count == 0)
            {
                return worksheet;
            }
            var setOrdinal = 0;
            data.PrimaryKey = null;
            var columnsToColor = new List<TableFieldAttribute>();
            var formatDictionary = new Dictionary<string, string>();
            foreach (var colProp in columns)
            {
                var Tattr = Attribute.GetCustomAttribute(colProp, typeof(TableFieldAttribute));
                if (Tattr is TableFieldAttribute excelAttrOfT)
                {
                    var columnname = excelAttrOfT.ColumnName;
                    //Deletes null columns
                    if (data.Columns.Contains(colProp.Name))
                    {

                        if (!_showAllColumns && (excelAttrOfT.ColumnRequirement == Requirement.HideIfNull && data.Rows.Cast<DataRow>().All(dr => string.IsNullOrEmpty(dr[colProp.Name].ToString()))))
                        {
                            data.Columns.Remove(colProp.Name);
                        }
                        else if (!_showAllColumns && (excelAttrOfT.ColumnRequirement == Requirement.HideIfZero && Convert.ToDouble(data.Compute("Max(" + colProp.Name + ")", string.Empty)) == 0))
                        {
                            data.Columns.Remove(colProp.Name);
                        }
                        else if (!_showAllColumns && (excelAttrOfT.ColumnRequirement == Requirement.SpecificSettingBased) && !CheckIfColRequiredBySetting(colProp, excelAttrOfT.CompanySettingsToCheck))
                        {
                            data.Columns.Remove(colProp.Name);
                        }
                        else
                        {
                            data.Columns[colProp.Name].SetOrdinal(setOrdinal);

                            if (_useNomenclature && excelAttrOfT.NomenclatureRequirement == true)
                            {
                                var i = 0;
                                var listforheader = columnname.Split(' ').ToList();
                                var listforNewHeader = columnname.Split(' ').ToList();
                                foreach (var item in listforheader)
                                {
                                    var nomenclaturename = nomenclatureSpecifier.GetHeaderName(item);
                                    listforNewHeader[i] = nomenclaturename;
                                    i++;
                                }
                                columnname = String.Join(" ", listforNewHeader);
                                if (VerticalTotalColumn == colProp.Name)
                                { VerticalTotalColumn = columnname;
                                    if (colProp.Name != columnname && data.Columns.Contains(columnname))
                                    {
                                        VerticalTotalColumn = "$$" + columnname;
                                    }
                                }
                            }
                            if (colProp.Name != columnname && data.Columns.Contains(columnname))
                            {
                                data.Columns[colProp.Name].ColumnName = "$$" + columnname;
                            }
                            else
                            {
                                data.Columns[colProp.Name].ColumnName = columnname;
                            }
                            setOrdinal++;
                        }
                        if (!string.IsNullOrWhiteSpace(excelAttrOfT.ConditionalMatchColumn))
                        {
                            columnsToColor.Add(excelAttrOfT);
                        }
                        if (!string.IsNullOrWhiteSpace(excelAttrOfT.ConditionalFormat))
                        {
                            columnsToColor.Add(excelAttrOfT);
                        }
                        if (!string.IsNullOrWhiteSpace(excelAttrOfT.CellFormat) && !formatDictionary.ContainsKey(columnname))
                        {
                            formatDictionary.Add(columnname, excelAttrOfT.CellFormat);
                        }

                    }
                }
                else
                {
                    if (data.Columns.Contains(colProp.Name))
                    {
                        data.Columns.Remove(colProp.Name);
                    }
                }
            }

            string pivotStartColumn = null;
            var Secondarycategories = new List<string>();
            var categories = new List<string>();
            if (pivotColumns != null && pivotColumns.ValueColumns != null && pivotColumns.ValueColumns.Count() > 0)
            {
                var pivotDataColumns = data.Columns.Cast<DataColumn>().Where(c => pivotColumns.ValueColumns.Any(i => c.ColumnName.Contains("+" + i))).ToList();
                if (pivotDataColumns.Count > 0)
                {
                    Secondarycategories = pivotDataColumns.Select(c => c.ColumnName.Split('+')[0]).Distinct().ToList();
                    categories = pivotDataColumns.Select(c => c.ColumnName.Split('+')[1]).Distinct().ToList();
                    Secondarycategories.Remove("Total");
                    switch (pivotColumns.SecondaryPivotColumn)
                    {
                        case "Month":
                            Secondarycategories = Secondarycategories.OrderBy(s => DateTime.ParseExact(s, "MMMM", new CultureInfo("en-US"))).ToList();
                            break;
                        case "Week":
                            List<string> months = new List<string> { "Week_1", "Week_2", "Week_3", "Week_4", "Week_5", "Week_6" };
                            Secondarycategories = months.Where(m => Secondarycategories.Any(o => o == m)).ToList();
                            break;
                        case "Date":
                            Secondarycategories = Secondarycategories.OrderBy(s => DateTime.ParseExact(s, "dd/MM/yyyy", new CultureInfo("en-US"))).ToList();
                            break;
                    }
                    Secondarycategories.Add("Total");
                    var columnPosition = pivotDataColumns.First().Ordinal;
                    var pivotStartPosition = columnPosition;
                    var columnOrdinalDictionary = new Dictionary<int, int>();
                    data.CaseSensitive = true;
                    var columnsWithValue = data.Columns.Cast<DataColumn>().Select(x => x.ColumnName).ToArray();
                    foreach (var Secondarycategory in Secondarycategories)
                    {
                        foreach (var category in categories)
                        {
                            foreach (var valueColumn in pivotColumns.ValueColumns)
                            {
                                var columName = Secondarycategory + pivotColumns.PivotLevel2ColumnSeperator + category + pivotColumns.PivotLevel2ColumnSeperator + valueColumn;
                                if (!columnsWithValue.Contains(columName))
                                {
                                    data.Columns.Add(columName, valueColumn.GetType());
                                }
                                if (data.Columns.Contains(columName))
                                {
                                    var column = data.Columns[columName];
                                    column.SetOrdinal(columnPosition++);
                                }
                            }
                        }
                    }
                    pivotStartColumn = data.Columns[pivotStartPosition].ColumnName;
                }
            }

            //Styling
            var headerRow = 1;
            worksheet.Row(headerRow).Style.Font.Bold = true;
            var totalRows = data.Rows.Count;
            var totalCols = data.Columns.Count;
            //Create Excel from DataTable
            worksheet.Cells[headerRow, 1].LoadFromDataTable(data, true);
            foreach (var colorColumn in columnsToColor)
            {
                for (int i = 1; i < totalCols; i++)
                {
                    if (worksheet.Cells[headerRow, i].Value?.ToString() == colorColumn.ColumnName)
                    {
                        for (int j = 1; j < totalCols; j++)
                        {
                            if (worksheet.Cells[headerRow, j]?.Value.ToString() == colorColumn.ConditionalMatchColumn)
                            {
                                string statement = $"=AND({worksheet.Cells[headerRow + 1, j].Address}<>\"\",{worksheet.Cells[headerRow + 1, j].Address}<>{worksheet.Cells[headerRow + 1, i].Address})";
                                var condFormat = worksheet.ConditionalFormatting.AddExpression(worksheet.Cells[headerRow + 1, i, totalRows + 1, i]);
                                condFormat.Formula = statement;
                                condFormat.Style.Font.Color.Color = Color.Red;
                            }
                        }
                    }
                }
            }

            foreach (var colorColumn in columnsToColor)
            {
                for (int i = 1; i <= totalCols; i++)
                {
                    if (worksheet.Cells[headerRow, i].Value?.ToString() == colorColumn.ColumnName)
                    {
                        var conditionsString = colorColumn.ConditionalFormat;
                        if (conditionsString != null)
                        {
                            var conditions = conditionsString.Split(',').Select(s => new
                            {
                                condition = s.Split('_')[0],
                                formula = s.Split('_')[1],
                                color = s.Split('_')[2]
                            }).ToList();

                            ExcelRange rng = worksheet.Cells[headerRow + 1, i, totalRows + headerRow, i];
                            foreach (var condition in conditions)
                            {
                                var condFormat = worksheet.ConditionalFormatting.AddExpression(rng);
                                condFormat.Style.Fill.BackgroundColor.Color = Color.FromName(condition.color);
                                condFormat.Formula = new ExcelFormulaAddress(rng.Address) + condition.formula;

                            }
                        }

                    }
                }
            }

            if (conditionalFormattingDictionary != null && conditionalFormattingDictionary.Count() > 0)
            {

                for (int i = 1; i <= totalCols; i++)
                {
                    if (conditionalFormattingDictionary.ContainsKey(worksheet.Cells[headerRow, i].Value?.ToString()))
                    {
                        var conditionsString = conditionalFormattingDictionary[worksheet.Cells[headerRow, i].Value?.ToString()];
                        var conditions = conditionsString.Split(',').Select(s => new
                        {
                            condition = s.Split('_')[0],
                            formula = s.Split('_')[1],
                            color = s.Split('_')[2]
                        }).ToList();

                        ExcelRange rng = worksheet.Cells[headerRow + 1, i, totalRows + headerRow, i];
                        var step = pivotColumns.ValueColumns.Count();
                        if (step > 1)
                        {
                            rng = worksheet.Cells[headerRow + 2, i, totalRows + headerRow + 1, i];
                        }
                        foreach (var condition in conditions)
                        {
                            var multipleFormula = condition.formula.Split(':').ToList();
                            var condFormat = worksheet.ConditionalFormatting.AddExpression(rng);
                            condFormat.Style.Fill.BackgroundColor.Color = Color.FromName(condition.color);
                            if (multipleFormula.Count > 1)
                            {
                                condFormat.Formula = $"=AND(({new ExcelFormulaAddress(rng.Address)}  {multipleFormula[0]}),({new ExcelFormulaAddress(rng.Address)}  {multipleFormula[1]}))";
                            }
                            else
                            {
                                condFormat.Formula = new ExcelFormulaAddress(rng.Address) + condition.formula;
                            }
                        }


                    }
                }
            }
            // coloring vertical Subgrouping rows


            int count = 1;
            foreach (DataRow dr in data.Rows)
            {
                var HorizontalTotalColumn = (pivotColumns.ValueColumns.Count() * categories.Count());
                if (VerticalTotalColumn != null && VerticalTotalColumn != "")
                {
                    string name = dr[VerticalTotalColumn].ToString();
                    if (name == "Total")
                    {
                        worksheet.Cells[count + 1, 1, count + 1, totalCols].Style.Fill.PatternType = ExcelFillStyle.Solid;
                        worksheet.Cells[count + 1, 1, count + 1, totalCols].Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
                        //worksheet.Row(count + 1).Style.Font.Bold = true;
                        worksheet.Cells[count + 1, 1, count + 1, totalCols - HorizontalTotalColumn].Style.Font.Bold = true;
                    }
                }

                
                worksheet.Cells[count + 1, totalCols - HorizontalTotalColumn + 1, count + 1, totalCols].Style.Fill.PatternType = ExcelFillStyle.Solid;
                worksheet.Cells[count + 1, totalCols - HorizontalTotalColumn + 1, count + 1, totalCols].Style.Fill.BackgroundColor.SetColor(Color.FromArgb(255, 209, 26));
                count++;
            }

            



            var modelTable = worksheet.Cells[headerRow, 1, totalRows + headerRow, totalCols];
            // Assign borders
            modelTable.Style.Border.Top.Style = ExcelBorderStyle.Thin;
            modelTable.Style.Border.Left.Style = ExcelBorderStyle.Thin;
            modelTable.Style.Border.Right.Style = ExcelBorderStyle.Thin;
            modelTable.Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
            modelTable.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
            modelTable.Style.VerticalAlignment = ExcelVerticalAlignment.Center;
            worksheet.Cells[headerRow, 1, headerRow, totalCols].Style.Fill.PatternType = ExcelFillStyle.Solid;
            worksheet.Cells[headerRow, 1, headerRow, totalCols].Style.Fill.BackgroundColor.SetColor(Color.FromArgb(0, 150, 207));
            if (groupDisplayTextDictionary == null)
            {
                groupDisplayTextDictionary = new Dictionary<string, string>();
            }

            //Reset Table For Pivot
            if (pivotStartColumn != null)
            {
                int i = 1;
                for (; i < totalCols; i++)
                {
                    if (worksheet.Cells[1, i].Value?.ToString() == pivotStartColumn)
                    {
                        break;
                    }
                    else
                    {
                        if (formatDictionary.ContainsKey(worksheet.Cells[1, i].Value?.ToString()))
                        {
                            worksheet.Column(i).Style.Numberformat.Format = formatDictionary[worksheet.Cells[1, i].Value?.ToString()];
                        }
                    }
                }
                var step = pivotColumns.ValueColumns.Count();
                var step2 = categories.Count();
                worksheet.InsertRow(headerRow++, 2);
                foreach (var Secondarycategory in Secondarycategories)
                {
                    worksheet.Cells[1, i].Value = groupDisplayTextDictionary.ContainsKey(Secondarycategory) ? groupDisplayTextDictionary[Secondarycategory] : Secondarycategory;
                    worksheet.Cells[1, i].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                    var mergedColumn = worksheet.Cells[1, i, 1, i + (step2 * step) - 1];
                    mergedColumn.Merge = true;
                    mergedColumn.Style.Font.Bold = true;
                    mergedColumn.Style.Fill.PatternType = ExcelFillStyle.Solid;
                    mergedColumn.Style.Fill.BackgroundColor.SetColor(Color.LightSlateGray);
                    mergedColumn.Style.Border.Top.Style = ExcelBorderStyle.Thick;
                    mergedColumn.Style.Border.Left.Style = ExcelBorderStyle.Thin;
                    mergedColumn.Style.Border.Right.Style = ExcelBorderStyle.Thick;
                    mergedColumn.Style.Border.Bottom.Style = ExcelBorderStyle.Thin;

                       
                    foreach (var category in categories)
                    {
                        worksheet.Cells[2, i].Value = groupDisplayTextDictionary.ContainsKey(category) ? groupDisplayTextDictionary[category] : category;
                        worksheet.Cells[2, i].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                        mergedColumn = worksheet.Cells[2, i, 2, i + step - 1];
                        mergedColumn.Merge = true;
                        mergedColumn.Style.Font.Bold = true;
                        mergedColumn.Style.Fill.PatternType = ExcelFillStyle.Solid;
                        mergedColumn.Style.Fill.BackgroundColor.SetColor(Color.LightGray);
                        mergedColumn.Style.Border.Top.Style = ExcelBorderStyle.Thin;
                        mergedColumn.Style.Border.Left.Style = ExcelBorderStyle.Thick;
                        mergedColumn.Style.Border.Right.Style = ExcelBorderStyle.Thick;
                        mergedColumn.Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                        var PivotColumn = category;
                        string format = string.Empty;
                        foreach (var item in pivotColumns.ValueColumns)
                        {
                            var colName = item;
                            //string format = string.Empty;
                            foreach (var colPro in columns)
                            {
                                var Tattr = Attribute.GetCustomAttribute(colPro, typeof(TableFieldAttribute));
                                if (Tattr is TableFieldAttribute excelAttrOfT)
                                {
                                    if (item == colPro.Name)
                                    {
                                        colName = excelAttrOfT.ColumnName;
                                        format = excelAttrOfT.CellFormat;
                                        var j = 0;
                                        var listforheader = colName.Split(' ').ToList();
                                        var listforNewHeader = colName.Split(' ').ToList();
                                        foreach (var item2 in listforheader)
                                        {
                                            var nomenclaturename = nomenclatureSpecifier.GetHeaderName(item2);
                                            listforNewHeader[j] = nomenclaturename;
                                            j++;
                                        }
                                        colName = String.Join(" ", listforNewHeader);

                                    }
                                }
                            }
                            worksheet.Cells[3, i].Value = colName;
                            if (!string.IsNullOrEmpty(format))
                            {
                                worksheet.Column(i).Style.Numberformat.Format = format;
                            }
                            i++;
                        }
                    }
                }

            }
            //Remove _ used in duplicate name in datatable
            for (int i = 1; i < totalCols; i++)
            {
                var step = pivotColumns.ValueColumns.Count();
                if (step > 1)
                {
                    var tempName = worksheet.Cells[3, i].Value?.ToString();
                    worksheet.Cells[3, i].Value = tempName?.Replace("$$", string.Empty);
                }
                if (worksheet.Cells[2, i].Value != null)
                {
                    var tempName = worksheet.Cells[2, i].Value.ToString();
                    worksheet.Cells[2, i].Value = tempName.Replace("$$", string.Empty);
                }
            }
            worksheet.Cells[worksheet.Dimension.Address].Style.QuotePrefix = false;
            try
            {
                worksheet.Cells[worksheet.Dimension.Address].AutoFitColumns();
            }
            catch
            {
                return worksheet;
            }
            return worksheet;
        }

        private bool CheckIfColZero<T>(PropertyInfo colProp, IEnumerable<T> listOfData)
        {
            decimal value;
            double value2;
            foreach (var item in listOfData)
            {
                
                if (colProp.GetValue(item) != null && Decimal.TryParse(colProp.GetValue(item).ToString(), out value) && value > 0)
                {
                    return false;
                }
                else if(colProp.GetValue(item) != null && Double.TryParse(colProp.GetValue(item).ToString(), out value2) && value2 > 0)
                {
                    return false;
                }
            }
            return true;
        }

        private bool CheckIfColNull<T>(PropertyInfo colProp, IEnumerable<T> listOfData)
        {

            foreach (var item in listOfData)
            {
                if (colProp.GetValue(item) != null && !string.IsNullOrWhiteSpace(colProp.GetValue(item).ToString()))
                {
                    return false;
                }

            }
            return true;
        }
        public ExcelWorksheet CreateExcelForErrorAndNoData(ExcelWorksheet worksheet, string errorMessage)
        {
            worksheet.Cells[1, 1].LoadFromText(errorMessage);
            return worksheet;
        }
        public ExcelPackage MakeExcelForError(Stream stream, string sheetName, string errorMessage)
        {
            var data = new { Message = errorMessage };
            MakeExcelToStream(0, stream, new string[] { sheetName }, new List<ErrorMessage> { new ErrorMessage { Message = errorMessage } });
            return new ExcelPackage();
        }

        public Dictionary<int, bool> CreateExcelColumnWise<T>(IEnumerable<T> data, ExcelWorksheet worksheet, Dictionary<int, bool> showDataForHeader, int startrowIndex = 2, bool datafetchedInParts = false)
        {

            int col = 1;
            var allColumns = data.FirstOrDefault()?.GetType().GetProperties().Where(p => Attribute.GetCustomAttribute(p, typeof(TableFieldAttribute)) != null);
            var columns = selectedCols != null && selectedCols.Count > 0 ? allColumns.Where(p => selectedCols.ContainsKey(p.Name)) : allColumns;

            if (columns == null)
            {
                return showDataForHeader;
            }
            int i = 0;
            bool colGroupColoring = false;
            var prevgroup = ColGroupType.None;
            bool isPrevGroupColored = false;
            var columnsToColor = new List<TableFieldAttribute>();
            TableHelpers tableHelpers = new TableHelpers();

            if (startrowIndex == 2)
            {
                foreach (var colProp in columns)
                {
                    var Tattr = Attribute.GetCustomAttribute(colProp, typeof(TableFieldAttribute));
                    if (Tattr is TableFieldAttribute excelAttrOfT)
                    {

                        var Tcolumnname = excelAttrOfT.ColumnName;
                        if (_showAllColumns || (excelAttrOfT.ColumnRequirement != Requirement.HideIfNull || (!CheckIfColNull(colProp, data)) || (CheckIfColNull(colProp, data) && datafetchedInParts)))
                        {
                            if (excelAttrOfT.ColumnRequirement == Requirement.SpecificSettingBased && !CheckIfColRequiredBySetting(colProp, excelAttrOfT.CompanySettingsToCheck))
                            {
                                showDataForHeader.Add(i++, false);
                            }
                            else if (!_showAllColumns && excelAttrOfT.ColumnRequirement == Requirement.HideIfZero && CheckIfColZero(colProp, data))
                            {
                                showDataForHeader.Add(i++, false);
                            }
                            else if (excelAttrOfT.ColumnRequirement == Requirement.Selected && !selectedCols.ContainsKey(colProp.Name))
                            {
                                showDataForHeader.Add(i++, false);
                            }
                            else
                            {
                                if (!string.IsNullOrWhiteSpace(excelAttrOfT.CellFormat))
                                {
                                    worksheet.Column(col).Style.Numberformat.Format = excelAttrOfT.CellFormat;
                                }
                                if (excelAttrOfT.ColGroupType != ColGroupType.None)
                                {
                                    if (prevgroup == excelAttrOfT.ColGroupType)
                                    {
                                        if (isPrevGroupColored)
                                        {
                                            worksheet.Column(col).Style.Fill.PatternType = ExcelFillStyle.Solid;
                                            worksheet.Column(col).Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
                                            isPrevGroupColored = true;
                                        }
                                    }
                                    else
                                    {
                                        if (!isPrevGroupColored)
                                        {
                                            worksheet.Column(col).Style.Fill.PatternType = ExcelFillStyle.Solid;
                                            worksheet.Column(col).Style.Fill.BackgroundColor.SetColor(Color.LightGray);
                                            isPrevGroupColored = true;
                                        }
                                        else
                                        {
                                            isPrevGroupColored = false;
                                        }

                                    }
                                    prevgroup = excelAttrOfT.ColGroupType;
                                    colGroupColoring = true;
                                }
                                else
                                {
                                    prevgroup = ColGroupType.None;
                                    isPrevGroupColored = false;
                                }

                                if (_useNomenclature && excelAttrOfT.NomenclatureRequirement == true)
                                {
                                    if (_useNomenclature && excelAttrOfT.NomenclatureRequirement == true)
                                    {
                                        var j = 0;
                                        var listforheader = Tcolumnname.Split(' ').ToList();
                                        var listforNewHeader = Tcolumnname.Split(' ').ToList();
                                        foreach (var item in listforheader)
                                        {
                                            var nomenclaturename = nomenclatureSpecifier.GetHeaderName(item);
                                            listforNewHeader[j] = nomenclaturename;
                                            j++;
                                        }
                                        Tcolumnname = String.Join(" ", listforNewHeader);

                                    }
                                }
                                if (!string.IsNullOrWhiteSpace(excelAttrOfT.ConditionalFormat))
                                {
                                    columnsToColor.Add(excelAttrOfT);
                                }
                                worksheet.Cells[1, col++].Value = Tcolumnname;
                                showDataForHeader.Add(i++, true);
                            }

                        }
                        else
                        {
                            showDataForHeader.Add(i++, false);
                        }
                    }
                    else
                    {
                        showDataForHeader.Add(i++, false);
                    }
                }
            }

            //populate the data
            var rowIndex = startrowIndex;
            int totalCols = startrowIndex > 2 ? showDataForHeader.Where(d => d.Value).Count() : (col - (col == 0 ? 0 : 1));
            
            col = 1; i = 0;
            foreach (var colProp in columns)
            {
                if (showDataForHeader[i++])
                {
                    var dataValues = data.Select(item => new { value = colProp.GetValue(item) });
                    var excelAttrOfT = (TableFieldAttribute)Attribute.GetCustomAttribute(colProp, typeof(TableFieldAttribute));
                    if (dataValues != null)
                    {
                        if (!string.IsNullOrWhiteSpace(excelAttrOfT.HyperLinkText))
                        {
                            var rowIndexTemp = rowIndex;
                            //Date: 16 oct 2021; Asana: https://app.asana.com/0/0/1201194070138819/f; change: select the actual value for URI columns and not the dynamic object
                            foreach (var dataValue in dataValues.Select(d => d.value))
                            {
                                if (dataValue != null && !string.IsNullOrWhiteSpace(dataValue.ToString()))
                                {
                                    worksheet.Cells[rowIndexTemp, col].Hyperlink = new Uri(dataValue.ToString(), UriKind.Absolute);
                                    worksheet.Cells[rowIndexTemp, col].Value = excelAttrOfT.HyperLinkText;
                                    worksheet.Cells[rowIndexTemp, col].Style.Font.Color.SetColor(System.Drawing.Color.Blue);
                                }
                                rowIndexTemp++;
                            }
                        }
                        else
                        {
                            var dataArray = new List<object[]> { dataValues.ToArray() };
                            var cells = worksheet.Cells[rowIndex, col, rowIndex + dataValues.Count() - 1, col];
                            //cells.LoadFromArrays(dataArray);
                            cells.LoadFromCollection(dataValues, false);
                        }
                    }
                    col++;
                }
            }
            var totalRows = startrowIndex + data.Count() - 1;

            //Styling
            if (startrowIndex == 2)
            {
                worksheet.Row(1).Style.Font.Bold = true;
            }

            if (startrowIndex >= 2 && totalRows > 0)
            {

                var modelTable = worksheet.Cells[startrowIndex, 1, totalRows, totalCols];
                // Assign borders
                modelTable.Style.Border.Top.Style = ExcelBorderStyle.Thin;
                modelTable.Style.Border.Left.Style = ExcelBorderStyle.Thin;
                modelTable.Style.Border.Right.Style = ExcelBorderStyle.Thin;
                modelTable.Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                modelTable.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                modelTable.Style.VerticalAlignment = ExcelVerticalAlignment.Center;
                worksheet.Cells[1, 1, 1, totalCols].Style.Fill.PatternType = ExcelFillStyle.Solid;
                worksheet.Cells[1, 1, 1, totalCols].Style.Fill.BackgroundColor.SetColor(Color.FromArgb(0, 150, 207));

                try
                {
                    worksheet.Cells[worksheet.Dimension.Address].AutoFitColumns();
                }
                catch
                {

                }
            }
            var headerRow = 1;
            foreach (var colorColumn in columnsToColor)
            {
                for (int j = 1; j <= totalCols; j++)
                {
                    if (worksheet.Cells[headerRow, j].Value?.ToString() == colorColumn.ColumnName)
                    {
                        var conditionsString = colorColumn.ConditionalFormat;
                        var conditions = conditionsString.Split(',').Select(s => new
                        {
                            condition = s.Split('_')[0],
                            formula = s.Split('_')[1],
                            color = s.Split('_')[2]
                        }).ToList();
                        ExcelRange rng = worksheet.Cells[headerRow + 1, j, totalRows + 1, j];
                        foreach (var condition in conditions)
                        {
                            var condFormat = worksheet.ConditionalFormatting.AddExpression(rng);
                            condFormat.Style.Fill.BackgroundColor.Color = Color.FromName(condition.color);
                            condFormat.Formula = new ExcelFormulaAddress(rng.Address) + condition.formula;
                        }


                    }
                }
            }
            return showDataForHeader;
        }

        public Dictionary<int, bool> CreateExcel<T>(IEnumerable<T> data, ExcelWorksheet worksheet, Dictionary<int, bool> showDataForHeader, int startrowIndex = 2, bool datafetchedInParts = false)
        {

            int col = 1;
            var columns = data.FirstOrDefault()?.GetType().GetProperties().Where(p => Attribute.GetCustomAttribute(p, typeof(TableFieldAttribute)) != null);
            if (columns == null)
            {
                return showDataForHeader;
            }
            int i = 0;
            bool colGroupColoring = false;
            var prevgroup = ColGroupType.None;
            bool isPrevGroupColored = false;
            var columnsToColor = new List<TableFieldAttribute>();
            TableHelpers tableHelpers = new TableHelpers();
            if (startrowIndex == 2)
            {
                foreach (var colProp in columns)
                {
                    var Tattr = Attribute.GetCustomAttribute(colProp, typeof(TableFieldAttribute));
                    if (Tattr is TableFieldAttribute excelAttrOfT)
                    {

                        var Tcolumnname = excelAttrOfT.ColumnName;
                        if (_showAllColumns || (excelAttrOfT.ColumnRequirement != Requirement.HideIfNull || (!CheckIfColNull(colProp, data) ) || (CheckIfColNull(colProp, data)  && datafetchedInParts )))
                        {
                            if (excelAttrOfT.ColumnRequirement == Requirement.SpecificSettingBased && !CheckIfColRequiredBySetting(colProp, excelAttrOfT.CompanySettingsToCheck))
                            {
                                showDataForHeader.Add(i++, false);
                            }
                            else if (!_showAllColumns && excelAttrOfT.ColumnRequirement == Requirement.HideIfZero && CheckIfColZero(colProp, data))
                            {
                                showDataForHeader.Add(i++, false);
                            }
                            else if (excelAttrOfT.ColumnRequirement == Requirement.Selected && !selectedCols.ContainsKey(colProp.Name))
                            {
                                showDataForHeader.Add(i++, false);
                            }
                            else
                            {
                                if (!string.IsNullOrWhiteSpace(excelAttrOfT.CellFormat))
                                {
                                    worksheet.Column(col).Style.Numberformat.Format = excelAttrOfT.CellFormat;
                                }
                                if (excelAttrOfT.ColGroupType != ColGroupType.None)
                                {
                                    if (prevgroup == excelAttrOfT.ColGroupType)
                                    {
                                        if (isPrevGroupColored)
                                        {
                                            worksheet.Column(col).Style.Fill.PatternType = ExcelFillStyle.Solid;
                                            worksheet.Column(col).Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
                                            isPrevGroupColored = true;
                                        }
                                    }
                                    else
                                    {
                                        if (!isPrevGroupColored)
                                        {
                                            worksheet.Column(col).Style.Fill.PatternType = ExcelFillStyle.Solid;
                                            worksheet.Column(col).Style.Fill.BackgroundColor.SetColor(Color.LightGray);
                                            isPrevGroupColored = true;
                                        }
                                        else
                                        {
                                            isPrevGroupColored = false;
                                        }

                                    }
                                    prevgroup = excelAttrOfT.ColGroupType;
                                    colGroupColoring = true;
                                }
                                else
                                {
                                    prevgroup = ColGroupType.None;
                                    isPrevGroupColored = false;
                                }

                                if (_useNomenclature && excelAttrOfT.NomenclatureRequirement == true)
                                {
                                    if (_useNomenclature && excelAttrOfT.NomenclatureRequirement == true)
                                    {
                                        var j = 0;
                                        var listforheader = Tcolumnname.Split(' ').ToList();
                                        var listforNewHeader = Tcolumnname.Split(' ').ToList();
                                        foreach (var item in listforheader)
                                        {
                                            var nomenclaturename = nomenclatureSpecifier.GetHeaderName(item);
                                            listforNewHeader[j] = nomenclaturename;
                                            j++;
                                        }
                                        Tcolumnname = String.Join(" ", listforNewHeader);

                                    }
                                }
                                if (!string.IsNullOrWhiteSpace(excelAttrOfT.ConditionalFormat))
                                {
                                    columnsToColor.Add(excelAttrOfT);
                                }
                                worksheet.Cells[1, col++].Value = Tcolumnname;
                                showDataForHeader.Add(i++, true);
                            }

                        }
                        else
                        {
                            showDataForHeader.Add(i++, false);
                        }
                    }
                    else
                    {
                        showDataForHeader.Add(i++, false);
                    }
                }
            }
            //populate the data
            var rowIndex = startrowIndex;
            int totalCols = startrowIndex > 2 ? showDataForHeader.Where(d => d.Value).Count() : (col - (col == 0 ? 0 : 1));
            foreach (var item in data)
            {
                col = 1; i = 0;
                if (rowIndex % 2 == 0 && !colGroupColoring)
                {
                    worksheet.Cells[rowIndex, 1, rowIndex, totalCols].Style.Fill.PatternType = ExcelFillStyle.Solid;
                    worksheet.Cells[rowIndex, 1, rowIndex, totalCols].Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
                }
                foreach (var colProp in columns)
                {
                    if (showDataForHeader[i++])
                    {
                        var dataValue = colProp.GetValue(item);
                        var excelAttrOfT = (TableFieldAttribute)Attribute.GetCustomAttribute(colProp, typeof(TableFieldAttribute));
                        if (dataValue != null)
                        {
                            worksheet.Cells[rowIndex, col].Value = dataValue;
                            //if (!string.IsNullOrWhiteSpace(excelAttrOfT.ConditionalFormat))
                            //{
                            //    AddConditionalFormatting(worksheet.Cells[rowIndex, col], excelAttrOfT.ConditionalFormat);
                            //}

                            if (!string.IsNullOrWhiteSpace(dataValue.ToString()) && !string.IsNullOrWhiteSpace(excelAttrOfT.HyperLinkText))
                            {
                                worksheet.Cells[rowIndex, col].Hyperlink = new Uri(dataValue.ToString(), UriKind.Absolute);
                                worksheet.Cells[rowIndex, col].Value = excelAttrOfT.HyperLinkText;
                                worksheet.Cells[rowIndex, col].Style.Font.Color.SetColor(System.Drawing.Color.Blue);
                            }
                            else
                            {
                                worksheet.Cells[rowIndex, col].Value = dataValue;
                            }

                        }
                        col++;
                    }

                }
                rowIndex++;
            }
            var totalRows = startrowIndex + data.Count() -1;

            //Styling
            if (startrowIndex == 2)
            {
                worksheet.Row(1).Style.Font.Bold = true;
            }

            if (startrowIndex >= 2 && totalRows > 0)
            {

                var modelTable = worksheet.Cells[startrowIndex, 1, totalRows, totalCols];
                // Assign borders
                modelTable.Style.Border.Top.Style = ExcelBorderStyle.Thin;
                modelTable.Style.Border.Left.Style = ExcelBorderStyle.Thin;
                modelTable.Style.Border.Right.Style = ExcelBorderStyle.Thin;
                modelTable.Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                modelTable.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                modelTable.Style.VerticalAlignment = ExcelVerticalAlignment.Center;
                worksheet.Cells[1, 1, 1, totalCols].Style.Fill.PatternType = ExcelFillStyle.Solid;
                worksheet.Cells[1, 1, 1, totalCols].Style.Fill.BackgroundColor.SetColor(Color.FromArgb(0, 150, 207));

                try
                {
                    worksheet.Cells[worksheet.Dimension.Address].AutoFitColumns();
                }
                catch
                {

                }
            }
            var headerRow = 1;
            foreach (var colorColumn in columnsToColor)
            {
                for (int j = 1; j <= totalCols; j++)
                {
                    if (worksheet.Cells[headerRow, j].Value?.ToString() == colorColumn.ColumnName)
                    {
                        var conditionsString = colorColumn.ConditionalFormat;
                        var conditions = conditionsString.Split(',').Select(s => new
                        {
                            condition = s.Split('_')[0],
                            formula = s.Split('_')[1],
                            color = s.Split('_')[2]
                        }).ToList();
                        ExcelRange rng = worksheet.Cells[headerRow + 1, j, totalRows + 1, j];
                        foreach (var condition in conditions)
                        {
                            var condFormat = worksheet.ConditionalFormatting.AddExpression(rng);
                            condFormat.Style.Fill.BackgroundColor.Color = Color.FromName(condition.color);
                            condFormat.Formula = new ExcelFormulaAddress(rng.Address) + condition.formula;
                        }


                    }
                }
            }
            return showDataForHeader;
        }

        public ExcelWorksheet RemoveExtraColumns<T>( ExcelWorksheet worksheet)
        {
            
            var columns = typeof(T).GetProperties();
            List<int> columnNumbers = new List<int>();
            if (columns == null || worksheet == null || worksheet.Dimension == null)
            {
                return worksheet;
            }
            List<string> ColumnNames = new List<string>();
            for (int i = 1; i <= worksheet.Dimension.End.Column; i++)
            {
                ColumnNames.Add(worksheet.Cells[1, i].Value.ToString()); 
            }
            var start = worksheet.Dimension.Start;
            var end = worksheet.Dimension.End;
            foreach (var colProp in columns)
            {
                var Tattr = Attribute.GetCustomAttribute(colProp, typeof(TableFieldAttribute));
                if (Tattr is TableFieldAttribute excelAttrOfT)
                {
                    var Tcolumnname = excelAttrOfT.ColumnName;

                    if (_useNomenclature && excelAttrOfT.NomenclatureRequirement == true)
                    {
                        var j = 0;
                        var listforheader = Tcolumnname.Split(' ').ToList();
                        var listforNewHeader = Tcolumnname.Split(' ').ToList();
                        foreach (var item in listforheader)
                        {
                            var nomenclaturename = nomenclatureSpecifier.GetHeaderName(item);
                            listforNewHeader[j] = nomenclaturename;
                            j++;
                        }
                        Tcolumnname = String.Join(" ", listforNewHeader);
                    }
                    int index = ColumnNames.FindIndex(x => x == Tcolumnname);
                    if(index >= 0)
                    {
                        index += 1;
                        if (!_showAllColumns && (excelAttrOfT.ColumnRequirement == Requirement.HideIfNull))
                        {
                            bool showCol = false;
                            for (int row = 2; row <= end.Row; row++)
                            {
                                string cellValue = worksheet.Cells[row, index].Text;
                                if (!string.IsNullOrEmpty(cellValue))
                                {
                                    showCol = true;
                                    break;
                                }
                            }
                            if (!showCol)
                            {
                                //hack if two columns have same name
                                if(columnNumbers.Contains(index))
                                {
                                    index = ColumnNames.LastIndexOf(Tcolumnname) + 1;                                  
                                }
                                columnNumbers.Add(index);

                            }


                        }
                        else if (!_showAllColumns && (excelAttrOfT.ColumnRequirement == Requirement.HideIfZero))
                        {
                            bool showCol = false;
                            decimal value;
                            double value2;
                            for (int row = 2; row <= end.Row; row++)
                            {
                                object cellValue = worksheet.Cells[row, index].Text;
                                if (cellValue != null && Decimal.TryParse(cellValue.ToString(), out value) && value > 0)
                                {
                                    showCol = true;
                                    break;
                                }
                                else if (cellValue != null && Double.TryParse(cellValue.ToString(), out value2) && value2 > 0)
                                {
                                    showCol = true;
                                    break;
                                }

                            }
                            if (!showCol)
                            {
                                columnNumbers.Add(index);
                            }
                        }
                    }
                   
                }

               
            }
            int z = 0;
            columnNumbers.Sort();
            foreach (int x in columnNumbers)
            {
                worksheet.DeleteColumn(x - z);
                z++;
            }

            return worksheet;
        }
    }

    class ErrorMessage
    {
        public String Message { get; set; }
    }
}
