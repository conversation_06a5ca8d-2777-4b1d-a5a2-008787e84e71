﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Library.NumberSystem
{
    public static class CalculationMethods
    {
        //Date:'July 23, 2020'; Logic: 'Converting the 0 as double and then returning in case the denominator is Zero to faciliate further casting as double'; Asana: https://app.asana.com/0/1156323451215190/1185665649256753/f 
        public static object DivideTwoNumbers(object a, object b)
        {
            if (a != null && b != null)
            {
                Double.TryParse(a.ToString(), out Double num1);
                Double.TryParse(b.ToString(), out Double num2);
                if (num2 > 0)
                {
                    return (num1 / num2);
                }
            }
            return Convert.ToDouble(0);
        }
        //Date:'July 23, 2020'; Logic: 'Converting the 0 as double and then returning in case the denominator is Zero to faciliate further casting as double'; Asana: https://app.asana.com/0/1156323451215190/1185665649256753/f 
        public static object CalculatePercentage(object a, object b)
        {
            var data = (double)DivideTwoNumbers(a, b);
            return data > 0 ? data * 100 : Convert.ToDouble(0);

        }
    }
}
