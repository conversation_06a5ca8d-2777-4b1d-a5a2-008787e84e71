﻿using Libraries.CommonEnums;
using Libraries.PerspectiveColumns.Interface;
using System;
using System.Collections.Generic;
using System.Text;

namespace Libraries.PerspectiveColumns
{
    class MasterDataPerspective : IPerspective
    {
        List<PerspectiveColumnModel> IPerspective.Columns { get => Columns; set => throw new NotImplementedException(); }
        public List<PerspectiveColumnModel> Columns => new List<PerspectiveColumnModel>
        {
            new PerspectiveColumnModel() { Attribute = "Field User" ,       DisplayName = "GlobalSalesManager" ,     Name = "GSM" ,           IsMeasure = false ,   IsDimension = true},
            new PerspectiveColumnModel() { Attribute = "Field User" ,       DisplayName =  "NationalSalesManager" ,  Name = "NSM",            IsMeasure = false,    IsDimension = true},
            new PerspectiveColumnModel() { Attribute = "Field User" ,       DisplayName =  "ZonalSalesManager" ,     Name = "ZSM",            IsMeasure = false,    IsDimension = true},
            new PerspectiveColumnModel() { Attribute = "Field User" ,       DisplayName =  "RegionalSalesManager" ,  Name = "RSM",            IsMeasure = false,    IsDimension = true},
            new PerspectiveColumnModel() { Attribute = "Field User" ,       DisplayName =  "AreaSalesManager" ,      Name = "ASM",            IsMeasure = false,    IsDimension = true},
            new PerspectiveColumnModel() { Attribute = "Field User" ,       DisplayName =  "Employee" ,              Name = "ESM",            IsMeasure = false,    IsDimension = true},
            new PerspectiveColumnModel() { Attribute = "Field User" ,       DisplayName = "FieldUser Name" ,         Name = "FieldUserName",  IsMeasure = false,    IsDimension = true},
            new PerspectiveColumnModel() { Attribute = "Sales" ,            DisplayName = "Distributor" ,            Name = "Distributor",    IsMeasure = true,     IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.CountDistinct},
            new PerspectiveColumnModel() { Attribute = "Sales Territory" ,  DisplayName =  "Beat" ,                  Name = "Beat",           IsMeasure = true,     IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.CountDistinct},
            new PerspectiveColumnModel() { Attribute = "Sales Territory" ,  DisplayName = "Region" ,                 Name = "Region",         IsMeasure = true,     IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.CountDistinct},
            new PerspectiveColumnModel() { Attribute = "Outlets" ,          DisplayName = "Outlets",                 Name = "Shop",           IsMeasure = true,     IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.CountDistinct},
        };
    }
}
