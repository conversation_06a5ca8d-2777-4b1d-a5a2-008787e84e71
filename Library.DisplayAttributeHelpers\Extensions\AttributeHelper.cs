﻿using System;
using System.ComponentModel.DataAnnotations;
using System.Linq;

namespace Library.DisplayAttributeHelpers.Extensions
{
    public static class AttributeHelper
    {
        public static DisplayAttribute  GetDisplayAttribute(this Enum seg)
        {
            return seg.GetType()
                .GetField(seg.ToString())
                 .GetCustomAttributes(false)
                 .OfType<DisplayAttribute>()
                 .SingleOrDefault();
        }
    }
}
