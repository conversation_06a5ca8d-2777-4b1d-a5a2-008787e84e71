﻿using Newtonsoft.Json;
using System;
using System.IO;
using System.Threading.Tasks;

namespace Library.StorageWriter
{
    public class BlobWriter : BlobReader
    {
        public BlobWriter(string storageConnectionString, string containerName) : base(storageConnectionString, containerName)
        {
        }
        public async Task Rename(string filePath, string newName)
        {
            var blob = this._container.GetBlockBlobReference(filePath);
            var blobCopy = this._container.GetBlockBlobReference(newName);
            if (await blob.ExistsAsync())
            {
                await blobCopy.StartCopyAsync(blob);
                await blob.DeleteIfExistsAsync();
            }
        }
        public async Task MoveToStream(string sourceBlob, Stream destBlob)
        {
            var blob = this._container.GetBlockBlobReference(sourceBlob);
            if (await blob.ExistsAsync())
            {
                var srcStream = await blob.OpenReadAsync();
                await srcStream.CopyToAsync(destBlob);
                destBlob.Close();
                srcStream.Close();
                await blob.DeleteIfExistsAsync();
            }

        }

        public async Task<string> WriteToBlob<T>(string filename, T request)
        {
            var blockBlob = _container.GetBlockBlobReference(filename);
            await blockBlob.UploadTextAsync(JsonConvert.SerializeObject(request)).ConfigureAwait(false);
            return GetPublicPath(filename);
        }

        public async Task<string> AppendToBlob<T>(string filename, T request)
        {
            var blockBlob = _container.GetBlockBlobReference(filename);

            if (await blockBlob.ExistsAsync())
            {
                var oldContent = await blockBlob.DownloadTextAsync().ConfigureAwait(false);
                var newContent = oldContent + "\n\n" + JsonConvert.SerializeObject(request);
                await blockBlob.UploadTextAsync(newContent).ConfigureAwait(false);
                return GetPublicPath(filename);
            }
            await blockBlob.UploadTextAsync(JsonConvert.SerializeObject(request)).ConfigureAwait(false);
            return GetPublicPath(filename);
        }

        public async Task<string> WriteToBlob_Temp<T>(string filename, T request)
        {
            var blockBlob = _container.GetBlockBlobReference(filename);
            var requestTemp = "temp";
            try
            {
                requestTemp = JsonConvert.SerializeObject(request);
                //throw new Exception("test exception");
            }
            catch (Exception ex)
            {
                try
                {
                    using (var stream = await this.GetWriteStream($"Exceptions/{filename}.json"))
                    using (TextWriter textWriter = new StreamWriter(stream))
                    {
                        var serializer = new JsonSerializer
                        {
                            ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                            DefaultValueHandling = DefaultValueHandling.Ignore
                        };
                        serializer.Serialize(textWriter, request);
                    }

                    using (var exStream = await this.GetWriteStream($"Exceptions/{filename}.txt"))
                    using (TextWriter textWriter = new StreamWriter(exStream))
                    {
                        await textWriter.WriteAsync(ex.ToString());
                    }
                }
                catch (Exception)
                {
                }
            }
            await blockBlob.UploadTextAsync(requestTemp).ConfigureAwait(false);
            return GetPublicPath(filename + ".json");
        }

        public async Task<DateTime?> DeleteIfOlder(string fileId, DateTime oldestAllowedTransactionBlobdate)
        {
            var blockBlob = _container.GetBlockBlobReference(fileId);
            await blockBlob.FetchAttributesAsync();
            if (blockBlob.Properties.LastModified < oldestAllowedTransactionBlobdate)
            {
                await blockBlob.DeleteAsync();
                return blockBlob.Properties.LastModified?.DateTime;
            }
            return null;
        }

        public async Task<bool> IsBlobOlder(string fileId, DateTime oldestAllowedTransactionBlobdate)
        {
            var blockBlob = _container.GetBlockBlobReference(fileId);
            await blockBlob.FetchAttributesAsync();
            return blockBlob.Properties.LastModified < oldestAllowedTransactionBlobdate;

        }

        public async Task<string> WriteToBlob(string filename, string data, string contentType)
        {
            var blockBlob = _container.GetBlockBlobReference(filename);
            blockBlob.Properties.ContentType = contentType;
            await blockBlob.UploadTextAsync(data).ConfigureAwait(false);
            return GetPublicPath(filename);
        }

        public async Task<Stream> GetWriteStream(string filename, string contentType = "application/octet-stream")
        {
            var blockBlob = _container.GetBlockBlobReference(filename);
            blockBlob.Properties.ContentType = contentType;
            return await blockBlob.OpenWriteAsync().ConfigureAwait(false);
        }
    }
}
