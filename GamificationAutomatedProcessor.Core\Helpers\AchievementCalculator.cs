﻿using Libraries.CommonEnums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace GamificationAutomatedProcessor.Core.Helpers
{
    public class AchievementCalculator
    {
        public static double GetNumericValue(string value, KpiMeasure kpiMeasure)
        {
            if (value == null || value == "0")
            {
                return 0;
            }

            switch (kpiMeasure)
            {
                case KpiMeasure.Number:
                    return GetWholeNumberFromString(value);
                case KpiMeasure.Percentage:
                    return (double)GetDecimalFromString(value);
                case KpiMeasure.Time:
                    return 0;
                case KpiMeasure.YesNo:
                    return GetNumericalYesNo(value);
                case KpiMeasure.Currency:
                    return (double)GetDecimalFromString(value);
                case KpiMeasure.Decimal:
                case KpiMeasure.ValueAPI:
                    return (double)GetDecimalFromString(value);
                case KpiMeasure.TimeDuration:
                    return GetTotalInMinutes(value);
                case KpiMeasure.PercentageNumber:
                    return (double)GetDecimalFromString(value);
                default: return 0;
            }
        }
        private static int GetNumericalYesNo(string value)
        {
                if (value == "Yes")
                {
                return 1;
                }
            return 0;
        }
        private static decimal GetDecimalFromString(string value)
        {
                decimal n = 0.0M;
                decimal.TryParse(value, out n);
            return n;
        }
        public static double GetTotalInMinutes(string value)
        {
                var val = value.Substring(0, value.Length - 3).Split(':');
                int hour=0;
                int minute=0;
                int.TryParse(val[0], out hour);
                int.TryParse(val[1], out minute);
            return hour * 60 + minute;
        }
        public static int GetWholeNumberFromString(string value)
        {
                var val = 0;
                int.TryParse(value, out val);

            return val;
        }
    }
}
