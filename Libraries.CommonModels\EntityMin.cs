﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Libraries.CommonModels
{
   public class EntityMin
    {
        public long Id { get; set; }

        public string Name { get; set; }
    }
    public class EntityMinIds
    {
        public long Id1 { get; set; }

        public long Id2 { get; set; }
    }
    public class EntityMinCount: CountMin
    {
        public long Id { get; set; }
    }
    public class CountMin
    {
        public int Count { get; set; }
    }
    public class EntityMinlong
    {
        public long Id { get; set; }

    }
    public class EntityMinDecimal
    {
        public decimal Id { get; set; }

    }
    public class EntityMinInt
    {
        public int Id { get; set; }

        public string Name { get; set; }
    }
    public class EntityNameValue
    {
        public string Name { get; set; }
        public string Value { get;set;}
    }

    public class EntityMinMapDecimal
    {
        public long Id { get; set; }
        public decimal Value { get; set; }

    }

    public class EntityMinMapLong
    {
        public long Id { get; set; }
        public long Value { get; set; }

    }
    public class EntityMinMapDec
    {
        public long Id { get; set; }
        public decimal Value { get; set; }

    }

}
