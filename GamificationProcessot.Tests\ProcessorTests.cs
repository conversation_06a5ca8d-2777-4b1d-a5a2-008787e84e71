using GamificationProcessor.Configuration;
using GamificationProcessor.Core.Models;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Newtonsoft.Json;
using System;
using System.Threading.Tasks;

namespace GamificationProcessor.Tests
{
    [TestClass]
    public class ProcessorTests
    {
        private ServiceProvider serviceProvider;
        [TestInitialize]
        public void Initialise()
        {
            //Environment.SetEnvironmentVariable("BuildEnvironment", "ManageTesting");
            //Environment.SetEnvironmentVariable("KEYVAULT_ENDPOINT", "https://v3ManageReadOnly.vault.azure.net/");
            Environment.SetEnvironmentVariable("KEYVAULT_ENDPOINT", "https://v3DebugWritable.vault.azure.net/");
            var config = Configuration.GetConfiguration();

            IServiceCollection serviceCollection = new ServiceCollection();
            Dependencies.SetUp(serviceCollection, config);
            serviceProvider = serviceCollection.BuildServiceProvider();
        }
        [TestMethod]
        public async Task StartProcessorTest()
        {
            var processor = serviceProvider.GetRequiredService<GamificationProcessor>();
            var json = "{\"Id\":\"5326\",\"Data\":{\"EmployeeId\":106200,\"CompanyId\":10417,\"QualifiedDateKey\":\"20210308\"},\"EventTime\":\"2020-11-27 13:05:45.8353384\"}";
            var data = JsonConvert.DeserializeObject<GamificationQueueEvent>(json);
            //var request = new GamificationQueueEvent
            //{
            //    Id = "1234",
            //    EventTime = new DateTime(2021, 1, 1, 0, 0, 0),
            //    Data = new GamificationQueueEventData
            //    {
            //        EmployeeId = 1234,
            //        CompanyId = 10235,
            //        QualifiedDateKey = "20210101"
            //    }
            //};
            await processor.ProcessGamificationQueue(data);
        }
    }
}
