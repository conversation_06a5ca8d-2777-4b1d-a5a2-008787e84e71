﻿using System.Collections.Generic;
using System.Linq;

namespace FileGenerator.HelperModels
{
    public class PivotColumn
    {
        public string PivotTableName => $"Pivot";
        public char PivotColumnSeperator => '+';
        public char PivotLevel2ColumnSeperator => '+';
        public string ParentColumn { get; set; }
        public string SecondaryPivotColumn { get; set; }
        public string[] ValueColumns { get; set; }
        public string ValueColumn { get { return ValueColumns.FirstOrDefault(); } set { ValueColumns = new string[] { value }; } }

        public int? Step { get; set; }
    }
}
