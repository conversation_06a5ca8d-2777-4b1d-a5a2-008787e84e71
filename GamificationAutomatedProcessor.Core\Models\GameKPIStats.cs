﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace GamificationAutomatedProcessor.Core.Models
{
    public class GameKPIStat
    {
        [Required]
        public long GameId { get; set; }
        [Required]
        public long KPIId { get; set; }
        [Required]
        public string KPITarget { get; set; }
        [Required]
        public string KPIAchievedValue { get; set; }
         [Required]
        public long TeamId { get; set; }
        public bool IsQualifier { get; set; } 
        public bool IsKPIAchieved { get; set; } 
        public long CoinsEarned { get; set; }
        public bool IsUserQualified { get; set; } 
        public Guid? SessionId { get; set; }
        public long? SlabId { get; set; }
        public long? Payout { get; set; }
    }

    public class MinGameKPIStat
    {
        public long Id { get; set; }
        public long DateKey { get; set; }
    }
    public class GamificationQueue
    {
        public long EmployeeId { get; set; }

        public long CompanyId { get; set; }

        public string QualifiedDateKey { get; set; }

    }
}
