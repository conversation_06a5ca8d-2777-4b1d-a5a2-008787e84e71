﻿using Library.SlackService;
using System;
using System.Collections.Generic;
using System.Text;

namespace Library.ReverseGeoCoder
{
    public class GeoCodingGoogle 
    {
        private static string googleApiKey = "AIzaSyA9n-ebPmaRxzKZp59SZrNpyImuBbyX2IY";
        private readonly ErrorMessenger _errorMessanger;
        public GeoCodingGoogle(ErrorMessenger errorMessanger, GeoCodingV2.Source source = GeoCodingV2.Source.GoogleApi)
        {
            switch (source)
            {
                case GeoCodingV2.Source.GoogleApi:
                    googleApiKey = "AIzaSyCEZ6SRPZlDRKi_pfqKaNaJi8Sa5w4aPR8";
                    break;
                case GeoCodingV2.Source.GoogleApi_FO1:
                    googleApiKey = "AIzaSyA9n-ebPmaRxzKZp59SZrNpyImuBbyX2IY";
                    break;
                case GeoCodingV2.Source.GoogleApi_FO2:
                    googleApiKey = "AIzaSyAorD1HYkzELu0xoC6DeSi858WSvPnBdOE";
                    break;
                case GeoCodingV2.Source.GoogleApi_FO3:
                    googleApiKey = "AIzaSyDbq5R5DgIdFBV8Ej_Pa0B02LnIVI_8NZs";
                    break;
                case GeoCodingV2.Source.GoogleApi_FO4:
                    googleApiKey = "AIzaSyAXxRypEpNp0FFFpzNZQrxvY7kHBf0GfVA";
                    break;
                case GeoCodingV2.Source.GoogleApi_FO5:
                    googleApiKey = "AIzaSyC1B_8x1sGFQhim3RKRZ7606ybl_EQ8m6E";
                    break;
                case GeoCodingV2.Source.GoogleApi_FO6:
                    googleApiKey = "AIzaSyAMwUlwYIdn0sZH9601U2O4zN6pC0zSkIo";
                    break;
                case GeoCodingV2.Source.GoogleApi_FO7:
                    googleApiKey = "AIzaSyCbK1uxu89hZtX_0ppeu0kXIxtX6YPPxFM";
                    break;
                default:
                    break;
            }

            this._errorMessanger = errorMessanger;
        }


        public GeoCodedReturnData GetDataFromLatLngFromGeogleAPI(decimal lat, decimal lng)
        {
            var api = $"https://maps.googleapis.com/maps/api/geocode/json?key={googleApiKey}&latlng={lat},{lng}";
            var data = new APIActions<GoogleApiListObject>(_errorMessanger).Get(api).Result;
            if (data != null)
            {

                var item = new GeoCodedReturnData
                {
                    Address = data.GetAddress(),
                    City = data.GetAdmin2() ?? data.GetLocality(),
                    Locality = data.GetLocality(),
                    Latitude = lat,
                    Longitude = lng,
                    State = data.GetState(),
                    PinCode = data.GetPinCode(),
                    Country = data.GetCountry()
                };
                return item;
            }
            
            return null;
        }

        public GeoCodedReturnData GetDataFromLatLng(decimal lat, decimal lng)
        {
            return GetDataFromLatLngFromGeogleAPI(lat, lng);
        }
    }
}
