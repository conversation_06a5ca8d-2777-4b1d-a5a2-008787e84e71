﻿using GamificationProcessor.Core.Models;
using GamificationProcessor.Core.Repositories;
using GamificationProcessor.Core.Services.Models;
using GamificationProcessor.DbStorage.DbContexts;
using GamificationProcessor.DbStorage.DbModels;
using Libraries.CommonEnums;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Libraries.CommonModels;

namespace GamificationProcessor.DBStorage.Repositories
{
    public class GamificationRepository : IGamificationRepository
    {
        private readonly WritableTransactionDbContext transactionDb;
        private readonly ReportDbContext reportDb;
        private readonly MasterDbContext masterDb;
        public GamificationRepository(WritableTransactionDbContext transactionDb,
                                     ReportDbContext reportDb,
                                      MasterDbContext masterDb
                                    )
        {
            this.transactionDb = transactionDb;
            this.masterDb = masterDb;
            this.reportDb = reportDb;
        }

        public async Task SavePerspectiveData(long id, long companyId, long esmId, FA_MTD_LMTD fA_MTD_LMTD_Dates)
        {
            try
            {
                var transData = await transactionDb.GameKPIStats.Where(a => a.Id == id && a.CompanyId == companyId && a.EsmId == esmId).FirstOrDefaultAsync();
                var user = masterDb.Employees.Where(a => a.CompanyId == companyId && a.Id == esmId)
                    .Include(a => a.Region)
                    .Include(a => a.Region.Zone.Parent.Parent.Parent)
                    .Include(x => x.Parent)
                    .ThenInclude(b => b.Parent)
                    .ThenInclude(y => y.Parent)
                    .ThenInclude(y => y.Parent)
                    .ThenInclude(y => y.Parent)
                    .FirstOrDefault();
                var posCodeHierarchy = new PositionCodeHierarchy();
                if (transData != null && transData.PositionCodeId.HasValue)
                {
                    posCodeHierarchy = (await masterDb.PositionCodes.Where(p => p.Id == transData.PositionCodeId.Value && p.CompanyId == companyId).Select(p => new PositionCodeHierarchy
                    {
                        PositionCodeLevel = p.Level,
                        Level1Id = p.Id,
                        Level2Id = p.ParentId,
                        Level3Id = p.Parent.ParentId,
                        Level4Id = p.Parent.Parent.ParentId,
                        Level5Id = p.Parent.Parent.Parent.ParentId,
                        Level6Id = p.Parent.Parent.Parent.Parent.ParentId,
                        Level7Id = p.Parent.Parent.Parent.Parent.Parent.ParentId,
                        Level8Id = p.Parent.Parent.Parent.Parent.Parent.Parent.ParentId,
                    }).FirstOrDefaultAsync())?.GetUpdatedPositionCodeLevels();
                }

                if (transData != null && user != null)
                {
                    var daysDifference = (DateTime.Now - fA_MTD_LMTD_Dates.MTD.StartDate).Days + 1;
                    var existingGameRecord = reportDb.GamificationKPIStats.Where(a => a.ESMId == esmId
                    && a.GameId == transData.GameId
                    && a.KPIId == transData.KPIId
                    && a.TeamId == transData.TeamId
                    && a.DayStartDateKey == transData.DateKey).FirstOrDefault();

                    var x = new GamificationKPIStat()
                    {
                        ESMId = esmId,
                        ReportingManagerId = user.ParentId,
                        UserRole = user.UserRole,
                        Rank = Enum.GetName(typeof(EmployeeRank), user.Rank),
                        RegionId = (long)(user.RegionId == null ? 0 : user.RegionId),
                        DayStartDateKey = transData.DateKey,
                        DayStartMonth = fA_MTD_LMTD_Dates.MTD.MonthNumber,
                        DayStartWeek = daysDifference == 0 ? 1 : (daysDifference % 7 == 0 ? daysDifference / 7 : daysDifference / 7 + 1),
                        GameId = transData.GameId,
                        TeamId = transData.TeamId,
                        KPIId = transData.KPIId,
                        IsQualified = transData.IsKPIAchieved,
                        IsQualifierKPI = transData.IsQualifier,
                        Coins = transData.CoinsEarned,
                        Achievements = transData.KPIAchievedValue,
                        CompanyId = companyId,
                        PositionLevel1 = posCodeHierarchy?.Level1Id,
                        PositionLevel2 = posCodeHierarchy?.Level2Id,
                        PositionLevel3 = posCodeHierarchy?.Level3Id,
                        PositionLevel4 = posCodeHierarchy?.Level4Id,
                        PositionLevel5 = posCodeHierarchy?.Level5Id,
                        PositionLevel6 = posCodeHierarchy?.Level6Id,
                        PositionLevel7 = posCodeHierarchy?.Level7Id,
                        PositionLevel8 = posCodeHierarchy?.Level8Id,
                        SlabId = transData.SlabId,
                        Payout = transData.Payout
                    };
                    //check if achievement is of type
                    //Asana:-https://app.asana.com/0/1198878538673206/1201259648068264
                    //Date:- 29-10-2021
                    //Description Date format to accept:- 0:0hrs , 0:00hrs, 00:0hrs, 00:00hrs
                    Regex rgex = new Regex("^([0-1]?[0-9]|2[0-3]):[0-5]?[0-9]hrs$");
                    if (x.Achievements.Contains("hrs") && !rgex.IsMatch(x.Achievements))
                    {
                        x.Achievements = "00:00hrs";
                    }
                    if (user.Parent != null)
                    {
                        x.ASMId = (long)user.ParentId;
                        if (user.Parent.Parent != null)
                        {
                            x.RSMId = (long)user.Parent.ParentId;
                            if (user.Parent.Parent.Parent != null)
                            {
                                x.ZSMId = (long)user.Parent.Parent.ParentId;
                                if (user.Parent.Parent.Parent.Parent != null)
                                {
                                    x.NSMId = user.Parent.Parent.Parent.ParentId;
                                    if (user.Parent.Parent.Parent.Parent.Parent != null)
                                    {
                                        x.GSMId = user.Parent.Parent.Parent.Parent.ParentId;
                                    }
                                }
                            }
                        }
                    }
                    if (user.Region != null)
                    {
                        x.ZoneId = user.Region.ZoneId;
                        x.GeographyLevel5 = user.Region.Zone?.ParentId;
                        x.GeographyLevel6 = user.Region.Zone?.Parent?.ParentId;
                        x.GeographyLevel7 = user.Region.Zone?.Parent?.Parent?.ParentId;
                    }
                    if (existingGameRecord != null)
                    {
                        existingGameRecord.GameId = x.GameId;
                        existingGameRecord.TeamId = x.TeamId;
                        existingGameRecord.KPIId = x.KPIId;
                        existingGameRecord.IsQualified = x.IsQualified;
                        existingGameRecord.IsQualifierKPI = x.IsQualifierKPI;
                        existingGameRecord.Coins = x.Coins;
                        existingGameRecord.Achievements = x.Achievements;
                        existingGameRecord.DayStartDateKey = x.DayStartDateKey;
                        existingGameRecord.ASMId = x.ASMId;
                        existingGameRecord.RSMId = x.RSMId;
                        existingGameRecord.ZSMId = x.ZSMId;
                        existingGameRecord.NSMId = x.NSMId;
                        existingGameRecord.GSMId = x.GSMId;
                        existingGameRecord.RegionId = x.RegionId;
                        existingGameRecord.ZoneId = x.ZoneId;
                        existingGameRecord.ESMId = x.ESMId;
                        existingGameRecord.ReportingManagerId = x.ReportingManagerId;
                        existingGameRecord.UserRole = x.UserRole;
                        existingGameRecord.Rank = x.Rank;
                        existingGameRecord.CompanyId = x.CompanyId;
                        existingGameRecord.PositionLevel1 = x.PositionLevel1;
                        existingGameRecord.PositionLevel2 = x.PositionLevel2;
                        existingGameRecord.PositionLevel3 = x.PositionLevel3;
                        existingGameRecord.PositionLevel4 = x.PositionLevel4;
                        existingGameRecord.PositionLevel5 = x.PositionLevel5;
                        existingGameRecord.PositionLevel6 = x.PositionLevel6;
                        existingGameRecord.PositionLevel7 = x.PositionLevel7;
                        existingGameRecord.PositionLevel8 = x.PositionLevel8;
                        existingGameRecord.GeographyLevel5 = x.GeographyLevel5;
                        existingGameRecord.GeographyLevel6 = x.GeographyLevel6;
                        existingGameRecord.GeographyLevel7 = x.GeographyLevel7;

                        existingGameRecord.SlabId = x.SlabId;
                        existingGameRecord.Payout = x.Payout;
                    }
                    else
                    {

                        await reportDb.GamificationKPIStats.AddAsync(x);
                    }

                    await reportDb.SaveChangesAsync();
                    transData.IsSynched = true;
                    await transactionDb.SaveChangesAsync();


                }
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public List<EntityMin> GetAllActiveCompanies()
        {
            var companyWithGames = masterDb.Games.Select(a => a.CompanyId).Distinct().ToList();

            return masterDb.Companies.Where(c => !c.Deleted && companyWithGames.Contains(c.Id))
                .Select(c => new EntityMin
                {
                    Id = c.Id,
                    Name = c.Name
                }).ToList();
        }

        public async Task<List<Game>> GetDispatchGames(long companyId, int dayLimitforOrderDispatch)
        {
            try
            {
                var dispatchKPIs = await masterDb.KPI.Where(a => a.Name == "%DispatchAgainstOrder (Volume)" || a.Name == "% Order Validation").Select(s => s.Id).ToListAsync();

                var x = await masterDb.Games
                    .Include(g => g.TargetsforTeams).ThenInclude(t => t.KPISlabs)
                    .Include(h => h.CoinsforKpi)
                    .Where(g => g.CompanyId == companyId && g.TargetsforTeams.Any(s => dispatchKPIs.Contains(s.KpiId))
                                 && g.EndDate >= DateTime.Today.Date.AddDays(-dayLimitforOrderDispatch) && g.EndDate < DateTime.Today.Date && g.IsActive).ToListAsync();

                return x?.Select(e => new Game
                {
                    Id = e.Id,
                    Name = e.Name,
                    StartDate = e.StartDate,
                    EndDate = e.EndDate,
                    RewardType = e.RewardType,
                    CompanyId = e.CompanyId,
                    IsActive = e.IsActive,
                    CreatedAt = e.CreatedAt,
                    CreationContext = e.CreationContext,
                    LastUpdatedAt = e.LastUpdatedAt,
                    TargetsforTeams = e.TargetsforTeams?.Select(f => new TargetForTeams
                    {
                        Id = f.Id,
                        GameId = f.GameId,
                        TeamId = f.TeamId,
                        KpiId = f.KpiId,
                        Target = f.Target,
                        IsQualifier = f.IsQualifier,
                        IsActive = e.IsActive,
                        KPISlabs = f.KPISlabs?.Select(g => new KPISlab
                        {
                            Id = g.Id,
                            TargetForTeamId = g.TargetForTeamId,
                            SlabCoins = g.SlabCoins,
                            SlabPayout = g.SlabPayout,
                            SlabTarget = g.SlabTarget
                        })?.ToList()
                    })?.ToList(),
                    CoinsforKpi = e.CoinsforKpi?.Select(f => new CoinsforKpi
                    {
                        Id = f.Id,
                        GameId = f.GameId,
                        KpiId = f.KpiId,
                        Coins = f.Coins,
                    })?.ToList()
                })?.ToList();
            }
            catch (Exception e)
            {
                return null;
            }
           
        }
        public Dictionary<long, string> GetDispatchKPIIds()
        {
            try
            {
                Dictionary<long, string> dispatchKPIs = masterDb.KPI.Where(a => a.Name == "%DispatchAgainstOrder (Volume)" || a.Name == "% Order Validation").ToDictionary(v => v.Id, v => v.Name);
                return dispatchKPIs;
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        public async Task<List<GameUser>> GetGameUsers(long companyId)
        {
            return await masterDb.TeamUserMappings.Where(m => m.CompanyId == companyId && m.IsActive).Select(e => new GameUser
            {
                UserId = e.TeamPlayerId,
                TeamId = e.TeamId,
            }).ToListAsync();
        }
        public async Task<List<GameUser>> GetUsersDispatchAgainstOrderCalculation(long companyId, List<long> gameUserIds, Game game)
        {
            try
            {
                var gameStartDate = Convert.ToInt64(game.StartDate.ToString("yyyyMMdd"));
                var gameEndDate = Convert.ToInt64(game.EndDate.ToString("yyyyMMdd"));
                var data = await reportDb.Attendances
                .Where(m => m.CompanyId == companyId && gameUserIds.Contains(m.ESMId) && m.CallStartDateKey >= gameStartDate && m.CallStartDateKey <= gameEndDate)
                .GroupBy(o => o.ESMId)
                .Select(g => new { UserId = g.Key, DispatchInStdUnits = g.Sum(j => j.DispatchInStdUnits), OrderInStdUnits = g.Sum(i => i.OrderInStdUnits) }).ToListAsync();

                var summaryData = data.Select(e => new GameUser
                {
                    UserId = e.UserId,
                    DispatchInStdUnits = e.DispatchInStdUnits,
                    OrderInStdUnits = e.OrderInStdUnits,
                }).ToList();
                return summaryData;
            }
            catch (Exception e)
            {
                return null;
            }

        }
        public async Task<List<GameUser>> GetUsersOrderValidationCalculation(long companyId, List<long> gameUserIds, Game game)
        {
            try
            {
                var gameStartDate = Convert.ToInt64(game.StartDate.ToString("yyyyMMdd"));
                var gameEndDate = Convert.ToInt64(game.EndDate.ToString("yyyyMMdd"));
                var data = await reportDb.Attendances
                .Where(m => m.CompanyId == companyId && gameUserIds.Contains(m.ESMId) && (m.CallStartDateKey >= gameStartDate && m.CallStartDateKey <= gameEndDate))
                .GroupBy(o => o.ESMId)
                .Select(g => new { UserId = g.Key, DispatchInStdUnits = g.Sum(j => j.DispatchInUnits), OrderInStdUnits = g.Sum(i => i.OrderInUnits) }).ToListAsync();

                var summaryData = data.Select(e => new GameUser
                {
                    UserId = e.UserId,
                    DispatchInStdUnits = e.DispatchInStdUnits,
                    OrderInStdUnits = e.OrderInStdUnits,
                }).ToList();
                return summaryData;
            }
            catch (Exception e)
            {
                return null;
            }

        }
        

        public async Task SaveDispatchKPIData(long companyId, Game game, GameUser data, long teamId, long kpiId, FA_MTD_LMTD fA_MTD_LMTD_Dates)
        {
            try
            {
                var achievement = (data.DispatchInStdUnits / data.OrderInStdUnits) * 100;
                var existingGameRecord = reportDb.GamificationKPIStats.Where(a => a.ESMId == data.UserId
                   && a.GameId == game.Id
                   && a.KPIId == kpiId
                   && a.TeamId == teamId).FirstOrDefault();

                if (existingGameRecord != null)
                {
                    var slabachieved = game.TargetsforTeams.Where(a => a.KpiId == kpiId && a.TeamId == teamId).FirstOrDefault().KPISlabs.Where(c => Convert.ToDouble(c.SlabTarget) <= achievement).OrderByDescending(d => d.SlabTarget).FirstOrDefault();
                    if (slabachieved != null)
                    {
                        existingGameRecord.Coins = slabachieved.SlabCoins;
                        existingGameRecord.Payout = slabachieved.SlabPayout;
                        existingGameRecord.IsQualified = game.TargetsforTeams.Where(a => a.KpiId == kpiId && a.TeamId == teamId).FirstOrDefault().IsQualifier && Convert.ToInt64(achievement) > Convert.ToInt64(game.TargetsforTeams.Where(a => a.KpiId == kpiId && a.TeamId == teamId).FirstOrDefault().Target) ? true : false;
                    }
                    else
                    {
                        existingGameRecord.Coins = game.CoinsforKpi.Where(a => a.KpiId == kpiId).ToList().Count() > 0 ? game.CoinsforKpi.Where(a => a.KpiId == kpiId).FirstOrDefault().Coins : 0;
                        existingGameRecord.Payout = 0;
                        existingGameRecord.IsQualified = game.TargetsforTeams.Where(a => a.KpiId == kpiId && a.TeamId == teamId).FirstOrDefault().IsQualifier && Convert.ToInt64(achievement) > Convert.ToInt64(game.TargetsforTeams.Where(a => a.KpiId == kpiId && a.TeamId == teamId).FirstOrDefault().Target) ? true : false;
                    }
                    existingGameRecord.Achievements = achievement.ToString();

                }
                else
                {
                    var transData = await transactionDb.GameKPIStats.Where(a => a.CompanyId == companyId && a.EsmId == data.UserId).FirstOrDefaultAsync();
                    var user = masterDb.Employees.Where(a => a.CompanyId == companyId && a.Id == data.UserId)
                        .Include(a => a.Region)
                        .Include(a => a.Region.Zone.Parent.Parent.Parent)
                        .Include(x => x.Parent)
                        .ThenInclude(b => b.Parent)
                        .ThenInclude(y => y.Parent)
                        .ThenInclude(y => y.Parent)
                        .ThenInclude(y => y.Parent)
                        .FirstOrDefault();
                    var posCodeHierarchy = new PositionCodeHierarchy();
                    if (transData != null && transData.PositionCodeId.HasValue)
                    {
                        posCodeHierarchy = (await masterDb.PositionCodes.Where(p => p.Id == transData.PositionCodeId.Value && p.CompanyId == companyId).Select(p => new PositionCodeHierarchy
                        {
                            PositionCodeLevel = p.Level,
                            Level1Id = p.Id,
                            Level2Id = p.ParentId,
                            Level3Id = p.Parent.ParentId,
                            Level4Id = p.Parent.Parent.ParentId,
                            Level5Id = p.Parent.Parent.Parent.ParentId,
                            Level6Id = p.Parent.Parent.Parent.Parent.ParentId,
                            Level7Id = p.Parent.Parent.Parent.Parent.Parent.ParentId,
                            Level8Id = p.Parent.Parent.Parent.Parent.Parent.Parent.ParentId,
                        }).FirstOrDefaultAsync())?.GetUpdatedPositionCodeLevels();
                    }

                    if (transData != null && user != null)
                    {
                        var daysDifference = (DateTime.Now - fA_MTD_LMTD_Dates.MTD.StartDate).Days + 1;

                        var x = new GamificationKPIStat()
                        {
                            ESMId = data.UserId,
                            ReportingManagerId = user.ParentId,
                            UserRole = user.UserRole,
                            Rank = Enum.GetName(typeof(EmployeeRank), user.Rank),
                            RegionId = (long)(user.RegionId == null ? 0 : user.RegionId),
                            DayStartDateKey = transData.DateKey,
                            DayStartMonth = fA_MTD_LMTD_Dates.MTD.MonthNumber,
                            DayStartWeek = daysDifference == 0 ? 1 : (daysDifference % 7 == 0 ? daysDifference / 7 : daysDifference / 7 + 1),
                            GameId = game.Id,
                            TeamId = teamId,
                            KPIId = kpiId,
                            IsQualified = false,
                            IsQualifierKPI = game.TargetsforTeams.Where(a => a.KpiId == kpiId && a.TeamId == teamId).FirstOrDefault().IsQualifier,
                            CompanyId = companyId,
                            PositionLevel1 = posCodeHierarchy?.Level1Id,
                            PositionLevel2 = posCodeHierarchy?.Level2Id,
                            PositionLevel3 = posCodeHierarchy?.Level3Id,
                            PositionLevel4 = posCodeHierarchy?.Level4Id,
                            PositionLevel5 = posCodeHierarchy?.Level5Id,
                            PositionLevel6 = posCodeHierarchy?.Level6Id,
                            PositionLevel7 = posCodeHierarchy?.Level7Id,
                            PositionLevel8 = posCodeHierarchy?.Level8Id,
                        };
                        if (user.Parent != null)
                        {
                            x.ASMId = (long)user.ParentId;
                            if (user.Parent.Parent != null)
                            {
                                x.RSMId = (long)user.Parent.ParentId;
                                if (user.Parent.Parent.Parent != null)
                                {
                                    x.ZSMId = (long)user.Parent.Parent.ParentId;
                                    if (user.Parent.Parent.Parent.Parent != null)
                                    {
                                        x.NSMId = user.Parent.Parent.Parent.ParentId;
                                        if (user.Parent.Parent.Parent.Parent.Parent != null)
                                        {
                                            x.GSMId = user.Parent.Parent.Parent.Parent.ParentId;
                                        }
                                    }
                                }
                            }
                        }
                        if (user.Region != null)
                        {
                            x.ZoneId = user.Region.ZoneId;
                            x.GeographyLevel5 = user.Region.Zone?.ParentId;
                            x.GeographyLevel6 = user.Region.Zone?.Parent?.ParentId;
                            x.GeographyLevel7 = user.Region.Zone?.Parent?.Parent?.ParentId;
                        }
                        var slabachieved = game.TargetsforTeams.Where(a => a.KpiId == kpiId && a.TeamId == teamId).FirstOrDefault().KPISlabs.Where(c => Convert.ToDouble(c.SlabTarget) <= achievement).OrderByDescending(d => d.SlabTarget).FirstOrDefault();
                        if (slabachieved != null)
                        {
                            x.Coins = slabachieved.SlabCoins;
                            x.Payout = slabachieved.SlabPayout;
                            x.IsQualified = game.TargetsforTeams.Where(a => a.KpiId == kpiId && a.TeamId == teamId).FirstOrDefault().IsQualifier && Convert.ToInt64(achievement) > Convert.ToInt64(game.TargetsforTeams.Where(a => a.KpiId == kpiId && a.TeamId == teamId).FirstOrDefault().Target) ? true : false;
                        }
                        else
                        {
                            x.Coins = game.CoinsforKpi.Where(a => a.KpiId == kpiId).ToList().Count <0 ? game.CoinsforKpi.Where(a => a.KpiId == kpiId).FirstOrDefault().Coins : 0;
                            x.Payout = 0;
                            x.IsQualified = game.TargetsforTeams.Where(a => a.KpiId == kpiId && a.TeamId == teamId).FirstOrDefault().IsQualifier && Convert.ToInt64(achievement) > Convert.ToInt64(game.TargetsforTeams.Where(a => a.KpiId == kpiId && a.TeamId == teamId).FirstOrDefault().Target) ? true : false;

                        }
                        x.Achievements = achievement.ToString();
                        await reportDb.GamificationKPIStats.AddAsync(x);
                    }
                }

                await reportDb.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                throw;
            }
        }

    }
}
