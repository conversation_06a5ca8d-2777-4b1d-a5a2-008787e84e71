﻿using System;
using System.Collections.Generic;
using System.Text;

namespace GamificationProcessor.Core.Services.Models
{
    public class FA_MTD_LMTD
    {
        public FADateRange MTD { get; set; }
        public FADateRange LMTD { get; set; }
        public FADateRange YTD { get; set; }
    }

    public class FADateRange
    {
        public DateTime Today { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int MonthNumber { get; set; }
        public int YearNumber { get; set; }
        public string MonthName { get; set; }
        public long StartDateKey { get; set; }
        public long EndDateKey { get; set; }
        public long TodayKey { get; set; }
    }
}
