﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Library.SlackService.Model
{


    public class SlackMessage : ISlackMessage
    {
        public string Username { get; set; }
        public string Channel { get; set; }
        public string Text { get; set; }
        public List<Attachment> Attachments { get; set; }
    }

    public interface ISlackMessage
    {
        List<Attachment> Attachments { get; set; }
        string Text { get; set; }
        string Username { get; set; }
    }

    public class Attachment
    {
        public string FallBack { get; set; }
        public string Title { get; set; }
        public List<Field> Fields { get; set; }
        public string Color { get; set; }
    }
    public class Field
    {
        public string Title { get; set; }
        public string Value { get; set; }
        public bool Short { get; set; }
    }
}

