﻿using System;
using System.Collections.Generic;
using System.Text;
using Libraries.CommonEnums;


namespace GamificationProcessor.Core.Models
{
    public class GameUser
    {
        public long UserId { get; set; }
        public long TeamId { get; set; }
        public long GameId { get; set; }
        public string DayStartTime { get; set; }
        public double? DispatchInStdUnits { get; set; }
        public double? OrderInStdUnits { get; set; }
    }

}
