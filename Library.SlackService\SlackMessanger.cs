﻿using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace Library.SlackService
{
    public class CSTSlackMessanger : SlackMessanger
    {
        public CSTSlackMessanger() : base("*****************************************************************************")
        {

        }
    }
    public class SMSConfigSlackMessanger : SlackMessanger
    {
    }
    public abstract class SlackMessanger
    {
        private readonly string _webhookurl;
        private readonly HttpClient _client;
        private readonly JsonSerializerSettings _jsonSerializerSettings;

        public SlackMessanger(string webhookurl = "*****************************************************************************")
        {
            _webhookurl = webhookurl;
            _client = new HttpClient();
            _jsonSerializerSettings = new JsonSerializerSettings
            {
                ContractResolver = new CamelCasePropertyNamesContractResolver()
            };
        }

        public async Task SendToSlack(string message, string botName, string channel)
        {
            var slackMessage = new SlackMessage()
            {
                Channel = channel,
                Username = botName,
                Text = message
            };
            await SendToSlack(slackMessage);
        }

        public async Task SendToSlack(ISlackMessage slackMessage)
        {
            var content = new StringContent(JsonConvert.SerializeObject(slackMessage, _jsonSerializerSettings), Encoding.UTF8, "application/json");
            var result = await _client.PostAsync(_webhookurl, content);
            result.EnsureSuccessStatusCode();
        }
    }
}
