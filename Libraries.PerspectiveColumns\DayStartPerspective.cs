﻿using Libraries.CommonEnums;
using Libraries.PerspectiveColumns.Interface;
using System.Collections.Generic;

namespace Libraries.PerspectiveColumns
{
    public class DayStartPerspective : IPerspective
    {
        //private readonly linkNames linkNames;

        public DayStartPerspective(long companyId)
        {
            //linkNames = InMemorySettings.GetLinkNames(companyId);
        }
        List<PerspectiveColumnModel> IPerspective.Columns { get => Columns; set => throw new System.NotImplementedException(); }
        public List<PerspectiveColumnModel> Columns => new List<PerspectiveColumnModel>
        {
            new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = "GSM" , Name = "GSM" , IsMeasure = true , IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.CountDistinct, IsPivot = true},
            new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = /*linkNames.*/ "NationalSalesManager" , Name = "NSM", IsMeasure = true,  IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct, IsPivot = true},
            new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = /*linkNames.*/ "ZonalSalesManager" , Name = "ZSM", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct, IsPivot = true},
            new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = /*linkNames.*/ "RegionalSalesManager" , Name = "RSM", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct, IsPivot = true},
            new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = /*linkNames.*/ "AreaSalesManager" , Name = "ASM", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct, IsPivot = true},
            new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = /*linkNames.*/ "Employee" , Name = "ESM", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Unknown},
            new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = "Reporting Manager", Name = "ReportingManager", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct},
            new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = "FieldUser Name" , Name = "FieldUserName", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct, IsOtherMeasure = true},
            new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = "Rank" , Name = "FieldUserRank", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct, IsPivot = true},
            new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = "FieldUser HQ" , Name = "FieldUserHQ", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct},
            new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = "Field User ERP ID" , Name = "FieldUserERPID", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct, IsOtherMeasure = true},
            new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = "Region" , Name = "Region", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct, IsOtherMeasure = false},
            new PerspectiveColumnModel() { Attribute = "Field User" , DisplayName = "Zone" , Name = "Zone", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct, IsOtherMeasure = false},
            new PerspectiveColumnModel() { Attribute = "Attendance" , DisplayName = "Is App Used?" , Name = "DayStart" , IsMeasure = true , IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.Bool, IsOtherMeasure = true},
            new PerspectiveColumnModel() { Attribute = "Attendance" , DisplayName = "Assigned Type" , Name = "AssignedReasonCategory", IsMeasure = true,  IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct},
            new PerspectiveColumnModel() { Attribute = "Attendance" , DisplayName = "Type" , Name = "ReasonCategory", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct},
            new PerspectiveColumnModel() { Attribute = "Attendance" , DisplayName = "Assigned Reason" , Name = "AssignedReason", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct},
            new PerspectiveColumnModel() { Attribute = "Attendance" , DisplayName = "Reason" , Name = "Reason", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct},
            new PerspectiveColumnModel() { Attribute = "Attendance" , DisplayName = "Assigned JW User", Name = "AssignedJointWorkingEmployee", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct},
            new PerspectiveColumnModel() { Attribute = "Attendance" , DisplayName = "Selected JW User" , Name = "JointWorkingEmployee", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct},
            new PerspectiveColumnModel() { Attribute = "Attendance" , DisplayName = "Assigned"+/*linkNames.*/ "Beat" , Name = "AssignedBeat", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct},
            new PerspectiveColumnModel() { Attribute = "Attendance" , DisplayName = "Selected"+ /*linkNames.*/ "Beat" , Name = "SelectedBeat", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct},
            new PerspectiveColumnModel() { Attribute = "Attendance" , DisplayName = /*linkNames.*/ "Distributor", Name = "Distributor", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct},
            new PerspectiveColumnModel() { Attribute = "Attendance" , DisplayName = /*linkNames.*/ "Distributor Erp Id", Name = "DistributorErpId", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct},
            new PerspectiveColumnModel() { Attribute = "Attendance" , DisplayName = "Log In", Name = "Login", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Unknown},
            new PerspectiveColumnModel() { Attribute = "Attendance" , DisplayName = "Log Out", Name = "Logout", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Unknown},
            new PerspectiveColumnModel() { Attribute = "Attendance" , DisplayName = "Is Late", Name = "IsLate", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Bool},
            new PerspectiveColumnModel() { Attribute = "Attendance" , DisplayName = "Day End", Name = "IsNormallyEnd", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Bool},
            new PerspectiveColumnModel() { Attribute = "Attendance" , DisplayName = "Total time", Name = "TotalTime", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Avg},
            new PerspectiveColumnModel() { Attribute = "Attendance" , DisplayName = "First Call", Name = "FirstCallTime", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct},
            new PerspectiveColumnModel() { Attribute = "Attendance" , DisplayName = "First Call OVC?", Name = "IsFirstCallOVC", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Bool},
            new PerspectiveColumnModel() { Attribute = "Attendance" , DisplayName = "Last Call", Name = "LastCallTime", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Unknown},
            //Feb 9 2022; Asana: https://app.asana.com/0/1201791257051477/1201785537239052/f; Change: Make Total Retail Time a Value Type Measure
            new PerspectiveColumnModel() { Attribute = "Attendance" , DisplayName = "Total Retailng Time", Name = "TotalRetailTime", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Value},
            new PerspectiveColumnModel() { Attribute = "Attendance" , DisplayName = "Total Retailng Time (Physical Calls)", Name = "TotalRetailTimePhysical", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Value},
            new PerspectiveColumnModel() { Attribute = "Attendance" , DisplayName = "First PC", Name = "FirstPCTime", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Unknown},
            new PerspectiveColumnModel() { Attribute = "Attendance" , DisplayName = "Last PC", Name = "LastPCTime", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Unknown},
            new PerspectiveColumnModel() { Attribute = "Visit" , DisplayName = "TC", Name = "TC", IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Visit" , DisplayName = "Avg. TC", Name = "AvgTC", IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Avg},
            new PerspectiveColumnModel() { Attribute = "Visit" , DisplayName = "PC", Name = "PC", IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Visit" , DisplayName = "Avg. PC", Name = "AvgPC", IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Avg},
            new PerspectiveColumnModel() { Attribute = "Visit" , DisplayName = "PC (Sch.)", Name = "SPC", IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Visit" , DisplayName = "OVT", Name = "OVT", IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Visit" , DisplayName = "Avg. OVT", Name = "AvgOVT", IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Avg},
            new PerspectiveColumnModel() { Attribute = "Visit" , DisplayName = "OVT (%)", Name = "PerOVT", IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Percent},
            new PerspectiveColumnModel() { Attribute = "Visit" , DisplayName = "OVC", Name = "OVC", IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Visit" , DisplayName = "Avg. OVC", Name = "AvgOVC", IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Avg},
            new PerspectiveColumnModel() { Attribute = "Visit" , DisplayName = "OVC (%)", Name = "PerOVC", IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Percent},
            new PerspectiveColumnModel() { Attribute = "Visit" , DisplayName = "SC" , Name = "SC", IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Visit" , DisplayName = "TO", Name = "TelephonicOrders", IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Visit" , DisplayName = "Avg. TO", Name = "AvgTelephonicOrders", IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Avg},
            new PerspectiveColumnModel() { Attribute = "Visit" , DisplayName = "Productivity", Name = "Productivity", IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Visit" , DisplayName = "Productivity (Sch.)", Name = "ScheduledProductivity", IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Visit" , DisplayName = "Effective Calls", Name = "EffectiveCalls", IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Visit" , DisplayName = "Avg. Effective Calls", Name = "AvgEffectiveCalls", IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Avg},
            new PerspectiveColumnModel() { Attribute = "Visit" , DisplayName = "Scheme Effective Calls", Name = "SchemeEffectiveCalls", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Visit" , DisplayName = "Avg. Scheme Effective Calls", Name = "AvgSchemeEffectiveCalls", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Avg},
            new PerspectiveColumnModel() { Attribute = "Visit" , DisplayName = /*linkNames.*/ "Employee" + " JW Calls", Name = "JointWorkingCalls", IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Count},
            new PerspectiveColumnModel() { Attribute = "Visit" , DisplayName = "Manager JW Calls", Name = "ManagerJointWorkingCalls", IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Visit" , DisplayName = "New Outlets", Name = "NewOutletsCreated", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Visit" , DisplayName = "New Outlets (Net Value)", Name = "NewOutletSalesInRevenue", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Visit" , DisplayName = "New Outlets (Qty in Std Unit)", Name = "NewOutletSalesInStdUnits", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Visit" , DisplayName = "New Outlets (Qty in Unit)", Name = "NewOutletSalesInUnits", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Visit" , DisplayName = "No Of Other Activities", Name = "NoOfOtherActivities", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Unknown},
            new PerspectiveColumnModel() { Attribute = "Visit" , DisplayName = "First Other Activity Time", Name = "FirstOtherActivityTime", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Unknown},
            new PerspectiveColumnModel() { Attribute = "Visit" , DisplayName = "Last Other Activity Time", Name = "LastOtherActivityTime", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Unknown},
            new PerspectiveColumnModel() { Attribute = "Visit" , DisplayName = "Time Spent in Other Activities", Name = "TimeSpentinOtherActivities", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Unknown},
            new PerspectiveColumnModel() { Attribute = "Visit" , DisplayName = "Selected Journey " +  /*linkNames.*/"Outlets", Name = "SelectedJourneyOutlets", IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "Value" , Name = "OrderInRevenue", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "Avg. Value" , Name = "AvgOrderInRevenue", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Avg},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "Qty (Std Unit)" , Name = "OrderInStdUnits", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "Avg. Qty (Std Unit)" , Name = "AvgOrderInStdUnits", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Avg},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "Qty (Unit)" , Name = "OrderInUnits", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "Avg. Qty (Unit)" , Name = "AvgOrderInUnits", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Avg},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "Discount" , Name = "ProductWiseDiscount", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "FOC" , Name = "TotalSchemeQty", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "Scheme Discount" , Name = "TotalSchemeDiscount", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "Net Value" , Name = "NetValue", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "Avg. Net Value" , Name = "AvgNetValue", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Avg},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "Net Value (Sales)" , Name = "DispatchInRevenue", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "Sale Qty (Std Unit)" , Name = "DispatchInStdUnits", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "Sale Qty (Unit)" , Name = "DispatchInUnits", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "LPC" , Name = "LPC", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "Styles per Call" , Name = "StylePerCall", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "Secondary Category Per Call" , Name = "SecondaryCategoryPerCall", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "CAP" , Name = "CAP", IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "Covered (%)" , Name = "Covered", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Time" , DisplayName = "DayStartYear" , Name = "Year", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct, IsOtherMeasure = true, IsPivot = true},
            new PerspectiveColumnModel() { Attribute = "Time" , DisplayName = "Date" , Name = "Date", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct, IsOtherMeasure = true, IsPivot = true},
            new PerspectiveColumnModel() { Attribute = "Time" , DisplayName = "Month" , Name = "Month", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct, IsOtherMeasure = true, IsPivot = true},
            new PerspectiveColumnModel() { Attribute = "Time" , DisplayName = "Week" , Name = "Week", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.CountDistinct, IsOtherMeasure = true, IsPivot = true},
            new PerspectiveColumnModel() { Attribute = "Time" , DisplayName = "Telephonic Validated Calls" , Name = "TelephonicValidated", IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Time" , DisplayName = "Telephonic Validated Time" , Name = "TotalValidatedTime", IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "MustSell PC" , Name = "MustSellPC", IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "MustSell Order Units", Name = "MustSellOrderInUnits", IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "MustSell Order ( StdUnit ) ", Name = "MustSellOrderInStdUnits", IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "MustSell Order Net Value" , Name = "MustSellNetOrderInRevenue", IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Sum},
            new PerspectiveColumnModel() { Attribute = "Sales Measure" , DisplayName = "MustSell LPC", Name = "MustSellLPC", IsMeasure = true, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Unknown}
        };
    }
}
