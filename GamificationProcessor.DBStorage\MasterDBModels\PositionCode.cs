﻿using Libraries.CommonEnums;
using System;
using System.Collections.Generic;
using System.Text;

namespace GamificationProcessor.DbStorage.MasterDBModels
{
    public class PositionCode
    {
        public long Id { get; set; }
        public long CompanyId { get; set; }
        public string CodeId { get; set; }
        public string Name { get; set; }
        public PositionCodeLevel Level { get; set; }
        public long? ParentId { get; set; }
        public PositionCode Parent { get; set; }
        public bool Deleted { get; set; }
    }
}
