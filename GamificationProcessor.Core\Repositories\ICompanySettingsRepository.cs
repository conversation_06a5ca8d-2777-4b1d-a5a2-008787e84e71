﻿using System;
using System.Collections.Generic;
using System.Text;

namespace GamificationProcessor.Core.Repositories
{
    public interface ICompanySettingsRepository
    {
        Dictionary<string, object> GetSettings(long companyId);
        Dictionary<long, string> GetSettingsForCompaniesForMonthStart(long companyId);
        Dictionary<long, string> GetSettingForDistributorMapping(List<long> companyIds);
    }
}
