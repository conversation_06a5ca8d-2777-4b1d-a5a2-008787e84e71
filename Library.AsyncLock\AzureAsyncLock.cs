﻿using Library.SlackService;
using Microsoft.WindowsAzure.Storage;
using Microsoft.WindowsAzure.Storage.Blob;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Library.AsyncLock
{
    class ControlledLock
    {
        public string Id { get; set; }
        public string LeaseId { get; set; }
        public CloudBlockBlob Blob { get; set; }

        public ControlledLock(string id, string leaseId, CloudBlockBlob blob)
        {
            Id = id;
            LeaseId = leaseId;
            Blob = blob;
        }
    }


    public sealed class AzureAsyncLock<T>
    {
        private readonly HashSet<ControlledLock> employeesBeingProcessed = new HashSet<ControlledLock>();
        private readonly int maxAllowedAttempts;
        private const int waitTimePerAttemptMillis = 2000;
        private readonly AutoResetEvent _mutex = new AutoResetEvent(true);
        private readonly CloudBlobClient _client;
        private readonly ErrorMessenger _logger;
        private CloudBlobContainer _container;
        private Timer _renewTimer;
        private TimeSpan LockTimeout = TimeSpan.FromMinutes(1);
        private TimeSpan RenewInterval => TimeSpan.FromSeconds(45);

        public AzureAsyncLock(string connectionString, string containerName, ErrorMessenger _logger, int maxWaitSeconds = 30)
        {
            this.maxAllowedAttempts = maxWaitSeconds * 1000 / waitTimePerAttemptMillis;
            var account = CloudStorageAccount.Parse(connectionString);
            _client = account.CreateCloudBlobClient();
            _container = _client.GetContainerReference(containerName);
            _renewTimer = new Timer(RenewLeases, null, RenewInterval, RenewInterval);
            this._logger = _logger;
        }

        private async void RenewLeases(object state)
        {
            if (_mutex.WaitOne())
            {
                try
                {
                    if (employeesBeingProcessed.Count() > 0)
                    {
                        foreach (var entry in employeesBeingProcessed)
                            await RenewLock(entry);
                    }
                }
                catch (Exception ex)
                {
                    await _logger.SendToSlack(ex, $"Error renewing leases - {ex.Message}");
                }
                finally
                {
                    _mutex.Set();
                }
            }
        }

        private async Task RenewLock(ControlledLock entry)
        {
            try
            {
                await entry.Blob.RenewLeaseAsync(AccessCondition.GenerateLeaseCondition(entry.LeaseId));
            }
            catch (Exception ex)
            {
                await _logger.SendToSlack(ex, $"Error renewing lease - {ex.Message}");
            }
        }

        public async Task<IDisposable> LockAsync(T Id)
        {
            var av = new AttemptValidator(maxAllowedAttempts);
            while (!await AcquireLock(Id, av))
            {
                if (av.HasExceeded())
                {
                    throw new MaxLockAttemptExceededException();
                }
            }
            return new Releaser(this, Id);
        }

        private async Task<bool> AcquireLock(T Id, AttemptValidator av)
        {
            while (employeesBeingProcessed.Any(e => e.Id == Id.ToString()) && av.NextAttempt())
            {
                await Task.Delay(waitTimePerAttemptMillis);
            }

            if (av.HasExceeded()) return false;

            var blob = _container.GetBlockBlobReference(Id.ToString());

            if (!await blob.ExistsAsync())
                await blob.UploadTextAsync(string.Empty);

            if (_mutex.WaitOne())
            {
                try
                {
                    var leaseId = await blob.AcquireLeaseAsync(LockTimeout);
                    employeesBeingProcessed.Add(new ControlledLock(Id.ToString(), leaseId, blob));
                    return true;
                }
                catch (StorageException ex)
                {
                    //await _logger.SendToSlack(ex, $"Failed to acquire lock {Id} - {ex.Message}");
                    av.NextAttempt();
                    return false;
                }
                finally
                {
                    _mutex.Set();
                }
            }
            await Task.Delay(waitTimePerAttemptMillis);
            av.NextAttempt();
            return false;
        }

        private sealed class Releaser : IDisposable
        {
            private readonly T employeeToRealease;
            private readonly AzureAsyncLock<T> asyncLock;

            internal Releaser(AzureAsyncLock<T> asyncLock, T employeeToRealease)
            {
                this.asyncLock = asyncLock;
                this.employeeToRealease = employeeToRealease;
            }
            public void Dispose()
            {
                asyncLock.ReleaseLock(employeeToRealease).Wait();
            }
        }

        private async Task ReleaseLock(T Id)
        {
            var blob = _container.GetBlockBlobReference(Id.ToString());

            //if (!await blob.ExistsAsync())
              //  await blob.UploadTextAsync(string.Empty);

            if (_mutex.WaitOne())
            {
                try
                {
                    var entry = employeesBeingProcessed.FirstOrDefault(x => x.Id == Id.ToString());
                    if (entry != null)
                    {
                        try
                        {
                            await blob.ReleaseLeaseAsync(AccessCondition.GenerateLeaseCondition(entry.LeaseId));
                        }
                        catch (Exception ex)
                        {
                            await _logger.SendToSlack(ex, $"Error releasing lock - {ex.Message}");
                        }
                        employeesBeingProcessed.Remove(entry);
                    }

                }
                finally
                {
                    _mutex.Set();
                }
            }
        }



        private class AttemptValidator
        {
            private readonly int maxAttempts;
            private int currentAttempts;

            public AttemptValidator(int maxAttempts)
            {
                this.maxAttempts = maxAttempts;
            }
            public bool NextAttempt()
            {
                currentAttempts++;
                return !HasExceeded();
            }
            public bool HasExceeded()
            {
                return currentAttempts > maxAttempts;
            }
        }



    }
}
