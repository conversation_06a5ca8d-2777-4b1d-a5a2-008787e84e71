﻿using Library.StorageWriter;
using System;
using System.Threading.Tasks;

namespace Library.ApiLogger.CosmosLogs
{
    public class StorageLogWriter_Temp : ISecondaryLogWriter, IDisposable
    {
        private readonly BlobWriter _logWriter;

        public StorageLogWriter_Temp(string storageConnectionString, string containerName)
        {
            _logWriter = new BlobWriter(storageConnectionString, containerName);
        }

        public void Dispose()
        {
            //Do Nothing
        }

        public string GetPublicUrl(string fileName)
        {
            return $"{_logWriter.GetPublicPath(fileName)}.json";
        }

        public async Task Log(IApiLog request)
        {
            await LogToBlob(request.RequestId, request);
        }

        public async Task LogToBlob<T>(string id, T request)
        {
            var blockBlob = await _logWriter.WriteToBlob_Temp(id , request);
        }
    }
}
