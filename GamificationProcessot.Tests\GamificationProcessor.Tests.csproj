﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netcoreapp3.1</TargetFramework>

    <IsPackable>false</IsPackable>
  </PropertyGroup>

  <ItemGroup>
	<PackageReference Include="Microsoft.Azure.KeyVault" Version="3.0.3" />
	<PackageReference Include="Microsoft.Azure.Services.AppAuthentication" Version="1.0.3" />
	<PackageReference Include="Microsoft.EntityFrameworkcore.SqlServer" Version="2.0.2" />
	<PackageReference Include="Microsoft.Extensions.Configuration.AzureKeyVault" Version="2.2.0" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="16.7.1" />
    <PackageReference Include="MSTest.TestAdapter" Version="2.1.1" />
    <PackageReference Include="MSTest.TestFramework" Version="2.1.1" />
  </ItemGroup>
	
  <ItemGroup>
    <ProjectReference Include="..\GamificationProcessor\GamificationProcessor.csproj" />
    <ProjectReference Include="..\GamificationProcessor.Core\GamificationProcessor.Core.csproj" />
    <ProjectReference Include="..\GamificationProcessor.DbStorage\GamificationProcessor.DbStorage.csproj" />
  </ItemGroup>
	
  <ItemGroup>
    <None Update="appsettings.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>
	
</Project>
