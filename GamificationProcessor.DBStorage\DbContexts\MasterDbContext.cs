﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Threading.Tasks;
using System.Threading;
using GamificationProcessor.DbStorage.MasterDBModels;
using GamificationProcessor.Core.Models;
using GamificationProcessor.DBStorage.MasterDBModels;

namespace GamificationProcessor.DbStorage.DbContexts
{
    public class MasterDbContext : DbContext
    {
        public MasterDbContext(DbContextOptions<MasterDbContext> options) : base(options)
        {

        }
              
        public DbSet<Employee> Employees { get; set; }
        public DbSet<Region> Regions { get; set; }
        public DbSet<CompanySetting> CompanySettings { get; set; }
        public DbSet<CompanySettingValue> CompanySettingValues { get; set; }
        public DbSet<PositionCode> PositionCodes { get; set; }
        public DbSet<Zone> Zones { get; set; }
        public DbSet<Geography> Geographies { get; set; }
        public DbSet<DBStorage.MasterDBModels.Companies> Companies { get; set; }
        public DbSet<DBStorage.MasterDBModels.Game> Games { get; set; }
        public DbSet<DBStorage.MasterDBModels.KPI> KPI { get; set; }
        public DbSet<TeamUserMapping> TeamUserMappings { get; set; }


        public override int SaveChanges()
        {
            throw new NotImplementedException();
        }

        public override int SaveChanges(bool acceptAllChangesOnSuccess)
        {
            throw new NotImplementedException();
        }

        public override Task<int> SaveChangesAsync(bool acceptAllChangesOnSuccess, CancellationToken cancellationToken = default(CancellationToken))
        {
            throw new NotImplementedException();
        }

        public override Task<int> SaveChangesAsync(CancellationToken cancellationToken = default(CancellationToken))
        {
            throw new NotImplementedException();
        }


        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {

        }
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
        }
    }
}
