﻿using Libraries.CommonEnums;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace Library.ApiLogger.CosmosLogs
{
    public interface IApiLog
    {
        string RequestId { get; }
        string Category { get; }
        string Input { get; set; }
        string Output { get; set; }
        long CompanyId { get; }
    }
    public class ApiLogRequest : IApiLog
    {
        public ApiLogRequest()
        {
            this.RequestId = Guid.NewGuid().ToString();

        }

        [JsonProperty(PropertyName = "_id")]
        public string Id { set { value = this.RequestId; } get { return this.RequestId; } }

        /// <summary>
        /// Guid for the Request
        /// </summary>
        public string RequestId { get; set; }
        /// <summary>
        /// UserName Of the Requester
        /// </summary>
        public string UserName { get; set; }
        public long CompanyId { get; set; }
        /// <summary>
        /// UTC Time when Response was delivered
        /// </summary>
        public DateTime ResponseTime { get; set; }
        /// <summary>
        /// UTC Time when Api Action was Requested
        /// </summary>
        public DateTime RequestTime { get; set; }
        /// <summary>
        /// UTC Timestamp when Api Action was Requested
        /// </summary>
        public long RequestTimestamp { get; set; }
        /// <summary>
        /// WebApi Path which was Used to request data
        /// </summary>
        public string RequestPath { get; set; }
        /// <summary>
        /// Body of the Request
        /// </summary>
        public String Input { get; set; }
        /// <summary>
        /// Status Text/ Error Text
        /// </summary>
        public string Status { get; set; }
        /// <summary>
        /// Http Response Code
        /// </summary>
        public int StatusCode { get; set; }
        /// <summary>
        /// Body of the Response
        /// </summary>
        public string Output { get; set; }

        public Exception Exception { get; set; }

        public ApiType ApiType { set; get; }

        /// <summary>
        /// Time taken to process the request
        /// </summary>
        public TimeSpan Duration
        {
            get
            {
                return ResponseTime - RequestTime;
            }
        }
        /// <summary>
        /// Headers in Api Request
        /// </summary>
        public Dictionary<string, string> RequestHeaders { get; set; }
        /// <summary>
        /// Headers in Api Response
        /// </summary>
        public IDictionary<string, string> ResponseHeaders { get; set; }
        public string RequestIpAddress { get; set; }
        public string Description { get; set; }
        public string Category => ApiType.ToString();


    }
}
