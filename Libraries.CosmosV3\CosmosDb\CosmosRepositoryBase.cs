﻿using Microsoft.Azure.Cosmos;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Libraries.CosmosV3.CosmosDb
{
    public abstract class CosmosRepositoryBase
    {
        private Container container;
        protected CosmosRepositoryBase(CosmosDbBase db,
            string containerName)
        {
            this.container = db.GetContainer(containerName);
        }
        public async Task<JObject> GetLatestRecord(string partitionKey)
        {
            string query = "select top 1 * from c order by c._ts desc";
            var response = await GetFeedIterator<JObject>(query, new PartitionKey(partitionKey))
                .ReadNextAsync();
            var item = response.FirstOrDefault();
            return item;
        }
        protected async Task AddItemProtectedAsync<T>(T item)
        {
            await container.CreateItemAsync(item);
        }
        protected async Task AddOrUpdateItemProtectedAsync<T>(T item, PartitionKey? partitionKey = null)
        {
            await container.UpsertItemAsync(item, partitionKey);
        }
        protected async Task DeleteItemProtectedAsync(string id, PartitionKey partitionKey)
        {
             await container.DeleteItemAsync<JObject>(id, partitionKey);
        }
        public virtual async Task<T> GetItemAsync<T>(string id, PartitionKey partitionKey)
        {
            try
            {
                ItemResponse<T> response = await container.ReadItemAsync<T>(id, partitionKey);
                return response.Resource;
            }
            catch (CosmosException ex) when (ex.StatusCode == System.Net.HttpStatusCode.NotFound)
            {
                return default;
            }

        }
        public async Task<T> GetItemAsync<T>(string id, string partitionKey)
        {
            return await this.GetItemAsync<T>(id, new PartitionKey(partitionKey));
        }
        protected virtual FeedIterator<T> GetFeedIterator<T>(string queryString, PartitionKey partitionKey)
        {
            var feedIterator = container.GetItemQueryIterator<T>(new QueryDefinition(queryString), null, new QueryRequestOptions
            {
                EnableScanInQuery = false,
                PartitionKey = partitionKey,
            });
            return feedIterator;
        }
        public async Task<IEnumerable<T>> GetItemsAsync<T>(string partitionKey)
        {
            return await this.GetItemsAsync<T>("select * from c", new PartitionKey(partitionKey));
        }
        public async Task<IEnumerable<T>> GetItemsAsync<T>(string query, string partitionKey)
        {
            return await this.GetItemsAsync<T>(query, new PartitionKey(partitionKey));
        }
        public async Task<object> GetObjectForQueryAsync(string query, string partitionKey)
        {
            return (await this.GetItemsAsync<object>(query, new PartitionKey(partitionKey))).FirstOrDefault();
        }
        public virtual async Task<IEnumerable<T>> GetItemsAsync<T>(string queryString, PartitionKey partitionKey)
        {
            var query = container.GetItemQueryIterator<T>(new QueryDefinition(queryString), null, new QueryRequestOptions
            {
                EnableScanInQuery = false,
                PartitionKey = partitionKey,
            });
            List<T> results = new List<T>();
            while (query.HasMoreResults)
            {
                var response = await query.ReadNextAsync();
                results.AddRange(response.ToList());
            }

            return results;
        }
        public async Task<IEnumerable<T>> GetMultiPartitionItemsAsync<T>(string queryString, List<string> partitions, string partitionKeyPlaceholder = "$partitionKey")
        {
            List<T> results = new List<T>();
            List<Task<IEnumerable<T>>> tasks = new List<Task<IEnumerable<T>>>();
            var counter = 0;
            foreach (var partitionKey in partitions)
            {
                var querySQL = queryString.Replace(partitionKeyPlaceholder, partitionKey);
                tasks.Add(GetItemsAsync<T>(querySQL, new PartitionKey(partitionKey)));
                if (++counter % 10 == 0)
                {
                    await Task.WhenAll(tasks);
                }
            }
            var res = await Task.WhenAll(tasks);
            results.AddRange(res.SelectMany(x => x));
            return results;
        }
        public async Task<IEnumerable<T>> GetCrossPartitionItemsAsync<T>(string queryString)
        {
            var query = container.GetItemQueryIterator<T>(new QueryDefinition(queryString), null, new QueryRequestOptions
            {
                EnableScanInQuery = true
            });
            List<T> results = new List<T>();
            while (query.HasMoreResults)
            {
                var response = await query.ReadNextAsync();
                results.AddRange(response.ToList());
            }
            return results;
        }
        protected async Task UpdateItemProtectedAsync<T>(T item, PartitionKey partitionKey)
        {
            await container.UpsertItemAsync<T>(item, partitionKey);
        }
    }
}
