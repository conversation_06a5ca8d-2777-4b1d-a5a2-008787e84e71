﻿using Libraries.CommonEnums;
using System;
using System.Collections.Generic;
using System.Text;

namespace Libraries.CommonModels
{
    public class EntityMinWithParent
    {
        public long Id { get; set; }

        public string Name { get; set; }
        public string BeatErpId { get; set; }

        public EntityMinWithParent Parent { get; set; }
    }
    public class EntityMinUser
    {
        public long UserId { get; set; }
        public PortalUserRole UserRole { get; set; }
    }
}
