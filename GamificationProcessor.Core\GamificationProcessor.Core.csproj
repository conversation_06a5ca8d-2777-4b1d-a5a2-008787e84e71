﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netstandard2.0</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\Libraries.CommonModels\Libraries.CommonModels.csproj" />
    <ProjectReference Include="..\Library.Infrastructure\Library.Infrastructure.csproj" />
    <ProjectReference Include="..\Library.ResiliencyHelpers\Library.ResiliencyHelpers.csproj" />
    <ProjectReference Include="..\Library.ResilientHttpClient\Library.ResilientHttpClient.csproj" />
    <ProjectReference Include="..\Library.SlackService\Library.SlackService.csproj" />
    <ProjectReference Include="..\Library.StringHelpers\Library.StringHelpers.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json" Version="12.0.3" />
  </ItemGroup>

</Project>
