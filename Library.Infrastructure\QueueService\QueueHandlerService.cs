﻿using Microsoft.WindowsAzure.Storage;
using Microsoft.WindowsAzure.Storage.Queue;
using Newtonsoft.Json;
using System;
using System.Threading.Tasks;

namespace Library.Infrastructure.QueueService
{
    public class QueueHandlerService
    {
        private readonly string connectionString;

        public QueueHandlerService(string connectionString)
        {
            this.connectionString = connectionString;
        }

        public async Task AddToQueue<T>(string queueString, T data)
        {
            var account = CloudStorageAccount.Parse(connectionString);
            var queueClient = account.CreateCloudQueueClient();
            var queue = queueClient.GetQueueReference(queueString);

            var msg = new CloudQueueMessage(JsonConvert.SerializeObject(data));
            await queue.AddMessageAsync(msg).ConfigureAwait(false);
        }

        public async Task<CloudQueueMessage> GetItem(string queueString)
        {
            var account = CloudStorageAccount.Parse(connectionString);
            var queueClient = account.CreateCloudQueueClient();
            var queue = queueClient.GetQueueReference(queueString);

            var item = await queue.GetMessageAsync().ConfigureAwait(false);
            if (item == null)
            {
                return default(CloudQueueMessage);
            }
            return item;
        }

        public async Task DeleteMessage(string queueString, CloudQueueMessage queueMessage)
        {
            var account = CloudStorageAccount.Parse(connectionString);
            var queueClient = account.CreateCloudQueueClient();
            var queue = queueClient.GetQueueReference(queueString);
            await queue.DeleteMessageAsync(queueMessage).ConfigureAwait(false); ;
        }
    }
}
