﻿using GamificationAutomatedProcessor.Core.Models;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace GamificationAutomatedProcessor.Core.Repositories
{
    public interface IGameRepository
    {
        Task<List<Game>> GetAllActiveGames(DateTime today);
        Task<List<Team>> GetActiveTeamsForGame(long gameId);
        Task<List<KPI>> GetMonthlyKPIsForGame(long gameId);
        Task<List<Employee>> GetActiveEmployeesInTeam(long teamId);
        Task<Dictionary<long, TargetForTeams>> GetMonthlyKPIsTargetForTeam(long teamId,long gameId, List<long> kpiIds);
        Task<Dictionary<long, CoinsforKpi>> GetMonthlyKPIsCoinsForTeam(long gameId, List<long> kpiIds);
        Task<MinGameKPIStat> SaveKPIUserStats(GameKPIStat gameKPIStats, long companyId, long employeeId, DateTime gameApiDate);

    }
}
