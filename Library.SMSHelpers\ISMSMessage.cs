﻿using Library.Infrastructure.QueueService;
using Library.SlackService;
using System;
using System.Collections.Generic;
using System.Text;

namespace Library.SMSHelpers
{
    public interface ISMSMessage : IQueueMessage
    {
        string Message { get; }
        string To { get; }
    }
    public class SMSMessage : ISMSMessage
    {
        public string Message { get; set; }
        public string To { get; set; }
        public ISlackMessage ToSlackMessage(string channel)
        {
            return new SlackMessage
            {
                Channel = channel,
                Text = $"SMS to:{To}",
                Username = "DebugSMSRequest",
                Attachments = new System.Collections.Generic.List<Attachment>
                {
                    new Attachment
                    {
                        Title=Message,
                    }
                }
            };
        }
    }
}
