﻿using Library.SlackService;
using Microsoft.Azure.Documents;
using Microsoft.Azure.Documents.Client;
using System;
using System.Threading.Tasks;

namespace Library.ApiLogger.CosmosLogs
{
    public class CosmosDbLogger : IPrimaryLogWriter
    {
        private DocumentClient client;
        private readonly CosmosSource cosmosSource;
        private ILogWriter _storageWriter;
        private readonly CosmosErrorMessenger cosmosErrorMessenger;

        public CosmosDbLogger(CosmosSource cosmosSource, ISecondaryLogWriter storageWriter, CosmosErrorMessenger cosmosErrorMessenger)
        {
            this.cosmosSource = cosmosSource;
            _storageWriter = storageWriter;
            this.cosmosErrorMessenger = cosmosErrorMessenger;
            client = new DocumentClient(new Uri(cosmosSource.EndpointUrl), cosmosSource.AuthorizationKey);
        }

        public async Task Log(IApiLog request)
        {
            var t = _storageWriter.Log(request).ConfigureAwait(false);
            await CreateDocument(request).ConfigureAwait(false);
            await t;
        }


        private async Task CreateDocument(IApiLog log)
        {
            if (log.CompanyId == 0)
            {
                return;
            }

            log.Input = log.Input.Length.ToString();
            log.Output = log.Output.Length.ToString();

            try
            {
                await client.CreateDocumentAsync(UriFactory.CreateDocumentCollectionUri(cosmosSource.DatabaseId, cosmosSource.CollectionId), log).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                await cosmosErrorMessenger.SendToSlack(ex, $"CompanyId : {log.CompanyId}, RequestId : {log.RequestId}, Error: {ex.GetBaseException().Message}");
            }
            //client.Dispose();
        }


    }
}
