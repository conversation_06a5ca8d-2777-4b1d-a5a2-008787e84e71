﻿using Microsoft.Azure.WebJobs.Host;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using GamificationProcessor.Configuration;

namespace GamificationProcessor
{
    public class Program
    {
        public static async Task Main(string[] args)
        {
            var builder = new HostBuilder()
               .ConfigureAppConfiguration((config) =>
               {
                   var env = Environment.GetEnvironmentVariable("BuildEnvironment");
                   config.AddJsonFile($"appsettings.{env}.json", optional: true, reloadOnChange: true);
               })
               .ConfigureWebJobs(b =>
               {
                   b.AddAzureStorage(c =>
                   {
                       c.BatchSize =
#if DEBUG
                        1;
#else
                        5;
#endif
                       c.MaxPollingInterval = TimeSpan.FromSeconds(5);
                   });
               })
               .ConfigureLogging((context, b) =>
               {
               })
                .ConfigureServices((context, services) =>
                {
                    Dependencies.SetUp(services, context.Configuration);
                })
                .UseConsoleLifetime();
            var host = builder.Build();
            using (host)
            {
                await host.RunAsync();
            }
        }

    }
}
