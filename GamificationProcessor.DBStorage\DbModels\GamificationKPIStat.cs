﻿using Libraries.CommonEnums;
using System;
using System.Collections.Generic;
using System.Text;

namespace GamificationProcessor.DbStorage.DbModels
{
    public class GamificationKPIStat
    {
        public long Id { get; set; }
        public long? GSMId { get; set; }
        public long? NSMId { get; set; }
        public long ZSMId { get; set; }
        public long RSMId { get; set; }
        public long ASMId { get; set; }
        public long ESMId { get; set; }
        public long? ReportingManagerId { get; set; }
        public PortalUserRole UserRole { set; get; }
        public string Rank { get; set; }

        public long RegionId { get; set; }
        public long ZoneId { get; set; }
        public long DayStartDateKey { get; set; }
        public int DayStartWeek { get; set; }
        public int DayStartMonth { get; set; }

     public long GameId { get; set; }
     public long TeamId { get; set; }
     public long KPIId { get; set; }
     public bool IsQualified { get; set; }
     public bool IsQualifierKPI{ get; set; }
     public long Coins{ get; set; }
     public string Achievements{ get; set; }
     public long CompanyId { get; set; }
     public long? PositionLevel1 { get; set; }
     public long? PositionLevel2 { get; set; }
     public long? PositionLevel3 { get; set; }
     public long? PositionLevel4 { get; set; }
     public long? PositionLevel5 { get; set; }
     public long? PositionLevel6 { get; set; }
     public long? PositionLevel7 { get; set; }
     public long? PositionLevel8 { get; set; }
     public long? SlabId { get; set; }
    public long? GeographyLevel5 { get; set; }
    public long? GeographyLevel6 { get; set; }
    public long? GeographyLevel7 { get; set; }
    public long? Payout { get; set; }
    }

}
