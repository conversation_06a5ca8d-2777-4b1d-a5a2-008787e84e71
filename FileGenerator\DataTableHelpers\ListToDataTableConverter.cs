﻿using FastMember;
using System;
using System.Collections.Generic;
using System.Data;
using System.Text;

namespace FileGenerator.DataTableHelpers
{
    public class ListToDataTableConverter
    {
        
            public DataTable GetDataTableFromList<T>(IEnumerable<T> data, string tablename)
            {
                DataTable table = new DataTable
                {
                    TableName = tablename
                };
                using (var reader = ObjectReader.Create(data))
                {
                    table.Load(reader);

                }
                return table;
            }
        
    }
}
