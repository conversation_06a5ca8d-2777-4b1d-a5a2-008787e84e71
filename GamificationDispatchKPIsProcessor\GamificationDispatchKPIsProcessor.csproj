<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <!--TODO: Upgrade to .net8-->
    <TargetFramework>netcoreapp3.1</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <Folder Include="Configuration\" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Azure.WebJobs" Version="3.0.27" />
    <PackageReference Include="Microsoft.Azure.WebJobs.Extensions.Storage" Version="4.0.4" />
    <PackageReference Include="Microsoft.Azure.WebJobs.Logging.ApplicationInsights" Version="3.0.27" />
    <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="5.0.0" />
    <PackageReference Include="Microsoft.NETCore.App" Version="2.2.8" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\GamificationProcessor.Core\GamificationProcessor.Core.csproj" />
    <ProjectReference Include="..\GamificationProcessor.DBStorage\GamificationProcessor.DBStorage.csproj" />
    <ProjectReference Include="..\GamificationProcessot.Tests\GamificationProcessor.Tests.csproj" />
    <!--TODO: Use Libraries SubModule-->
    <ProjectReference Include="..\Library.AsyncLock\Library.AsyncLock.csproj" />
    <ProjectReference Include="..\Library.ConnectionStringParsor\Library.ConnectionStringParsor.csproj" />
    <ProjectReference Include="..\Library.ResiliencyHelpers\Library.ResiliencyHelpers.csproj" />
    <ProjectReference Include="..\Library.SlackService\Library.SlackService.csproj" />
  </ItemGroup>

</Project>
