﻿using Libraries.CommonEnums;
using System;
using System.Collections.Generic;
using System.Linq;

namespace FileGenerator.Interfaces
{
    public interface INomenclatureSpecifier
    {
        //Date: 28th Sep 2020
        //Link: https://app.asana.com/0/43051116721058/1195308828573106/f
        //Reason: Retrieving nomeclature from the nomeclature dictionary and not from mapped company
        CompanyNomenclatureSpecifier GetSpecifierForCompany(long companyId, bool getAll = false);
    }

    public class CompanyNomenclatureSpecifier
    {
        private readonly Dictionary<string, string> dictionary;

        public CompanyNomenclatureSpecifier(Dictionary<string, string> dictionary)
        {
            this.dictionary = dictionary;
        }
        public string GetHeaderName(string name)
        {
            return dictionary.ContainsKey(name) ? dictionary[name] : name;
        }
        public string GetUserNomeclature(PortalUserRole portalRole)
        {
            var roleDictionary = new Dictionary<PortalUserRole, string>
            {
                [PortalUserRole.ClientEmployee] = "ESM",
                [PortalUserRole.AreaSalesManager] = "ASM",
                [PortalUserRole.RegionalSalesManager] = "RSM",
                [PortalUserRole.ZonalSalesManager] = "ZSM",
                [PortalUserRole.NationalSalesManager] = "NSM",
                [PortalUserRole.GlobalSalesManager] = "GSM"
            };
            var name = roleDictionary.ContainsKey(portalRole) ? roleDictionary[portalRole] : "Unknown";
            return GetHeaderName(name);
        }
        public Dictionary<string, string> GetRankNomenclatureDictionary()
        {
            var roleItems = new List<string>
            {
                 "ESM",
                 "ASM",
                "RSM",
                 "ZSM",
                "NSM",
                "GSM"
            };
            return roleItems.Where(r => dictionary.ContainsKey(r)).ToDictionary(r => r, r => dictionary[r]);
        }

        public string GetNomenclatureForRank(string rankNo)
        {
            var roleDictionary = new Dictionary<string, string>
            {
                ["0"] = "ESM",
                ["1"] = "ASM",
                ["2"] = "RSM",
                ["3"] = "ZSM",
                ["4"] = "NSM",
                ["5"] = "GSM"
            };
            var name = roleDictionary.ContainsKey(rankNo) ? roleDictionary[rankNo] : "Unknown";
            return GetHeaderName(name);
        }
        public Dictionary<string,string> GetAllNomenClaturesForCompany()
        {
            return dictionary;
        }
    }
}