﻿using System;

namespace Library.StorageWriter
{
    public class OutletImageBlobReader : BlobReader
    {
        public OutletImageBlobReader(string masterStorageConnectionString) : base(masterStorageConnectionString, "outletimages")
        {


        }
    }

    public class ClientEmployeeImageBlobReader : B<PERSON>bReader
    {
        public ClientEmployeeImageBlobReader(string masterStorageConnectionString) : base(masterStorageConnectionString, "userprofilepicture")
        {


        }
    }
    public class InvoiceImageBlobReader : BlobReader
    {
        public InvoiceImageBlobReader(string masterStorageConnectionString) : base(masterStorageConnectionString, "invoiceimages")
        {


        }
    }

    public class SelfiImageBlobReader : BlobReader
    {
        public SelfiImageBlobReader(string masterStorageConnectionString) : base(masterStorageConnectionString, "daystartimage")
        {


        }

        public string GetSelfiPath(string filename)
        {
            try
            {
                var path = _container.Uri.AbsoluteUri + "/" + filename;
                return !String.IsNullOrWhiteSpace(filename) ? path : null;
            }
            catch(Exception e)
            {
                return null;
            }
        }
    }
}
